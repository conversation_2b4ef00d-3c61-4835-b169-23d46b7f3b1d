import type { ProjectSchema } from '@shape-construction/api/src/types';
import type { TabOption } from '../../src/app/pages/projects/[projectId]/issues/components/IssueList/issue-tab-options';
import ExportFile from './IssuesList/ExportFile';
import Grouping from './IssuesList/Grouping';
import GroupingSelection from './IssuesList/GroupingSelection';
import SortingSelectionPage from './IssuesList/SortingSelectionPage';
import Search from './NavBar/Search';

class IssuesListPage {
  projectId: ProjectSchema['id'] | undefined;

  constructor(projectId?: ProjectSchema['id']) {
    this.projectId = projectId;
  }

  visit(options?: Cypress.VisitAuthenticatedOptions) {
    cy.visitAuthenticated(`/projects/${this.projectId}/issues`, options);

    return this;
  }

  get root() {
    return cy.get('[data-testid="res-main-box"]');
  }

  get search() {
    return new Search();
  }

  get issues() {
    return this.root.findAllByTestId(/issue-card-/);
  }

  // @ts-expect-error ts-migrate(7006) FIXME: Parameter 'title' implicitly has an 'any' type.
  getIssue(title, group?: string) {
    if (!group) return this.issues.contains(title);

    return this.groupWithName(group).issues.contains(title);
  }

  get loadMoreButton() {
    return this.root.contains(/load more/i);
  }

  get tabs() {
    return this.root.findByRole('tablist');
  }

  tabSelected(name: string | RegExp) {
    return this.root.findByRole('tab', { name, selected: true });
  }

  /**
   * "Find the tab content element with the given name."
   *
   * The function takes a name parameter, which is the name of the tab content element we want to find
   * @param {string} name - The name of the tab. Examples: "I'm Responsible", "All"
   * @returns The tab content element with the name passed in.
   */
  tabsContent(tabId: TabOption) {
    return this.root.find(`[data-cy="selected-tab-${tabId}"]`);
  }

  get exportFile() {
    return new ExportFile();
  }

  groupWithName(name: string | RegExp) {
    return new Grouping(this.root.contains('[data-cy="group"]', name));
  }

  get groups() {
    return this.root.find('[data-cy="group"]');
  }

  get groupingButtons() {
    return this.root.find('[data-cy^="group-collapse-depth-"]');
  }

  get groupingSelection() {
    return new GroupingSelection(this.root);
  }

  get sortingSelection() {
    return new SortingSelectionPage(this.root);
  }

  get loadingSkeleton() {
    return this.root.find('[data-cy="issues-loading"]');
  }

  get emptyState() {
    return this.root.find('[data-cy="issues-empty-state"]');
  }

  get newIssueMenu() {
    return cy.findByRole('button', { name: 'New issue' });
  }

  get newIssueCompleteOption() {
    return cy.findByRole('menuitem', { name: 'Complete' });
  }

  get newIssueQuickOption() {
    return cy.findByRole('menuitem', { name: 'Quick' });
  }

  get saveViewOptions() {
    return this.root.find('[data-cy="issues-toolbar"]').findByRole('button', { name: 'Save' });
  }

  waitIssuesLoad() {
    this.loadingSkeleton.should('be.visible');
    this.loadingSkeleton.should('not.exist');
  }

  openTab(tabOption: string, waitForLoading = true) {
    this.tabs.contains(new RegExp(tabOption, 'i')).click();
    if (waitForLoading) this.waitIssuesLoad();
  }
}

export default IssuesListPage;
