class FeedbackForm {
  get modalHeader() {
    return cy.get('[data-testid="modal-header"]');
  }

  get modalTitle() {
    return this.modalHeader.contains(/Send feedback/);
  }

  get modal() {
    return this.modalTitle.closest('[role="dialog"]');
  }

  get likeTextarea() {
    return this.modal.find('textarea[name=likeText]');
  }

  get improvementTextarea() {
    return this.modal.find('textarea[name=improvementText]');
  }

  get submitButton() {
    return this.modal.find('[data-cy="submit-feedback"]');
  }

  get successMessage() {
    return cy.contains(/Thanks. Feedback submitted/);
  }
}

export default FeedbackForm;
