import type { TextMatch } from '../PageObjects';

class AssignmentRejectWorkflow {
  get issueActionsSection() {
    return cy.get('[data-cy="issue-state-actions"]');
  }

  get modalTitle() {
    return cy.contains(/Assign responsible person/);
  }

  get workflowModal() {
    return this.modalTitle.closest('[role="dialog"]');
  }

  get reassignButton() {
    return this.issueActionsSection.contains(/Reassign/);
  }

  get assignButton() {
    return this.workflowModal.contains('button', /Assign/);
  }

  get skipButton() {
    return this.workflowModal.contains('button', /Skip/);
  }

  selectUserOption(user: TextMatch) {
    return this.workflowModal.contains('[role="radio"]', user).click();
  }
}

export default AssignmentRejectWorkflow;
