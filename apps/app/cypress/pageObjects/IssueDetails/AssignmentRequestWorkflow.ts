import type { TextMatch } from '../PageObjects';

class AssignmentRequestWorkflow {
  get issueActionsSection() {
    return cy.get('[data-cy="issue-state-actions"]');
  }

  get rejectButton() {
    return this.issueActionsSection.findByRole('button', { name: 'Reject' });
  }

  get confirmRejectModal() {
    return cy.findByRole('dialog');
  }

  get confirmRejectTitle() {
    return this.confirmRejectModal.findByRole('heading', { name: /Reject assignment/ });
  }

  get confirmRejectSubtitle() {
    return this.confirmRejectModal.contains(/Are you sure you want to reject this assignment/);
  }

  get modalRejectButton() {
    return this.confirmRejectModal.findByRole('button', { name: /Reject/ });
  }

  get modalReassignTitle() {
    return cy.contains(/Assign responsible person/);
  }

  get reassignModal() {
    return this.modalReassignTitle.parent().parent();
  }

  get assignButton() {
    return this.reassignModal.findByRole('button', { name: 'Assign' });
  }

  get modalRejectSkipButton() {
    return this.reassignModal.findByRole('button', { name: 'Skip' });
  }

  get acceptButton() {
    return this.issueActionsSection.findByRole('button', { name: 'Accept' });
  }

  get confirmAcceptModal() {
    return cy.findByRole('dialog');
  }

  get confirmAcceptTitle() {
    return this.confirmAcceptModal.findByRole('heading', { name: /Accept assignment/ });
  }

  get confirmAcceptSubtitle() {
    return this.confirmAcceptModal.contains(/Are you sure you want to accept this assignment/);
  }

  get confirmAcceptButton() {
    return this.confirmAcceptModal.findByRole('button', { name: /Accept/ });
  }

  get acceptModal() {
    return cy.contains(/Add a planned closure date/).closest('[role="dialog"]');
  }

  get plannedClosureDateInput() {
    return this.acceptModal.get('input[name=plannedClosureDate]');
  }

  get plannedClosureTimeInput() {
    return this.acceptModal.get('input[name=plannedClosureTime]');
  }

  get modalAcceptSubmitButton() {
    return this.acceptModal.findByRole('button', { name: 'Submit' });
  }

  get acceptModalSkipButton() {
    return this.acceptModal.findByRole('button', { name: 'Skip' });
  }

  get successMessage() {
    return cy.contains(/Issue accepted/);
  }

  selectUserOption(user: TextMatch) {
    return cy.contains('[role="radio"]', user).should('be.visible').click();
  }
}

export default AssignmentRequestWorkflow;
