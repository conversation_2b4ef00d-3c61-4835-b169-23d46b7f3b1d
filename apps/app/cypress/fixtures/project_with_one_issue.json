{"project": {"id": "project-one", "currentTeamId": 1001, "currentTeamMemberId": 1001, "currentTeamMemberRole": "viewer", "currentTeamMemberStatus": "joined", "rootLocationId": "root-location", "logoUrl": "/uploads/image.jpeg", "shortName": "PROJ", "timezone": "Europe/London", "title": "Test Project", "availableActions": {"createIssue": true, "createShiftReport": true, "inviteTeam": true, "manageLocations": true, "uploadDocument": true}}, "user": {"id": "user-one", "avatarUrl": "/uploads/image.jpeg", "defaultProject": "project-one", "email": "<EMAIL>", "emailConfirmed": true, "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "name": "<PERSON>", "pendingEuaAcceptance": false, "signupDate": "2021-11-01", "timezone": "Europe/London"}, "team": {"id": 1001, "displayName": "Team one", "projectId": "project-one", "org": null, "availableActions": {"edit": true, "inviteUser": true, "delete": true}}, "teamMember": {"id": "1001", "user": {"id": "user-one", "avatarUrl": "/uploads/image.jpeg", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "name": "<PERSON>"}, "team": {"id": 1001, "displayName": "OON"}, "invite": null, "status": "joined", "role": "viewer", "constructionRole": null, "projectId": "project-one", "availableActions": {"edit": true, "archive": true}}, "issue": {"id": "issue-one", "archived": false, "assignedTeamMemberId": 1001, "category": "safety", "closedAt": null, "createdAt": "2021-12-21T18:04:39", "critical": false, "currentState": "in_progress", "delayFinish": "2021-12-21T18:04:39", "delayStart": "2021-12-21T18:04:39", "description": "This is the issue number one", "disciplineId": null, "draftAssigneeId": null, "dueDate": null, "immediateAction": null, "impact": "liveDelay", "lastVisitedAt": null, "locationId": "root-location", "nextActionerId": null, "observedAt": "2021-12-21T18:04:39", "observerId": 1001, "originatorId": 1001, "overdue": false, "peopleInvolvedSafety": null, "plannedClosureDate": null, "potentialImpactSeverity": null, "preventativeAction": null, "projectId": "project-one", "referenceNumber": "INO", "safetyAlert": false, "safetyLikelihoodScore": null, "subCategory": "hazard", "title": "Issue number one", "unreadUpdatesCount": 0, "updatedAt": "2021-12-21T18:04:39", "updates": {"lastVisitedAt": "2021-12-21T18:04:39", "unreadUpdatesCount": {"public": 0, "team": 0}}, "visibilityStatus": "projectWide", "workAffected": "Left window", "customFields": [], "observerTeamId": 1001, "observerUser": {"id": "user-one", "avatarUrl": "/uploads/image.jpeg", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "name": "<PERSON>"}, "assignedTeamId": 1001, "assignedTeamName": "Team one", "assignedUser": {"id": "user-one", "avatarUrl": "/uploads/image.jpeg", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "name": "<PERSON>"}, "issueAssignment": null, "nextActionerUser": {"id": "user-one", "avatarUrl": "/uploads/image.jpeg", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "name": "<PERSON>"}, "involvedTeams": [{"id": "involved-team-one", "role": "originator", "teamId": 1001, "displayName": "Team one"}], "approvers": [{"id": "user-one", "teamMemberId": 1001, "sortOrder": 0, "approvedAt": null, "name": "<PERSON>", "teamDisplayName": "Team one", "teamId": 1001, "status": "pending", "user": {"id": "user-one", "avatarUrl": "/uploads/image.jpeg", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "name": "<PERSON>"}, "team": {"id": 1001, "displayName": "Team one"}}], "isWatching": true, "activityLevel": "stale", "availableActions": {"acceptAssignment": true, "approve": true, "archive": true, "assign": true, "complete": true, "edit": true, "reject": false, "reopen": false, "restore": false, "start": true, "stop": false, "upload": true}}, "filter": {"custom": false, "deletedAt": null, "filterProperties": {"filterState": ["assigned", "assignment_rejected", "assignment_requested", "in_progress"]}, "id": 1, "parentId": null, "projectId": null, "title": "All open issues", "userId": null}, "groupCount": {"groupCollection": {"group": "state", "groupEntities": [{"identifier": "in_progress", "totalCount": 1}], "groups": ["state"], "totalCount": 1}}, "customFields": {"availableActions": {"createForProject": false, "createForTeam": false}, "customFields": [], "meta": {"maximumProjectWideCustomFields": 0, "maximumTeamCustomFields": 0}}}