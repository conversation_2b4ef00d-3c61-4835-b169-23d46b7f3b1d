{"joinedProjects": [{"id": "project-0", "timezone": "Europe/London", "currentTeamMemberRole": "contributor", "currentTeamMemberStatus": "joined", "currentTeamId": "6265f42f-961f-4ba7-908b-470660e5be84", "currentTeamMemberId": "1ab42bd2-c2be-410d-a551-6e4258dba1cd", "rootLocationId": "be1ecc9a-8d2a-4615-80c2-d2888649b7e9", "logoUrl": null, "title": "project-0", "shortName": "PRO0", "availableActions": {"createIssue": true, "createShiftReport": true, "inviteTeam": true, "manageLocations": true, "uploadDocument": true}, "channels": {"streamChatTeam": "project:123"}}, {"id": "project-1", "timezone": "Europe/London", "currentTeamMemberRole": "contributor", "currentTeamMemberStatus": "joined", "currentTeamId": "6dbf071a-a9a8-4546-a144-a3fd1f1f7fe3", "currentTeamMemberId": "317c8973-587d-4b40-9cd1-18a9ea91ecda", "rootLocationId": "e3d2b6fc-b405-4065-bcdb-08833deaeadc", "logoUrl": null, "title": "project-1", "shortName": "PRO1", "availableActions": {"createIssue": true, "createShiftReport": true, "inviteTeam": true, "manageLocations": true, "uploadDocument": true}, "channels": {"streamChatTeam": "project:123"}}, {"id": "project-2", "timezone": "Europe/London", "currentTeamMemberRole": "contributor", "currentTeamMemberStatus": "joined", "currentTeamId": "b7103576-58af-4ace-b973-e37d4c1e7deb", "currentTeamMemberId": "ec93ec13-1d8d-4c6a-9601-648c23ab206d", "rootLocationId": "bb4ec0ee-9a4d-4430-848e-62d8f0d3b3ae", "logoUrl": null, "title": "project-2", "shortName": "PRO2", "availableActions": {"createIssue": true, "createShiftReport": true, "inviteTeam": true, "manageLocations": true, "uploadDocument": true}, "channels": {"streamChatTeam": "project:123"}}], "teamMember": {"id": "06559c8e-db08-41a5-b39b-ebb131384bc9", "user": {"id": "user-one", "name": "Team member undefined", "avatarUrl": null, "firstName": null, "lastName": null}, "team": {"id": "7c4376d8-a402-4a35-8305-c30b030b7885", "displayName": "team display name"}, "invite": {"id": "753492c9-a1e6-4669-b016-33bb384f5f66", "availableActions": {"resendEmail": false, "destroy": false}}, "status": "joined", "role": "contributor", "constructionRole": null, "projectId": "project-0", "availableActions": {"edit": false, "archive": false}}}