import type { UserSchema } from '@shape-construction/api/src/types';
// @ts-expect-error ts-migrate(7016) FIXME: Could not find a declaration file for module 'chai... Remove this comment to see the full error message
import chaiSorted from 'chai-sorted';
import './commands';
import { postApiUsersMockHandler } from '@shape-construction/api/handlers-factories/channels';

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * Custom command to select DOM element by data-cy attribute.
       * @example cy.dataCy('greeting')
       */
      clearLocalForage(): Chainable<AUTWindow>;
      logout(): Chainable<AUTWindow>;
      visitAuthenticated(url: string, options?: VisitAuthenticatedOptions): Chainable<AUTWindow>;
    }

    type VisitAuthenticatedOptions = Partial<VisitOptions> & { mockedUser?: UserSchema };
  }
}

beforeEach(() => {
  cy.clearLocalForage();

  cy.setCookie(
    'tracking-preferences',
    JSON.stringify({
      preferences: false,
      statistics: true,
      marketing: false,
    }),
    {
      path: '/',
    }
  );

  // We are not testing the Stream Chat API, so we mock the token endpoint
  cy.intercept('POST', '/api/channels/token', postApiUsersMockHandler());
});

afterEach(() => {
  if (window.navigator && navigator.serviceWorker) {
    navigator.serviceWorker.getRegistrations().then((registrations) => {
      registrations.forEach((registration) => {
        registration.unregister();
      });
    });
  }

  cy.logout();
});

chai.use(chaiSorted);
