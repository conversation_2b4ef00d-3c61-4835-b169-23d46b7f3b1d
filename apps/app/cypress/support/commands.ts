import 'cypress-audit/commands';
import 'cypress-file-upload';
import '@testing-library/cypress/add-commands';
import { environment } from 'app/config/environment';

const resizeObserverLoopErrRe = /^[^(ResizeObserver loop limit exceeded)]/;
const generateJwt = (minutes: number) => {
  const now = Math.floor(Date.now() / 1_000);
  return `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.${btoa(
    JSON.stringify({
      exp: now + minutes * 60,
      iat: now,
    })
  )}.mockSignature`;
};

Cypress.on('uncaught:exception', (err) => {
  /* returning false here prevents <PERSON><PERSON> from failing the test */
  if (resizeObserverLoopErrRe.test(err.message)) return false;

  return true;
});

Cypress.Commands.add('clearLocalForage', () => {
  cy.window().then((win) => {
    win.indexedDB.deleteDatabase(environment.LOCAL_PERSISTER_KEY);
    win.indexedDB.deleteDatabase(environment.REDUX_PERSISTER_KEY);
    win.indexedDB.deleteDatabase(environment.REACT_QUERY_PERSISTER_KEY);
  });
});

Cypress.Commands.add(
  'visitAuthenticated',
  (
    url,
    options = {
      auth: {
        username: Cypress.env('LOGIN'),
        password: Cypress.env('PASSWORD'),
      },
    }
  ) => {
    const { auth, mockedUser } = options;

    if (mockedUser) {
      cy.intercept('GET', '/api/users/me', mockedUser);
      window.localStorage.setItem('authorization', generateJwt(5));
      window.localStorage.setItem('authorization-refresh', 'PlaceholderRefreshToken');
      cy.visit(url);
    } else {
      cy.request('POST', `${Cypress.env('apiBaseURL')}/api/login`, {
        user: {
          email: auth?.username,
          password: auth?.password,
        },
      }).then((response) => {
        const token = response.headers.authorization as string;
        const refreshToken = response.headers['authorization-refresh'] as string;
        window.localStorage.setItem('authorization', token);
        window.localStorage.setItem('authorization-refresh', refreshToken);
        cy.visit(url);
      });
    }
  }
);

Cypress.Commands.add('logout', () => {
  window.localStorage.removeItem('authorization');
});
