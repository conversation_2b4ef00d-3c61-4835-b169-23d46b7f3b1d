import UtilsStats from 'app/components/Utils/UtilsStats';
import HomePage from '../pageObjects/Homepage';
import IssueDetailsPage from '../pageObjects/IssueDetailsPage';
import UnpublishedIssuesListPage from '../pageObjects/IssuesList/UnpublishedIssuesListPage';
import IssuesListPage from '../pageObjects/IssuesListPage';
import Navbar from '../pageObjects/Navbar';
import QuickCapture from '../pageObjects/QuickCapture';
import { goOffline, goOnline } from '../support/network';

const auth = {
  username: Cypress.env('LOGIN'),
  password: Cypress.env('PASSWORD'),
};

describe('Create a new issue', () => {
  it('allows creating quick captures', () => {
    new HomePage().visit({ auth }).projectsWidget.projectWithName('Shape').click();

    const navbar = new Navbar();
    navbar.newIssueMenu.click();
    navbar.newIssueQuickOption.click();

    const quickCapture = new QuickCapture();
    const issueTitle = `A broken window ${UtilsStats.uuidv4()}`;
    quickCapture.titleInput.type(issueTitle);
    quickCapture.createButton.click();

    cy.url().should('contains', '/issues/lists/my-issues');

    const issuesListPage = new IssuesListPage();
    issuesListPage.issues.contains(issueTitle);
  });

  it('allows creating quick captures with images', () => {
    new HomePage().visit({ auth }).projectsWidget.projectWithName('Shape').click();

    const navbar = new Navbar();
    navbar.newIssueMenu.click();
    navbar.newIssueQuickOption.click();

    const quickCapture = new QuickCapture();
    const issueTitle = `A broken window ${UtilsStats.uuidv4()}`;

    quickCapture.titleInput.type(issueTitle);
    quickCapture.uploadFromEmptyState('image.jpeg');
    quickCapture.uploadFromGallery('image2.jpeg');
    quickCapture.createButton.click();

    cy.url().should('contains', '/issues/lists/my-issues');

    const issuesListPage = new IssuesListPage();
    issuesListPage.issues.contains(issueTitle).click();

    const issueDetailsPage = new IssueDetailsPage();
    issueDetailsPage.activity.tab('Gallery').click();
    issueDetailsPage.activity.tabPanel('Gallery').find('figure').should('have.length', 2);
  });

  it('allows creating multiple quick captures in batch', () => {
    new HomePage().visit({ auth }).projectsWidget.projectWithName('Shape').click();

    const navbar = new Navbar();
    navbar.newIssueMenu.click();
    navbar.newIssueQuickOption.click();

    const quickCapture = new QuickCapture();
    const issue1Title = `A broken window ${UtilsStats.uuidv4()}`;
    const issue2Title = `A broken door ${UtilsStats.uuidv4()}`;
    const issue3Title = `A broken pipe ${UtilsStats.uuidv4()}`;

    quickCapture.titleInput.type(issue1Title);
    quickCapture.createAnother.click();
    quickCapture.createButton.click();
    quickCapture.titleInput.should('have.value', '');

    quickCapture.titleInput.type(issue2Title);
    quickCapture.createAnother.click();
    quickCapture.createButton.click();
    quickCapture.titleInput.should('have.value', '');

    quickCapture.titleInput.type(issue3Title);
    quickCapture.createButton.click();

    cy.url().should('contains', '/issues/lists/my-issues');

    const issuesListPage = new IssuesListPage();
    issuesListPage.issues.contains(issue1Title);
    issuesListPage.issues.contains(issue2Title);
    issuesListPage.issues.contains(issue3Title);
  });

  describe('when user is offline', () => {
    beforeEach(goOnline);
    afterEach(goOnline);

    it('supports creating quick captures offline and syncs them when back online', () => {
      new HomePage().visit({ auth }).projectsWidget.projectWithName('Shape').click();
      const issuesListPage = new IssuesListPage();

      issuesListPage.loadingSkeleton.should('be.visible');
      issuesListPage.loadingSkeleton.should('not.exist');

      const navbar = new Navbar();
      navbar.newIssueMenu.click();
      navbar.newIssueQuickOption.click();

      goOffline();

      const quickCapture = new QuickCapture();
      const issue1Title = `A broken window ${UtilsStats.uuidv4()}`;
      const issue2Title = `A broken door ${UtilsStats.uuidv4()}`;

      quickCapture.titleInput.type(issue1Title);
      quickCapture.createAnother.click();
      quickCapture.createButton.click();
      quickCapture.titleInput.should('have.value', '');

      quickCapture.titleInput.type(issue2Title);
      quickCapture.createButton.click();

      quickCapture.offlineToastList.should('have.length', 2);
      cy.url().should('contains', '/issues/lists/unpublished');

      const unPublishedIssuesListPage = new UnpublishedIssuesListPage();
      unPublishedIssuesListPage.localDrafts.should('have.length', 2);

      const firstUnsynced = unPublishedIssuesListPage.localDraft(issue1Title).root;
      expect(firstUnsynced.should('contain.text', issue1Title));
      expect(firstUnsynced.should('contain.text', 'Created:N/A'));
      expect(firstUnsynced.should('contain.text', 'Updated:N/A'));

      const secondUnsynced = unPublishedIssuesListPage.localDraft(issue2Title).root;
      expect(secondUnsynced.should('contain.text', issue2Title));
      expect(secondUnsynced.should('contain.text', 'Created:N/A'));
      expect(secondUnsynced.should('contain.text', 'Updated:N/A'));

      goOnline();

      cy.url().should('contains', '/issues/lists/my-issues');

      issuesListPage.issues.contains(issue1Title);
      issuesListPage.issues.contains(issue2Title);
    });

    it('supports creating quick captures offline and syncs them when back online after refresh page', () => {
      new HomePage().visit({ auth }).projectsWidget.projectWithName('Shape').click();
      const issuesListPage = new IssuesListPage();

      issuesListPage.loadingSkeleton.should('be.visible');
      issuesListPage.loadingSkeleton.should('not.exist');

      const navbar = new Navbar();
      navbar.newIssueMenu.click();
      navbar.newIssueQuickOption.click();

      goOffline();

      const quickCapture = new QuickCapture();
      const issue1Title = `A broken window ${UtilsStats.uuidv4()}`;

      quickCapture.titleInput.type(issue1Title);
      quickCapture.uploadFromEmptyState('image.jpeg');
      quickCapture.uploadFromGallery('image2.jpeg');
      quickCapture.createButton.click();

      quickCapture.offlineToast.should('be.visible');
      cy.url().should('contains', '/issues/lists/unpublished');

      const unPublishedIssuesListPage = new UnpublishedIssuesListPage();
      unPublishedIssuesListPage.localDrafts.should('have.length', 1);

      const firstUnsynced = unPublishedIssuesListPage.localDraft(issue1Title).root;
      expect(firstUnsynced.should('contain.text', issue1Title));
      expect(firstUnsynced.should('contain.text', 'Created:N/A'));
      expect(firstUnsynced.should('contain.text', 'Updated:N/A'));

      cy.reload();
      goOnline();

      issuesListPage.openTab('My issues', false);

      cy.url().should('contains', '/issues/lists/my-issues');

      issuesListPage.issues.contains(issue1Title);
    });
  });
});
