import { issueFactory } from '@shape-construction/api/factories/issues';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { subscriptionPlanAvailableActionsFactory } from '@shape-construction/api/factories/subscriptionPlans';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { teamFactory } from '@shape-construction/api/factories/teams';
import { userBasicDetailsFactory, userFactory } from '@shape-construction/api/factories/users';
import { factoryList } from '@shape-construction/api/factories/utils';
import type { CustomFieldListSchema } from '@shape-construction/api/src/types';
import ArchivedProjects from '../../pageObjects/Homepage/ArchivedProjects';
import CurrentProjects from '../../pageObjects/Homepage/CurrentProjects';
import IssuesListPage from '../../pageObjects/IssuesListPage';
import LandingPage from '../../pageObjects/Landing';
import ProjectsSelector from '../../pageObjects/ProjectsSelector';
import SideBar from '../../pageObjects/SideBar';

describe('Archived project', () => {
  const userBasicDetails = userBasicDetailsFactory();
  const user = userFactory({
    ...userBasicDetails,
    defaultProject: null, // so we enter directly on the /my-projects page
  });

  beforeEach(() => {
    cy.intercept('POST', '/api/login', user);
    cy.intercept('POST', '/api/push_subscriptions', {});
    cy.intercept('POST', '/api/channels/token', {});
    cy.intercept('GET', '/api/feature_flags*', {});
    cy.intercept('GET', '/api/projects/project-one/disciplines', []);
    cy.intercept('GET', '/api/time_zones', { fixture: 'time_zones' });
    cy.intercept('GET', '/api/notifications/overview', { total: 0, totalUnread: 0 });
    cy.intercept('GET', '/api/projects/*/**', []);
    cy.intercept('GET', '/api/projects/project-*/people', []);
    cy.intercept('GET', '/api/product_tours/issues*', {
      statusCode: 400,
    });
    cy.intercept('GET', '/api/projects/project-*/issues/issue-*/feed/public*', {
      entries: [],
      meta: {
        firstEntryCursor: null,
        hasNextPage: false,
        hasPreviousPage: false,
        lastEntryCursor: null,
        total: 0,
      },
    });
    cy.intercept('GET', '/api/projects/project-*/issues/issue-*/feed/team*', {
      entries: [],
      meta: {
        firstEntryCursor: null,
        hasNextPage: false,
        hasPreviousPage: false,
        lastEntryCursor: null,
        total: 0,
      },
    });
  });

  describe('when user has archived project', () => {
    it('does not see project on project selector', () => {
      const projects = factoryList(projectFactory, 3, (index) => ({
        id: `project-${index + 1}`,
        archived: index === 2,
        currentTeamMemberId: 5,
        title: `Project ${index + 1}`,
        shortName: `P-${index + 1}`,
      }));
      const team = teamFactory({ projectId: 'project-1' });
      const teamMember = teamMemberFactory({
        id: 5,
        user: userBasicDetails,
        team,
        role: 'admin',
      });
      const issue = issueFactory({ id: 'issue-1', projectId: 'project-1' });
      cy.intercept('GET', '/api/users/me', user);
      cy.intercept('GET', '/api/projects', projects);
      cy.intercept('GET', '/api/projects/project-1', projects[0]);
      cy.intercept('POST', '/api/projects/project-1/default', projects[0]).as('setDefaultProject');
      cy.intercept('GET', '/api/projects/project-1/custom_fields', {
        customFields: [],
        meta: {
          maximumProjectWideCustomFields: 3,
          maximumTeamCustomFields: 3,
        },
        availableActions: {
          createForProject: true,
          createForTeam: true,
        },
      } as CustomFieldListSchema);
      cy.intercept('GET', '/api/projects/project-1/teams', [team]);
      cy.intercept('GET', '/api/projects/project-1/people', [teamMember]);
      cy.intercept('GET', '/api/projects/project-1/issues*', {
        issues: [issue],
        meta: {
          currentPage: 1,
          pageSize: 10,
          totalEntries: 1,
          totalPages: 1,
        },
      });
      cy.intercept('GET', '/api/projects/project-1/issues/issue-1', [issue]);
      cy.intercept('GET', '/api/projects/project-1/teams/*/subscription_plan', {
        name: 'Pro',
        features: {
          issuePrivateChat: {
            available: true,
          },
        },
        availableActions: subscriptionPlanAvailableActionsFactory({
          accessBillingPortal: false,
          billingPortal: false,
          edit: false,
        }),
      });
      cy.intercept('/uploads/image.jpeg', { fixture: 'image.jpeg' });

      const landingPage = new LandingPage().visit({ mockedUser: user });
      landingPage.myProjectsTitle.should('be.visible');
      const currentProjects = new CurrentProjects();
      currentProjects.projects.should('have.length', 2);

      landingPage.archivedTab.click();
      const archivedProjects = new ArchivedProjects();
      archivedProjects.projects.should('have.length', 1);

      landingPage.projectsTab.click();
      currentProjects.projects.first().click();
      cy.wait('@setDefaultProject').then(() => {
        // We need this to avoid an infinite loop
        cy.intercept('GET', '/api/users/me', { ...user, defaultProject: 'project-1' });
      });
      const issuesListPage = new IssuesListPage();
      issuesListPage.waitIssuesLoad();

      new SideBar().projectSelectorButton.click();
      const projectsSelector = new ProjectsSelector();
      projectsSelector.projects.should('be.visible');
      projectsSelector.projects.should('contain', 'Project 1');
      projectsSelector.projects.should('contain', 'Project 2');
      projectsSelector.projects.should('not.contain', 'Project 3');
    });
  });
});
