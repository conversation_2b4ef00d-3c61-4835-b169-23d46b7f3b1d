import { projectFactory } from '@shape-construction/api/factories/projects';
import { subscriptionPlanFactory } from '@shape-construction/api/factories/subscriptionPlans';
import { userFactory } from '@shape-construction/api/factories/users';
import { factoryList } from '@shape-construction/api/factories/utils';
import { getApiProductToursIssuesMockHandler } from '@shape-construction/api/handlers-factories/product-tours';
import type { CustomFieldListSchema, UserSchema } from '@shape-construction/api/src/types';
import LandingPage from '../../pageObjects/Landing';

describe('Landing page for the auth user', () => {
  beforeEach(() => {
    cy.intercept('GET', '/api/projects/project-*/custom_fields', {
      customFields: [],
      meta: {
        maximumProjectWideCustomFields: 3,
        maximumTeamCustomFields: 3,
      },
      availableActions: {
        createForProject: true,
        createForTeam: true,
      },
    } as CustomFieldListSchema);
    cy.intercept('GET', '/api/product_tours/timeline', getApiProductToursIssuesMockHandler());
    cy.intercept('GET', '/api/projects/project-*/people*', []);
    cy.intercept('POST', '/api/projects/project-*/default', {});
    cy.intercept('GET', '/api/projects/project-*/events*', { events: [], hasMore: false });
    cy.intercept('GET', '/api/projects/project-*/locations', []);
    cy.intercept('GET', '/api/projects/project-*/teams', []);
    cy.intercept('GET', '/api/projects/project-*/teams/*/subscription_plan', subscriptionPlanFactory());
    cy.intercept('GET', '/api/time_zones', { fixture: 'time_zones' });
    cy.intercept('GET', '/api/notifications/overview', { total: 0, totalUnread: 0 });
    cy.intercept('POST', '/api/push_subscriptions', {});
    cy.intercept('GET', '/api/feature_flags*', {});
  });

  describe('when have projects', () => {
    describe('use the last visited project', () => {
      let mockedUser: UserSchema;

      beforeEach(() => {
        mockedUser = userFactory({
          id: 'user-one',
          defaultProject: 'project-2',
          email: '<EMAIL>',
          emailConfirmed: true,
          firstName: 'John',
          lastName: 'Doe',
          name: 'John Doe',
          avatarUrl: 'https://placehold.co/600x400',
        });
        const joinedProjects = factoryList(projectFactory, 3, (index) => ({
          id: `project-${index}`,
          title: `project-${index}`,
          currentTeamMemberStatus: 'joined' as const,
        }));
        cy.intercept('GET', '/api/projects', joinedProjects);
        cy.intercept('GET', '/api/projects/project-2', joinedProjects[2]);
      });

      it('redirects to the timeline page', () => {
        const landingPage = new LandingPage().visit({ mockedUser });
        cy.url().should('contain', '/projects/project-2/timeline');
        landingPage.timelineTitle.should('contain', 'Timeline');
      });
    });
  });

  describe('when does not have projects', () => {
    let mockedUser: UserSchema;

    beforeEach(() => {
      mockedUser = userFactory({
        id: 'user-one',
        defaultProject: null,
        email: '<EMAIL>',
        emailConfirmed: true,
        firstName: 'John',
        lastName: 'Doe',
        name: 'John Doe',
        avatarUrl: 'https://placehold.co/600x400',
      });
      cy.intercept('GET', '/api/projects', []);
    });

    describe('allow user to view projects page', () => {
      it('redirects the user to /my-projects', () => {
        const landingPage = new LandingPage().visit({ mockedUser });

        cy.url().should('contain', '/my-projects');
        landingPage.myProjectsTitle.should('contain', 'My Projects');
        landingPage.currentProjectsList.noCurrentProjects.should('be.visible');
      });
    });
  });
});
