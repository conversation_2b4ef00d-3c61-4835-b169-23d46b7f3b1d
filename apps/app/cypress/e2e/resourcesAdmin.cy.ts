import UtilsStats from 'app/components/Utils/UtilsStats';
import AdminResources from '../pageObjects/Admin/AdminResources';
import NewResourceModal from '../pageObjects/Admin/NewResourceModal';
import HomePage from '../pageObjects/Homepage';
import IssuesListPage from '../pageObjects/IssuesListPage';
import EditShiftReportPage from '../pageObjects/ShiftReports/EditShiftReportPage';
import ShiftReportsPage from '../pageObjects/ShiftReports/ShiftReportsPage';
import SideBar from '../pageObjects/SideBar';

const auth = {
  username: '<EMAIL>',
  password: 'Password123!',
};

describe('Resources Admin', () => {
  it('creates a resource and checks its presence on the shift report form', () => {
    const sidebar = new SideBar();
    const adminResources = new AdminResources();
    const newResourceModal = new NewResourceModal();
    const shiftReportsPage = new ShiftReportsPage();
    const editShiftReportPage = new EditShiftReportPage();

    new HomePage().visit({ auth }).projectsWidget.projectWithName('Shift Reports 1').click();
    const issuesListPage = new IssuesListPage();
    issuesListPage.waitIssuesLoad();
    sidebar.adminSubmenuTrigger.click();
    sidebar.resourcesLink.click();

    const person = `example of a person ${UtilsStats.uuidv4()}`;
    const organisation = `example of an organisation ${UtilsStats.uuidv4()}`;
    const role = `example of a role ${UtilsStats.uuidv4()}`;
    const equipment = `example of an equipment ${UtilsStats.uuidv4()}`;
    const material = `example of a material ${UtilsStats.uuidv4()}`;

    adminResources.newPeopleButton.click();
    newResourceModal.titleInput.type(person);
    newResourceModal.createButton.click();
    adminResources.selectResourceTableRow(person).should('be.visible');

    adminResources.tab('Organisations').click();
    adminResources.newOrganisationsButton.click();
    newResourceModal.titleInput.type(organisation);
    newResourceModal.createButton.click();
    adminResources.selectResourceTableRow(organisation).should('be.visible');

    adminResources.tab('Roles').click();
    adminResources.newRolesButton.click();
    newResourceModal.titleInput.type(role);
    newResourceModal.createButton.click();
    adminResources.selectResourceTableRow(role).should('be.visible');

    adminResources.tab('Equipment').click();
    adminResources.newEquipmentButton.click();
    newResourceModal.titleInput.type(equipment);
    newResourceModal.createButton.click();
    adminResources.selectResourceTableRow(equipment).should('be.visible');

    adminResources.tab('Material').click();
    adminResources.newMaterialsButton.click();
    newResourceModal.titleInput.type(material);
    newResourceModal.createButton.click();
    adminResources.selectResourceTableRow(material).should('be.visible');

    sidebar.shiftReportsLink.click();
    shiftReportsPage.newShiftReportButton.click();

    editShiftReportPage.sectionAddPersonButton().click();

    editShiftReportPage.sectionRowField('Person', 0, 'person_resource_id').click();
    editShiftReportPage.getOptionByName(person).should('be.visible');
    editShiftReportPage.root.click();

    editShiftReportPage.sectionRowField('Person', 0, 'organisation_resource_id').click();
    editShiftReportPage.getOptionByName(organisation).should('be.visible');
    editShiftReportPage.root.click();

    editShiftReportPage.sectionRowField('Person', 0, 'role_resource_id').click();
    editShiftReportPage.getOptionByName(role).should('be.visible');
    editShiftReportPage.root.click();

    editShiftReportPage.sectionAddEquipmentButton().click();

    editShiftReportPage.sectionRowField('Equipment', 0, 'equipment_resource_id').click();
    editShiftReportPage.getOptionByName(equipment).should('be.visible');
    editShiftReportPage.root.click();

    editShiftReportPage.sectionAddMaterialButton().click();

    editShiftReportPage.sectionRowField('Material', 0, 'material_resource_id').click();
    editShiftReportPage.getOptionByName(material).should('be.visible');
    editShiftReportPage.root.click();
  });

  describe('when a resource is disabled', () => {
    it('checks its absense on the shift report form', () => {
      const sidebar = new SideBar();
      const adminResources = new AdminResources();
      const newResourceModal = new NewResourceModal();
      const shiftReportsPage = new ShiftReportsPage();
      const editShiftReportPage = new EditShiftReportPage();

      new HomePage().visit({ auth }).projectsWidget.projectWithName('Shift Reports 1').click();
      const issuesListPage = new IssuesListPage();
      issuesListPage.waitIssuesLoad();
      sidebar.adminSubmenuTrigger.click();
      sidebar.resourcesLink.click();

      adminResources.newPeopleButton.click();
      newResourceModal.titleInput.type('example of a disabled resource');
      newResourceModal.createButton.click();
      adminResources.selectResourceTableRow('example of a disabled resource').should('be.visible');
      adminResources.disableResourceByName('example of a disabled resource');
      adminResources.selectResourceTableRow('Disabled').should('be.visible');

      sidebar.shiftReportsLink.click();
      shiftReportsPage.newShiftReportButton.click();

      editShiftReportPage.sectionAddPersonButton().click();

      editShiftReportPage.sectionRowField('Person', 0, 'person_resource_id').click();
      editShiftReportPage.getOptionByName('example of a disabled resource').should('not.exist');
      editShiftReportPage.root.click();
    });
  });
});
