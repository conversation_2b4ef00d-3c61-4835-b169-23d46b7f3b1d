/// <reference types="cypress" />
// ***********************************************************
// This example plugins/index.js can be used to load plugins
//
// You can change the location of this file or turn off loading
// the plugins file with the 'pluginsFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/plugins-guide
// ***********************************************************

// This function is called when a project is opened or re-opened (e.g. due to
// the project's config changing)

import { insertRunning } from './instrumentation';

/**
 * @type {Cypress.PluginConfig}
 */
const { lighthouse, prepareAudit } = require('cypress-audit');
const fs = require('fs');
const { cypressBrowserPermissionsPlugin } = require('cypress-browser-permissions');

module.exports = (on: Cypress.PluginEvents, config: Cypress.PluginConfigOptions) => {
  // `on` is used to hook into various events Cypress emits
  // `config` is the resolved Cypress config

  // eslint-disable-next-line no-param-reassign
  config = cypressBrowserPermissionsPlugin(on, config);

  on('before:browser:launch', (browser, launchOptions) => {
    if (browser.family === 'chromium') {
      launchOptions.args.push(
        `--unsafely-treat-insecure-origin-as-secure="${config.baseUrl},${config.env.apiBaseURL}"`
      );
    }

    prepareAudit(launchOptions);

    // whatever you return here becomes the launchOptions
    return launchOptions;
  });

  on('after:run', async (results) => {
    if ('status' in results && results.status === 'failed') {
      console.log('Cypress run failed.');
      console.log(results.message);
    } else {
      // results: CypressCommandLine.CypressRunResult
      await insertRunning(config, JSON.stringify(results, null, 2));
    }
  });

  // cy.task('log', <string here>); can be used to log text to the console from
  // within a cypress test e.g. during a `cypress run --spec "file"` command.
  on('task', {
    log(message, ...extra) {
      // eslint-disable-next-line no-console
      console.log(message, ...extra);

      return null;
    },
    // @ts-expect-error ts-migrate(7006) FIXME: Parameter 'lighthouseReport' implicitly has an 'an... Remove this comment to see the full error message
    lighthouse: lighthouse((lighthouseReport) => {
      const filename = `./cypress/reports/lighthouse-${lighthouseReport.lhr.fetchTime.replace(/:|\./g, '')}.html`;
      fs.writeFileSync(filename, Buffer.from(lighthouseReport.report, 'utf8'));
    }),
  });

  return config;
};
