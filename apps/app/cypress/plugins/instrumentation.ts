import pg from 'pg';
const { Client } = pg;

const databaseConnectionString = process.env.CYPRESS_INSTRUMENTATION_DATABASE_CONNECTION_STRING;

export const insertRunning = async (config: Cypress.PluginConfigOptions, results: string) => {
  if (!databaseConnectionString) {
    console.log('env CYPRESS_INSTRUMENTATION_DATABASE_CONNECTION_STRING is not set. Skipping instrumentation.');
    return;
  }

  try {
    const [query, data] = [
      `INSERT INTO instrumentation (
        data,
        pipeline_name,
        pipeline_counter,
        stage_counter,
        job_run_index      
      ) VALUES ($1, $2, $3, $4, $5)
      `,
      [
        results,
        process.env.GO_PIPELINE_NAME,
        Number.parseInt(process.env.GO_PIPELINE_COUNTER ?? '', 10) || null,
        Number.parseInt(process.env.GO_STAGE_COUNTER ?? '', 10) || null,
        Number.parseInt(process.env.GO_JOB_RUN_INDEX ?? '', 10) || null,
      ],
    ];

    const client = new Client({ connectionString: databaseConnectionString, ssl: true });
    await client.connect();
    await client.query(query, data);
    await client.end();
  } catch (err) {
    console.error('Error inserting Cypress running:', err);
    throw new Error('Database operation failed');
  }
};
