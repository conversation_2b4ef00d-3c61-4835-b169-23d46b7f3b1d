# E2E tests

There are 3 ways to run the E2E tests:

1. Run everything outside docker
2. Run everything inside docker
3. Run some services inside docker but run cypress manually

The simplest way is to use 3) in the following way:

- start the Shape API using docker
- start the Shape app manually
- run Cypress manually

but feel free to use the way that bests suits you.

---

## Run cypress manually

Shape app uses Cypress to run E2E tests.
To run the tests, it needs to have the App running, as well as the API and the API services.
You can run those services
manually but the suggested way is to run them using docker.

### Starting API and Postgres services

Start Docker.

Run the image of `shape-api-e2e`

```sh
shape-cli login-to-ecr # See shape-cli installation procedure at https://github.com/shape-construction/shape-cli#install
# This is needed so that the API generates valid URLs, since we don't have the nginx container running
export E2E_API_HOST_URL=http://localhost:3001
docker-compose up shape-api-e2e
```

**Alternatively**,
you can have your your local instances running with the e2e db but the suggested way is to run the API using docker.

To setup the database data:

```
bundle exec rails db:reset db:populate:e2e
```

### Shape App

You will need to have your local `.env` file in the root of the project. In that file you will need:

```
REACT_APP_RAILS_API_URL=http://localhost:3001
```

Add the following to your `/etc/hosts` or equivalent:

```
127.0.0.1 app.shape-e2e.com
```

Then:

```sh
npm install
npm run build
cypress/scripts/generate_cert.sh
npx serve --ssl-cert cypress/certs/app.shape-e2e.com.crt --ssl-key cypress/certs/app.shape-e2e.com.key -l 3000 build
```

You can start the app normally as you do when developing (see main [README](../README.md#running-and-developing)) but
keep in mind that you'll lose SSL and tests that depend on service worker registration will fail. You'll also need to
override the cypress `baseUrl` defined in `cypress.json` to use `http` instead of `https`.

```
CYPRESS_baseUrl=http://localhost:3000
```

### Run Cypress

You can run the e2e tests using the command line with a headless browser or using the Cypress Test Runner UI and a browser.

#### Running Cypress Test Runner

To open the Cypress Test Runner, run

```sh
npm run cy
```

#### Running Cypress headless browser

It's possible to run cypress with a headless browser with:

```sh
npm run cy:run
```

Run a single spec using the command line

```sh
npm run cy:run -- -s cypress/e2e/signIn.spec.js
```

## Run everything inside docker

If you want to run the full suite using docker, this will run the e2e tests against a build in production mode:

```sh
APP_ENV=e2e docker-compose run --rm field-app-dev npm run build
cypress/scripts/generate_cert.sh # generate a self signed cert to use on e2e tests
docker-compose run --rm shape-frontend-e2e
```

If you want to run the tests using an actual browser inside the container follow these instructions on MacOs:

1- On MacOs install [XQuartz](https://www.xquartz.org/)
2- On the XQuartz Security preferences choose "Allow connections from network clients"
3- Restart your mac after XQuartz instalation
4- `xhost +localhost`
5- Uncomment the MacOs `DISPLAY` env var on docker-compose.yml for the shape-frontend-e2e service
6- Run `docker-compose run --rm shape-frontend-e2e open --project .`

On linux:
1- `xhost +local:docker`
2- Uncomment the linux `DISPLAY` env var on docker-compose.yml for the shape-frontend-e2e service
3- Run `docker-compose run --rm shape-frontend-e2e open --project .`

On linux follow 5 and 6 above.

If you're only running headless tests or if you don't have an XServer installed then be sure to unset the `DISPLAY` environment variable or else cypress will hang with no output sent to the console.

## Important notes

The current setup for setting data for tests on the database is not the best. If you need to run the same test twice,
be sure to reset the data between tests.

If you are using docker, running `docker-compose up shape-api-e2e` again will reset the data.

Alternatively you can also first save a database dump with:
`docker-compose -f docker-compose.yml -f docker-compose.save-db-dump.yml run --rm shape-api-e2e`

And then fire up the api server with that database dump with:
`docker-compose -f docker-compose.yml -f docker-compose.load-db-dump.yml up shape-api-e2e`

Or if you want to run the full suite using docker:
`docker-compose -f docker-compose.yml -f docker-compose.load-db-dump.yml run --rm shape-frontend-e2e`

## ARM64 and M1 Mac support

Due to the way we build our image, arm64 or M1 Mac users need to explicitly append `-arm64` to the image name or use the example `docker-compose.override.yml` provided.
To enable it just do `cp docker-compose.override.yml.arm64.example docker-compose.override.yml` and run all the docker-compose commands the same way.


## Instrumentation

After each test run, Cypress tries to upload the results to a database. This is used in our CI pipelines to generate
reports and track test results over time.

If you want to enable this locally, you first need to have a valid database and define a database connection string
with the environment variable `CYPRESS_INSTRUMENTATION_DATABASE_CONNECTION_STRING`.
See the following example for a description of the schema used:

```sql
docker exec -it 10651401f2a4 psql -U postgres -d postgres

CREATE DATABASE cypress WITH OWNER=postgres;
\connect cypress;

-- DROP TABLE instrumentation;
CREATE TABLE instrumentation (
  id uuid DEFAULT gen_random_uuid() NOT NULL,
  created_at timestamp without time zone NOT NULL DEFAULT clock_timestamp(),
  data JSONB,
  pipeline_name character varying,
  pipeline_counter integer,
  stage_counter integer,
  job_run_index integer
);
```
```bash
export CYPRESS_INSTRUMENTATION_DATABASE_CONNECTION_STRING="postgresql://postgres:password@localhost:5432/cypress"
```

The `pipeline_name`, `pipeline_counter`, `stage_counter`, `job_run_index` are used to identify the test workers
and retries in the CI pipelines.

