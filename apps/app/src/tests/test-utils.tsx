import { type MessageObject, MessageProvider } from '@messageformat/react';
import { GoogleOAuthProvider } from '@react-oauth/google';
import { initialiseAxiosClient } from '@shape-construction/api/client';
import { issueFactory } from '@shape-construction/api/factories/issues';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { shiftReportsFactory } from '@shape-construction/api/factories/shiftReports';
import { userFactory } from '@shape-construction/api/factories/users';
import { weeklyWorkPlanFactory } from '@shape-construction/api/factories/weeklyWorkPlans';
import { getApiUsersMeQueryOptions } from '@shape-construction/api/src/hooks';
import type {
  IssueSchema,
  ProjectSchema,
  ShiftReportSchema,
  UserSchema,
  WeeklyWorkPlanSchema,
} from '@shape-construction/api/src/types';
import { Toaster } from '@shape-construction/arch-ui';
import { FeatureFlagEntityProvider } from '@shape-construction/feature-flags';
import { type QueryClient, QueryClientProvider } from '@tanstack/react-query';
import {
  type RenderHookOptions,
  act,
  render as rtlRender,
  renderHook as rtlRenderHook,
  screen,
} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { axiosInstance } from 'app/axios';
import { chatContextValueFactory } from 'app/channels/get-stream/factories/chat-context';
import { environment } from 'app/config/environment';
import { CurrentIssueProvider } from 'app/contexts/currentIssue';
import { CurrentProjectProvider } from 'app/contexts/currentProject';
import { CurrentShiftReportProvider } from 'app/contexts/currentShiftReport';
import { CurrentWeeklyWorkPlanProvider } from 'app/contexts/currentWeeklyWorkPlan';
import type { ILayoutConfig } from 'app/contexts/layout/layout.types';
import { LayoutProvider } from 'app/contexts/layout/layoutContext';
import { Form, Formik as FormikProvider } from 'formik';
import { type MemoryHistory, createMemoryHistory } from 'history';
import { produce } from 'immer';
import { type Atom, Provider as JotaiProvider } from 'jotai';
import { delay } from 'msw';
import React, { Suspense } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Provider as ReduxProvider } from 'react-redux';
import { Outlet, Route, Router, Routes } from 'react-router';
import type { StreamChat } from 'stream-chat';
import {
  type ChannelStateContextValue,
  ChannelStateProvider,
  type ChatContextValue,
  ChatProvider,
} from 'stream-chat-react';
import { FakeTipTapEditorContextProvider } from '../app/components/CommentInput/TestUtils/FakeTipTapEditorContextProvider';
import { InstallAppProvider } from '../app/hooks/useInstallApp';
import { SelectedIssueProvider } from '../app/pages/projects/[projectId]/issues/components/IssueCoordinationSpace/hooks/issueContext';
import { buildQueryClient, defaultQueryClientConfig } from '../app/queries/query.client.builder';
import { type RootState, type Store, createReduxStore } from '../app/store';
import createMatchMedia from './create-match-media';

initialiseAxiosClient(axiosInstance);

const queryClientOptions = produce(defaultQueryClientConfig, (draft) => {
  draft.defaultOptions!.queries!.retry = 0;
});

type RenderContext = {
  user?: UserSchema;
  chatClient?: StreamChat;
};

export function eachDeviceSize(callback: (sizeDescription: string, sizeName: string, viewportWidth: number) => void) {
  return describe.each([
    ['Larger', 'lg', 1024],
    ['Smaller', 'sm', 620],
  ])('%s screens (%s [%d])', (...args) => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(args[2]);
    });

    callback(...args);
  });
}

export const fakeTimer = (time: number | Date | string, callback: any) => {
  jest.useFakeTimers();
  jest.setSystemTime(typeof time === 'string' ? new Date(time) : time);
  callback();
  jest.useRealTimers();
};

export const fakeTimerAsync = async (time: number | Date | string, callback: any) => {
  jest.useFakeTimers();
  jest.setSystemTime(typeof time === 'string' ? new Date(time) : time);
  await callback();
  jest.useRealTimers();
};

/**
 * @deprecated
 * This can be removed since we are now using 'resize-observer-polyfill' at the root level
 */
export const fakeObserver = () => {
  // IntersectionObserver and ResizeObserver aren't available in test environment
  const mockObserver = jest.fn();
  mockObserver.mockReturnValue({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  });
  return mockObserver;
};

type TestRouterProps = {
  history: ReturnType<typeof createMemoryHistory>;
  route: { path: string; context?: unknown; layout?: React.ReactNode };
  children: React.ReactNode;
};

const TestRouter = ({ history, route, children }: TestRouterProps) => (
  <Router location={history.location} navigator={history}>
    <Routes>
      <Route path={route.path} element={route.layout || <Outlet context={route.context} />}>
        <Route index element={children} />
      </Route>
    </Routes>
  </Router>
);

type CurrentEntitiesProps = {
  project?: ProjectSchema;
  issue?: IssueSchema;
  shiftReport?: ShiftReportSchema;
  weeklyWorkPlan?: WeeklyWorkPlanSchema;
};

const CurrentEntities: React.FC<React.PropsWithChildren<CurrentEntitiesProps>> = ({
  children,
  project,
  issue,
  shiftReport,
  weeklyWorkPlan,
}) => {
  return (
    <CurrentProjectProvider project={project || projectFactory()}>
      <CurrentIssueProvider issue={issue || issueFactory()}>
        <CurrentShiftReportProvider shiftReport={shiftReport || shiftReportsFactory()}>
          <CurrentWeeklyWorkPlanProvider weeklyWorkPlan={weeklyWorkPlan || weeklyWorkPlanFactory()}>
            {children}
          </CurrentWeeklyWorkPlanProvider>
        </CurrentShiftReportProvider>
      </CurrentIssueProvider>
    </CurrentProjectProvider>
  );
};

type RenderOptions = {
  user?: UserSchema;
  initialState?: any;
  chatContextValue?: ChatContextValue;
  route?: TestRouterProps['route'];
  outletContext?: any;
  history?: TestRouterProps['history'];
  store?: any;
  messages?: Record<string, any>;
  formikBag?: any;
  stateProviderInitialValue?: any;
  fakeEditor?: any;
  suspense?: boolean;
  layoutConfig?: Partial<ILayoutConfig>;
  drawerExpanded?: boolean;
  hookFormValues?: any;
  renderToast?: boolean;
  jotaiInitialValues?: Iterable<readonly [Atom<unknown>, unknown]>;
  pageData?: CurrentEntitiesProps;
};

// For testing components that use Redux connect
// Visit https://redux.js.org/recipes/writing-tests#connected-components
// for more info
function render(
  ui: any,
  {
    initialState,
    user = userFactory(),
    chatContextValue = chatContextValueFactory(),
    route = { path: '/', context: {} },
    outletContext = {},
    history = createMemoryHistory(),
    store = createReduxStore(initialState).store,
    messages = {},
    formikBag = {},
    stateProviderInitialValue = null,
    fakeEditor,
    suspense = true,
    layoutConfig = {},
    drawerExpanded = true,
    hookFormValues = {},
    renderToast = false,
    jotaiInitialValues,
    pageData,
    ...renderOptions
  }: RenderOptions = {}
) {
  const userAction = userEvent.setup({ applyAccept: false });

  function Wrapper({ children }: any) {
    const renderForm = !!Object.keys(formikBag).length;
    const queryClient = buildQueryClient(queryClientOptions);
    queryClient.setQueryData(getApiUsersMeQueryOptions().queryKey, user);
    const hookForm = useForm({ defaultValues: hookFormValues });
    // Spy on store to easily check store.dispatch calls
    // eslint-disable-next-line no-param-reassign
    store.dispatch = jest.fn(store.dispatch);

    const channelStateContextValue = {
      channel: chatContextValue.channel,
    } as ChannelStateContextValue;

    return (
      <GoogleOAuthProvider clientId={environment.GOOGLE_CLIENT_ID}>
        <QueryClientProvider client={queryClient}>
          <MessageProvider messages={messages} locale="en" onError="silent">
            <ReduxProvider store={store}>
              <CurrentEntities {...pageData}>
                <TestRouter history={history} route={route}>
                  <InstallAppProvider>
                    <Suspense fallback={<span>loading...</span>}>
                      <SelectedIssueProvider initialValue={stateProviderInitialValue}>
                        <FormikProvider {...formikBag}>
                          <FormProvider {...hookForm}>
                            <JotaiProvider initialValues={jotaiInitialValues}>
                              <FeatureFlagEntityProvider deviceId="device-id" userId={user?.id}>
                                <LayoutProvider
                                  layoutConfig={layoutConfig as ILayoutConfig}
                                  drawerExpanded={drawerExpanded}
                                >
                                  <FakeTipTapEditorContextProvider fakeEditor={fakeEditor}>
                                    <ChatProvider value={chatContextValue}>
                                      <ChannelStateProvider value={channelStateContextValue}>
                                        {renderForm ? <Form>{children}</Form> : children}
                                        {renderToast && <Toaster />}
                                      </ChannelStateProvider>
                                    </ChatProvider>
                                  </FakeTipTapEditorContextProvider>
                                </LayoutProvider>
                              </FeatureFlagEntityProvider>
                            </JotaiProvider>
                          </FormProvider>
                        </FormikProvider>
                      </SelectedIssueProvider>
                    </Suspense>
                  </InstallAppProvider>
                </TestRouter>
              </CurrentEntities>
            </ReduxProvider>
          </MessageProvider>
        </QueryClientProvider>
      </GoogleOAuthProvider>
    );
  }

  return {
    ...rtlRender(ui, { wrapper: Wrapper, ...renderOptions }),
    user: userAction,
    store,
    history,
  };
}

const asyncRender = (...args: Parameters<typeof render>) =>
  act(async () => {
    await render(...args);
  });

export const renderHook = <TProps, TResult>(
  hook: (props: TProps) => TResult,
  {
    initialState = {},
    chatContextValue = chatContextValueFactory(),
    user = userFactory(),
    store = createReduxStore(initialState).store,
    history = createMemoryHistory(),
    suspense = false,
    messages = {},
    queryClient = buildQueryClient(queryClientOptions),
    route = { path: '/', context: {} },
    outletContext = {},
    jotaiInitialValues,
    renderToast = false,
    pageData,
    ...options
  }: RenderHookOptions<TProps> &
    RenderContext & {
      initialState?: Partial<RootState>;
      chatContextValue?: ChatContextValue;
      user?: UserSchema | null;
      store?: Store;
      history?: MemoryHistory;
      suspense?: boolean;
      messages?: MessageObject;
      queryClient?: QueryClient;
      route?: { path: string; context?: unknown };
      outletContext?: unknown;
      jotaiInitialValues?: Array<[Atom<unknown>, unknown]>;
      renderToast?: boolean;
      pageData?: CurrentEntitiesProps;
    } = {}
) => {
  // @ts-expect-error
  // eslint-disable-next-line no-param-reassign
  store.dispatch = jest.fn(store.dispatch);
  queryClient.setQueryData(getApiUsersMeQueryOptions().queryKey, user);

  const channelStateContextValue = {
    channel: chatContextValue.channel,
  } as ChannelStateContextValue;

  const wrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
    <JotaiProvider initialValues={jotaiInitialValues}>
      <QueryClientProvider client={queryClient}>
        <Suspense fallback={suspense}>
          <CurrentEntities {...pageData}>
            <TestRouter history={history} route={route}>
              <MessageProvider messages={messages} locale="en" onError="silent">
                <ReduxProvider store={store}>
                  <FeatureFlagEntityProvider deviceId="device-id" userId={user?.id}>
                    <ChatProvider value={chatContextValue}>
                      <ChannelStateProvider value={channelStateContextValue}>
                        {children}
                        {renderToast && <Toaster />}
                      </ChannelStateProvider>
                    </ChatProvider>
                  </FeatureFlagEntityProvider>
                </ReduxProvider>
              </MessageProvider>
            </TestRouter>
          </CurrentEntities>
        </Suspense>
      </QueryClientProvider>
    </JotaiProvider>
  );

  return { ...rtlRenderHook(hook, { wrapper, ...options }), store };
};

export const findAsyncToaster = (message: string) =>
  screen.findByRole('status', {
    name: (_, e) => e.textContent?.includes(message) ?? false,
  });

export const waitForWindowMessage = <Msg = any>(
  win: Window,
  callback: (message: Msg) => void | Promise<void>,
  { timeout = 2000 }: { timeout?: number } = {}
): Promise<Msg> => {
  const messagePromise = new Promise<Msg>((res) => {
    const handler = async (e: MessageEvent) => {
      try {
        await callback(e.data);
        res(e.data);
        win.removeEventListener('message', handler);
      } catch (e) {}
    };
    win.addEventListener('message', handler);
  });
  const timeoutPromise = delay(timeout).then((_) => Promise.reject('Timeout: waitForWindowMessage'));

  return Promise.race([timeoutPromise, messagePromise]);
};

// re-export everything
export * from '@testing-library/react';
// override render method
export { createMatchMedia, asyncRender, render, userEvent };
