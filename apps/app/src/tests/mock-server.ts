import { deleteApiLogoutMockHandler } from '@shape-construction/api/handlers-factories/authentication';
import { getApiConstructionRolesMockHandler } from '@shape-construction/api/handlers-factories/construction-roles';
import { getApiFeatureFlagsMockHandler } from '@shape-construction/api/handlers-factories/feature-flags';
import { imageHandlerMockHandler } from '@shape-construction/api/handlers-factories/images';
import {
  getApiNotificationsMockHandler,
  getApiNotificationsOverviewMockHandler,
  postApiNotificationsMarkReadMockHandler,
} from '@shape-construction/api/handlers-factories/notifications';
import { getApiOnboardingMockHandler } from '@shape-construction/api/handlers-factories/onboarding';
import {
  getApiProjectsMockHandler,
  getApiProjectsProjectIdMockHandler,
  patchApiProjectsProjectIdMockHandler,
  postApiProjectsProjectIdDefaultMockHandler,
} from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdAccessRequestsMockHandler } from '@shape-construction/api/handlers-factories/projects/access-requests';
import {
  deleteApiProjectsProjectIdCustomFieldsCustomFieldIdMockHandler,
  getApiProjectsProjectIdCustomFieldsMockHandler,
  patchApiProjectsProjectIdCustomFieldsCustomFieldIdMockHandler,
  postApiProjectsProjectIdCustomFieldsMockHandler,
} from '@shape-construction/api/handlers-factories/projects/custom-fields';
import { getApiProjectsProjectIdDashboardsIssuesStalenessIssuesMockHandler } from '@shape-construction/api/handlers-factories/projects/dashboard';
import { getApiProjectsProjectIdDashboardsMockHandler } from '@shape-construction/api/handlers-factories/projects/data-book';
import { getApiProjectsProjectIdDisciplinesMockHandler } from '@shape-construction/api/handlers-factories/projects/disciplines';
import {
  getApiProjectsProjectIdDocumentsDocumentIdMockHandler,
  getApiProjectsProjectIdDocumentsMockHandler,
} from '@shape-construction/api/handlers-factories/projects/documents';
import { getApiProjectsProjectIdEventsMockHandler } from '@shape-construction/api/handlers-factories/projects/events';
import { patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler } from '@shape-construction/api/handlers-factories/projects/groups';
import {
  deleteApiProjectsProjectIdIssuesIssueIdCommentsCommentIdMockHandler,
  deleteApiProjectsProjectIdIssuesIssueIdMockHandler,
  getApiProjectsProjectIdIssueViewsMockHandler,
  getApiProjectsProjectIdIssuesGroupCountMockHandler,
  getApiProjectsProjectIdIssuesIssueIdDocumentsMockHandler,
  getApiProjectsProjectIdIssuesIssueIdFeedPublicMockHandler,
  getApiProjectsProjectIdIssuesIssueIdFeedTeamMockHandler,
  getApiProjectsProjectIdIssuesIssueIdIssueImagesMockHandler,
  getApiProjectsProjectIdIssuesIssueIdMockHandler,
  getApiProjectsProjectIdIssuesIssueIdVisitMockHandler,
  getApiProjectsProjectIdIssuesMockHandler,
  patchApiProjectsProjectIdIssuesIssueIdMockHandler,
  postApiProjectsProjectIdIssuesIssueIdAssignmentsMockHandler,
  postApiProjectsProjectIdIssuesIssueIdCommentsMockHandler,
  postApiProjectsProjectIdIssuesIssueIdVisitMockHandler,
  postApiProjectsProjectIdIssuesMockHandler,
} from '@shape-construction/api/handlers-factories/projects/issues';
import {
  getApiProjectsProjectIdLocationsLocationIdMockHandler,
  getApiProjectsProjectIdLocationsMockHandler,
} from '@shape-construction/api/handlers-factories/projects/locations';
import { getApiQueuedTasksMockHandler } from '@shape-construction/api/handlers-factories/projects/queued-tasks';
import {
  deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler,
  getApiProjectsProjectIdShiftActivitiesMockHandler,
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersMockHandler,
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler,
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMockHandler,
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsMockHandler,
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler,
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMockHandler,
  patchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler,
  postApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-activities';
import {
  getApiProjectsProjectIdShiftReportsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdActivitiesActivityIdDocumentsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdContractForcesContractForceIdMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdEquipmentsEquipmentIdMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdMaterialsMaterialIdDocumentsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler,
  patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import {
  getApiProjectsProjectIdPeopleMockHandler,
  getApiProjectsProjectIdPeopleTeamMemberIdMockHandler,
  getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesMockHandler,
} from '@shape-construction/api/handlers-factories/projects/team-members';
import {
  getApiProjectsProjectIdTeamsMockHandler,
  getApiProjectsProjectIdTeamsTeamIdMockHandler,
  getApiProjectsProjectIdTeamsTeamIdResourcesKindMockHandler,
  getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler,
  postApiProjectsProjectIdTeamsTeamIdResourcesKindMockHandler,
  postApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMockHandler,
} from '@shape-construction/api/handlers-factories/projects/teams';
import {
  getApiProjectsProjectIdWeeklyWorkPlansMockHandler,
  getApiProjectsProjectIdWeeklyWorkPlansPlanIdActivitiesMockHandler,
  getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler,
  getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler,
  postApiProjectsProjectIdWeeklyWorkPlansPlanIdExportMockHandler,
  postApiProjectsProjectIdWeeklyWorkPlansPlanIdLookbackExportMockHandler,
} from '@shape-construction/api/handlers-factories/projects/weekly-work-plans';
import {
  getApiTeamJoinTokenPublicMockHandler,
  postApiTeamMembersMockHandler,
} from '@shape-construction/api/handlers-factories/team-members';
import { getApiTimeZonesMockHandler } from '@shape-construction/api/handlers-factories/timezones';
import {
  getApiUsersMeMockHandler,
  postApiUsersPasswordInstructionsMockHandler,
} from '@shape-construction/api/handlers-factories/users';
import { streamApiMocks } from 'app/channels/get-stream/msw/mock-server';
import { matchRequestUrl } from 'msw';
import { setupServer } from 'msw/node';

const handlers = [
  // Intercept image service
  imageHandlerMockHandler(),
  getApiTimeZonesMockHandler(),
  getApiFeatureFlagsMockHandler(),

  // users
  getApiUsersMeMockHandler(),
  deleteApiLogoutMockHandler(),
  postApiUsersPasswordInstructionsMockHandler(),

  // notifications
  getApiNotificationsOverviewMockHandler(),
  getApiNotificationsMockHandler(),
  postApiNotificationsMarkReadMockHandler(),

  // projects
  patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler(),
  getApiProjectsMockHandler(),
  getApiProjectsProjectIdMockHandler(),
  patchApiProjectsProjectIdMockHandler(),
  postApiProjectsProjectIdDefaultMockHandler(),

  // project documents
  getApiProjectsProjectIdDocumentsMockHandler(),
  getApiProjectsProjectIdDocumentsDocumentIdMockHandler(),

  // project disciplines
  getApiProjectsProjectIdDisciplinesMockHandler(),

  // project locations
  getApiProjectsProjectIdLocationsMockHandler(),
  getApiProjectsProjectIdLocationsLocationIdMockHandler(),

  // project teams
  getApiProjectsProjectIdTeamsMockHandler(),
  getApiProjectsProjectIdTeamsTeamIdMockHandler(),
  getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesMockHandler(),
  getApiProjectsProjectIdPeopleMockHandler(),
  getApiProjectsProjectIdPeopleTeamMemberIdMockHandler(),

  // project access requests
  getApiProjectsProjectIdAccessRequestsMockHandler(),

  // project custom fields
  getApiProjectsProjectIdCustomFieldsMockHandler(),
  postApiProjectsProjectIdCustomFieldsMockHandler(),
  patchApiProjectsProjectIdCustomFieldsCustomFieldIdMockHandler(),
  deleteApiProjectsProjectIdCustomFieldsCustomFieldIdMockHandler(),

  // project events
  getApiProjectsProjectIdEventsMockHandler(),

  // project dashboard
  getApiProjectsProjectIdDashboardsIssuesStalenessIssuesMockHandler(),

  // issues
  getApiProjectsProjectIdIssuesMockHandler(),
  postApiProjectsProjectIdIssuesMockHandler(),
  getApiProjectsProjectIdIssuesIssueIdMockHandler(),
  patchApiProjectsProjectIdIssuesIssueIdMockHandler(),
  deleteApiProjectsProjectIdIssuesIssueIdMockHandler(),
  getApiProjectsProjectIdIssuesIssueIdVisitMockHandler(),
  postApiProjectsProjectIdIssuesIssueIdVisitMockHandler(),
  getApiProjectsProjectIdIssuesIssueIdDocumentsMockHandler(),
  getApiProjectsProjectIdIssuesIssueIdIssueImagesMockHandler(),
  postApiProjectsProjectIdIssuesIssueIdCommentsMockHandler(),
  deleteApiProjectsProjectIdIssuesIssueIdCommentsCommentIdMockHandler(),
  getApiProjectsProjectIdIssuesGroupCountMockHandler(),
  postApiProjectsProjectIdIssuesIssueIdAssignmentsMockHandler(),
  getApiProjectsProjectIdIssuesIssueIdFeedPublicMockHandler(),
  getApiProjectsProjectIdIssuesIssueIdFeedTeamMockHandler(),
  getApiProjectsProjectIdIssueViewsMockHandler(),

  // teams subscription plan
  getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler(),
  postApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMockHandler(),

  // resources
  getApiProjectsProjectIdTeamsTeamIdResourcesKindMockHandler(),
  postApiProjectsProjectIdTeamsTeamIdResourcesKindMockHandler(),

  // team members
  postApiTeamMembersMockHandler(),

  // team join token public
  getApiTeamJoinTokenPublicMockHandler(),

  // weekly work plans
  getApiProjectsProjectIdWeeklyWorkPlansMockHandler(),
  getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(),
  getApiProjectsProjectIdWeeklyWorkPlansPlanIdActivitiesMockHandler(),
  postApiProjectsProjectIdWeeklyWorkPlansPlanIdExportMockHandler(),
  postApiProjectsProjectIdWeeklyWorkPlansPlanIdLookbackExportMockHandler(),
  getApiProjectsProjectIdWeeklyWorkPlansPlanIdProgressLogsMockHandler(),

  // shift reports
  getApiProjectsProjectIdShiftReportsMockHandler(),
  getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(),
  patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(),
  getApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMockHandler(),
  getApiProjectsProjectIdShiftReportsShiftReportIdContractForcesContractForceIdMockHandler(),
  getApiProjectsProjectIdShiftReportsShiftReportIdEquipmentsEquipmentIdMockHandler(),
  getApiProjectsProjectIdShiftReportsShiftReportIdMaterialsMaterialIdDocumentsMockHandler(),
  getApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsMockHandler(),
  getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(),
  getApiProjectsProjectIdShiftReportsShiftReportIdActivitiesActivityIdDocumentsMockHandler(),

  // shift activities
  getApiProjectsProjectIdShiftActivitiesMockHandler(),
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdMockHandler(),
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMockHandler(),
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersMockHandler(),

  // Shift activity progress log
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMockHandler(),
  postApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMockHandler(),
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler(),
  patchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler(),
  deleteApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMockHandler(),
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsMockHandler(),

  getApiQueuedTasksMockHandler(),

  // onboarding
  getApiOnboardingMockHandler(),

  // construction roles
  getApiConstructionRolesMockHandler(),

  // Dashboards list
  getApiProjectsProjectIdDashboardsMockHandler(),

  // Stream API
  ...streamApiMocks,
];

export const server = setupServer(...handlers);

// Based on https://mswjs.io/docs/extensions/life-cycle-events#asserting-request-payload
// Useful for unit testing when we want to assert an outgoing request and we don't have a UI side-effect to probe on.
export const waitForRequest = (method: string, url: string, predicate: (req: Request) => boolean = () => true) => {
  let requestId = '';
  return new Promise<Request>((resolve, reject) => {
    server.events.on('request:start', ({ request, requestId: reqId }) => {
      const matchesMethod = request.method.toLowerCase() === method.toLowerCase();
      const matchesUrl = matchRequestUrl(new URL(request.url), url).matches;
      if (matchesMethod && matchesUrl && predicate(request)) {
        requestId = reqId;
      }
    });
    server.events.on('request:match', async ({ request, requestId: reqId }) => {
      if (reqId === requestId) {
        resolve(request);
      }
    });
    server.events.on('request:unhandled', ({ request, requestId: reqId }) => {
      if (reqId === requestId) {
        reject(new Error(`The ${request.method} ${new URL(request.url).href} request was unhandled.`));
      }
    });
  });
};

export const waitForResponse = (method: string, url: string) =>
  new Promise<Response>((resolve, reject) => {
    let requestId = '';

    server.events.on('request:start', ({ request, requestId: reqId }) => {
      const matchesMethod = request.method.toLowerCase() === method.toLowerCase();
      const matchesUrl = matchRequestUrl(new URL(request.url), url).matches;
      if (matchesMethod && matchesUrl) {
        requestId = reqId;
      }
    });

    server.events.on('response:mocked', ({ response, requestId: reqId }) => {
      if (reqId === requestId) {
        resolve(response);
      }
    });
    server.events.on('request:unhandled', ({ request, requestId: reqId }) => {
      if (reqId === requestId) {
        reject(new Error(`The ${request.method} ${new URL(request.url).href} request was unhandled.`));
      }
    });
  });
