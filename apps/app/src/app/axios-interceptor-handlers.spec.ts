import { postApiLoginRefresh<PERSON>ock<PERSON>and<PERSON> } from '@shape-construction/api/handlers-factories/authentication';
import { environment } from 'app/config/environment';
import { server } from 'tests/mock-server';
import * as authorization from './authorization';

import { refreshAuthToken } from './axios-interceptor-handlers';

describe('axios-interceptor-handlers', () => {
  describe('refreshAuthToken', () => {
    afterEach(() => {
      jest.resetAllMocks();
    });

    describe('when refresh token is present', () => {
      it('preserves authorization headers', async () => {
        jest.spyOn(authorization, 'getRefreshToken').mockReturnValue('current-refresh-token');
        const setTokenMock = jest.spyOn(authorization, 'setToken');
        const setRefreshTokenMock = jest.spyOn(authorization, 'setRefreshToken');
        server.use(
          postApiLoginRefreshMockHandler(undefined, {
            headers: {
              'authorization-refresh': 'new-refresh-token',
              authorization: 'new-auth-token',
            },
          })
        );

        const response = await refreshAuthToken();

        expect(setTokenMock).toHaveBeenCalledWith('new-auth-token');
        expect(setRefreshTokenMock).toHaveBeenCalledWith('new-refresh-token');
        expect(response.headers).toEqual(
          expect.objectContaining({
            'authorization-refresh': 'new-refresh-token',
            authorization: 'new-auth-token',
          })
        );
      });

      it('makes request with correct headers', async () => {
        jest.spyOn(authorization, 'getRefreshToken').mockReturnValue('current-refresh-token');

        const onRequest = jest.fn();
        server.use(
          postApiLoginRefreshMockHandler((req) => {
            const headers = {
              'authorization-refresh': req.request.headers.get('authorization-refresh'),
              'x-shape-user-agent': req.request.headers.get('x-shape-user-agent'),
            };
            onRequest(headers);
            return undefined;
          })
        );

        await refreshAuthToken();

        expect(onRequest).toHaveBeenCalledWith({
          'authorization-refresh': 'current-refresh-token',
          'x-shape-user-agent': `ShapeApp/${environment.VERSION}`,
        });
      });
    });

    describe('when refresh token is missing', () => {
      it('throws error and does not call refresh token api', async () => {
        jest.spyOn(authorization, 'getRefreshToken').mockReturnValue(null);
        const onRefreshApiMock = jest.fn();
        server.use(postApiLoginRefreshMockHandler(() => onRefreshApiMock()));

        await expect(refreshAuthToken()).rejects.toThrow('No refresh token found');
        expect(onRefreshApiMock).not.toBeCalled();
      });
    });
  });
});
