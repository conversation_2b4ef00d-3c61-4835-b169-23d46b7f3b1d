import { createMemoryHistory } from 'history';
import React from 'react';
import { render, screen, waitFor } from 'tests/test-utils';
import { NewActivity } from './NewActivity';

describe('<NewActivity />', () => {
  it('renders the title', async () => {
    render(<NewActivity />);

    expect(await screen.findByRole('heading', { name: 'activities.new.title' })).toBeInTheDocument();
  });

  describe('when cancel button is clicked', () => {
    it('redirects to activities page', async () => {
      const route = { path: '/projects/:projectId/activities/new' };
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/activities/new'],
      });
      const { user } = render(<NewActivity />, {
        route,
        history,
      });

      await user.click(await screen.findByRole('button', { name: 'Close Overlay' }));

      expect(history.location.pathname).toBe('/projects/project-0/activities');
    });
  });

  describe('when the user clicks on the new activity button', () => {
    it('redirects to activities page', async () => {
      const route = {
        path: '/projects/:projectId/activities/new',
      };
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/activities/new'],
      });
      const { user } = render(<NewActivity />, {
        route,
        history,
      });

      await user.click(await screen.findByRole('button', { name: 'Close Overlay' }));

      await waitFor(() => {
        return expect(history.location.pathname).toBe('/projects/project-0/activities');
      });
    });
  });
});
