import { useMessageGetter } from '@messageformat/react';
import type {
  PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema,
  ProjectSchema,
} from '@shape-construction/api/src/types';
import { <PERSON><PERSON>, Drawer } from '@shape-construction/arch-ui';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import type { ResponseError } from 'app/axios';
import { ActivityForm } from 'app/components/ShiftManager/Activities/ActivityForm/ActivityForm';
import { LoadingSkeleton } from 'app/components/ShiftManager/Activities/ActivityForm/LoadingSkeleton';
import { useActivityForm } from 'app/components/ShiftManager/hooks/useActivityForm';
import { useCreateShiftActivity } from 'app/queries/activities/activities';
import { useProject } from 'app/queries/projects/projects';
import React from 'react';
import { FormProvider } from 'react-hook-form';
import { useNavigate, useParams } from 'react-router';
import { useTaskIdentifierError } from '../useTaskIdentifierError';
import { serializeShiftActivityValues } from '../utils';

export const NewActivity = () => {
  const navigate = useNavigate();
  const messages = useMessageGetter('activities.new');
  const formMessages = useMessageGetter('activities.form');

  const { projectId } = useParams() as { projectId: ProjectSchema['id'] };
  const { data: project } = useProject(projectId);
  const timeZone = project?.timezone;

  const onClose = () => {
    navigate(`/projects/${projectId}/activities`);
  };

  const { mutate: createShiftActivity, error: createError } = useCreateShiftActivity();

  const onAddActivity = (values: PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema) => {
    const createShiftActivityValues = serializeShiftActivityValues(values, timeZone!);

    createShiftActivity(
      {
        projectId,
        data: createShiftActivityValues as PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema,
      },
      {
        onSuccess: () => {
          showSuccessToast({ message: formMessages('toast.createSuccess') });
          onClose();
        },
      }
    );
  };

  const { form, isLoadingData } = useActivityForm();
  const onSubmit = form.handleSubmit(onAddActivity);

  useTaskIdentifierError(form, createError as ResponseError);

  if (isLoadingData) {
    return <LoadingSkeleton />;
  }

  return (
    <Drawer.Root open onClose={onClose} maxWidth="xl" data-cy="new-activity-form">
      <Drawer.Header onClose={onClose}>
        <Drawer.Title className="text-lg font-medium leading-7 text-gray-900 md:text-xl md:font-medium md:leading-7">
          {messages('title')}
        </Drawer.Title>
      </Drawer.Header>
      <Drawer.Content className="bg-white">
        <FormProvider {...form}>
          <ActivityForm timeZone={timeZone} onSubmit={onSubmit} />
        </FormProvider>
      </Drawer.Content>
      <Drawer.Footer className="border-t border-gray-200 flex justify-end">
        <Button size="md" color="primary" variant="contained" onClick={onSubmit}>
          {formMessages('createCTA')}
        </Button>
      </Drawer.Footer>
    </Drawer.Root>
  );
};

export { NewActivity as Component };
