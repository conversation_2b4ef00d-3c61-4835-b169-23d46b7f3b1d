import { useMessageGetter } from '@messageformat/react';
import type {
  PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationRequestSchema,
  ProjectSchema,
  ShiftActivitySchema,
} from '@shape-construction/api/src/types';
import { <PERSON><PERSON>, Drawer } from '@shape-construction/arch-ui';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import type { ResponseError } from 'app/axios';
import { ActivityForm } from 'app/components/ShiftManager/Activities/ActivityForm/ActivityForm';
import { LoadingSkeleton } from 'app/components/ShiftManager/Activities/ActivityForm/LoadingSkeleton';
import { useActivityForm } from 'app/components/ShiftManager/hooks/useActivityForm';
import { ProgressDrawer } from 'app/pages/projects/[projectId]/weekly-planner/plan/components/progress/ProgressDrawer';
import { useProgressDrawerState } from 'app/pages/projects/[projectId]/weekly-planner/plan/hooks/useProgressDrawerState';
import { useUpdateShiftActivity } from 'app/queries/activities/activities';
import React from 'react';
import { FormProvider } from 'react-hook-form';
import { useTaskIdentifierError } from '../useTaskIdentifierError';
import { serializeShiftActivityValues } from '../utils';

type EditActivityProps = {
  projectId: ProjectSchema['id'];
  shiftActivityId: ShiftActivitySchema['id'];
  onClose: () => void;
  timezone: string;
};

type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdBodyKeys =
  keyof PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationRequestSchema;

export const EditActivity: React.FC<EditActivityProps> = ({ projectId, shiftActivityId, onClose, timezone }) => {
  const messages = useMessageGetter('activities.edit');
  const formMessages = useMessageGetter('activities.form');

  const { drawerOpen, drawerPlanActivity, drawerDefaultDate, closeDrawer } = useProgressDrawerState();

  const { mutate: updateShiftActivity, error: updateError } = useUpdateShiftActivity();
  const onEditActivity = (values: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationRequestSchema) => {
    const updatedFields = Object.entries(values).reduce((acc, [key, value]) => {
      if (value !== initialValues[key as PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdBodyKeys]) {
        return {
          ...acc,
          [key]: value,
        };
      }
      return acc;
    }, {} as PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationRequestSchema);

    const updatedShiftActivityValues = serializeShiftActivityValues(updatedFields, timezone!);

    updateShiftActivity(
      {
        projectId,
        shiftActivityId,
        data: updatedShiftActivityValues as PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationRequestSchema,
      },
      {
        onSuccess: () => {
          showSuccessToast({ message: formMessages('toast.editSuccess') });
          onClose();
        },
      }
    );
  };

  const { form, initialValues, isLoadingData } = useActivityForm();
  const onSubmit = form.handleSubmit(onEditActivity);

  useTaskIdentifierError(form, updateError as ResponseError);

  const renderContent = () => {
    if (isLoadingData) {
      return <LoadingSkeleton />;
    }

    return (
      <FormProvider {...form}>
        <ActivityForm timeZone={timezone} onSubmit={onSubmit} />
      </FormProvider>
    );
  };

  return (
    <>
      <Drawer.Header onClose={onClose}>
        <Drawer.Title className="text-lg font-medium leading-7 text-gray-900 md:text-xl md:font-medium md:leading-7">
          {messages('title')}
        </Drawer.Title>
      </Drawer.Header>
      <Drawer.Content className="bg-white">{renderContent()}</Drawer.Content>
      <Drawer.Footer className="border-t border-gray-200 flex justify-end">
        <Button size="md" color="primary" variant="contained" onClick={onSubmit} disabled={isLoadingData}>
          {formMessages('saveCTA')}
        </Button>
      </Drawer.Footer>

      <ProgressDrawer
        isFromEditActivity
        open={drawerOpen}
        planActivity={drawerPlanActivity}
        defaultDate={drawerDefaultDate}
        onClose={() => closeDrawer()}
      />
    </>
  );
};
