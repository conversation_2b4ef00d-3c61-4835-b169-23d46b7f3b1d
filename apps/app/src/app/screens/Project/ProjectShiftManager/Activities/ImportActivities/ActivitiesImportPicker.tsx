import { useMessage, useMessageGetter } from '@messageformat/react';
import { Button, FileUpload, Menu } from '@shape-construction/arch-ui';
import { ArrowDownTrayIcon, ArrowUpTrayIcon, ChevronDownIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import React, { forwardRef, type PropsWithChildren, useRef } from 'react';

type ImportDataOption = {
  accept: string;
  enabled: boolean;
  onSelectFiles: (files: File[]) => void;
  multiple?: boolean;
};

type DownloadTemplateOption = {
  enabled: boolean;
  target: string;
};

export type ImportPickerOptions = {
  downloadTemplate: DownloadTemplateOption;
  importData: ImportDataOption;
};

export type ImportPickerProps = PropsWithChildren<{
  options: ImportPickerOptions;
}>;

export const ImportPickerTrigger = forwardRef<React.ElementRef<'button'>>((_, ref) => {
  const triggerTitle = useMessage('activities.import.trigger.title');

  return (
    <Button ref={ref} variant="outlined" color="secondary" size="sm">
      {triggerTitle}
      <ChevronDownIcon className="w-5 h-5" />
    </Button>
  );
});

export const ActivitiesImportPicker = ({ children = <ImportPickerTrigger />, options }: ImportPickerProps) => {
  const messageGetter = useMessageGetter('activities.import');
  const importOptionRef = useRef<React.ElementRef<typeof FileUpload.Root>>(null);

  const isOptionEnabled = (key: keyof ImportPickerOptions) => options[key]?.enabled;

  return (
    <div>
      <Menu.Root>
        <Menu.Trigger as="div">{children}</Menu.Trigger>
        <Menu.Items unmount={false} className="md:w-[358px]">
          {isOptionEnabled('importData') && (
            <button type="button" className="w-full" onClick={() => importOptionRef.current?.click()}>
              <Menu.Item as="div" icon={ArrowUpTrayIcon}>
                <FileUpload.Root
                  ref={importOptionRef}
                  accept={options.importData?.accept}
                  onChange={options.importData?.onSelectFiles}
                  multiple={options.importData?.multiple}
                  className="flex flex-row gap-2 items-center justify-start"
                >
                  <div className="flex flex-col gap-1 justify-start items-start">
                    <div className="text-sm font-medium leading-tight">
                      <FileUpload.Label>{messageGetter('options.importData.title')}</FileUpload.Label>
                    </div>
                    <div className="opacity-60 text-sm font-normal leading-tight">
                      {messageGetter('options.importData.subtitle')}
                    </div>
                  </div>
                </FileUpload.Root>
              </Menu.Item>
            </button>
          )}
          {isOptionEnabled('downloadTemplate') && (
            <a href={options.downloadTemplate.target} download>
              <Menu.Item as="div" icon={ArrowDownTrayIcon}>
                <div className="flex flex-col gap-1 justify-start items-start">
                  <div className="text-sm font-medium leading-tight">
                    {messageGetter('options.downloadTemplate.title')}
                  </div>
                  <div className="opacity-60 text-sm font-normal leading-tight">
                    {messageGetter('options.downloadTemplate.subtitle')}
                  </div>
                </div>
              </Menu.Item>
            </a>
          )}
        </Menu.Items>
      </Menu.Root>
    </div>
  );
};
