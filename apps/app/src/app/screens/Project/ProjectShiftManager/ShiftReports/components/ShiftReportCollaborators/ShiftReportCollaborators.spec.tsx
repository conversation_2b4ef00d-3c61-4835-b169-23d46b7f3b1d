import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { shiftReportsAvailableActions, shiftReportsFactory } from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import {
  getApiProjectsProjectIdShiftReportsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { ShiftReportCollaborators } from './ShiftReportCollaborators';

describe('ShiftReportCollaborators', () => {
  describe('when there are no collaborators', () => {
    it('displays Add collaborators button', () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: null,
            lastEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            total: 0,
          },
          shiftReports: [],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        getApiProjectsProjectIdPeopleMockHandler(() => [
          teamMemberFactory({
            id: 2,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'Another contributor' }),
          }),
          teamMemberFactory({
            id: 3,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'A contributor' }),
          }),
          teamMemberFactory({
            id: 4,
            role: 'admin',
            user: userBasicDetailsFactory({ name: 'An admin' }),
          }),
          teamMemberFactory({
            id: 5,
            role: 'owner',
            user: userBasicDetailsFactory({ name: 'An owner' }),
          }),
        ])
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const handleChange = jest.fn();
      render(<ShiftReportCollaborators value={[]} onChange={handleChange} />, {
        route,
        history,
      });

      expect(screen.getByRole('button', { name: 'shiftReport.collaborators.addCollaborators' })).toBeInTheDocument();
    });

    it('opens the collaborators modal and adds new collaborators', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: null,
            lastEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            total: 0,
          },
          shiftReports: [],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        getApiProjectsProjectIdPeopleMockHandler(() => [
          teamMemberFactory({
            id: 2,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'Another contributor' }),
          }),
          teamMemberFactory({
            id: 3,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'A contributor' }),
          }),
          teamMemberFactory({
            id: 4,
            role: 'admin',
            user: userBasicDetailsFactory({ name: 'An admin' }),
          }),
          teamMemberFactory({
            id: 5,
            role: 'owner',
            user: userBasicDetailsFactory({ name: 'An owner' }),
          }),
        ])
      );

      let value: number[] = [];
      const handleChange = jest.fn((newValue) => {
        value = newValue;
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/test-project-id/shift-reports/shift-report-1/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      const { rerender } = render(<ShiftReportCollaborators value={value} onChange={handleChange} />, {
        route,
        history,
      });

      userEvent.click(await screen.findByRole('button', { name: 'shiftReport.collaborators.addCollaborators' }));

      await userEvent.click(await screen.findByRole('radio', { name: /Another contributor/ }));
      await userEvent.click(screen.getByRole('radio', { name: /A contributor/ }));
      await userEvent.click(screen.getByRole('button', { name: 'shiftReport.collaboratorsModal.actions.add' }));
      userEvent.click(await screen.findByRole('button', { name: 'actions.save' }));

      await waitFor(() => {
        expect(handleChange).toHaveBeenCalledWith([2, 3]);
      });

      rerender(<ShiftReportCollaborators value={value} onChange={handleChange} />);

      expect(screen.getAllByRole('listitem', { name: 'user avatar item' })).toHaveLength(2);
    });
  });

  describe('when there are collaborators', () => {
    it('displays the collaborators and edit button', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        teamMemberId: 1,
        collaboratorsTeamMemberIds: [2, 3, 4],
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: null,
            lastEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            total: 0,
          },
          shiftReports: [],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        getApiProjectsProjectIdPeopleMockHandler(() => [
          teamMemberFactory({
            id: 1,
            role: 'owner',
            user: userBasicDetailsFactory({ name: 'An owner' }),
          }),
          teamMemberFactory({
            id: 2,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'Another contributor' }),
          }),
          teamMemberFactory({
            id: 3,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'A contributor' }),
          }),
        ])
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      render(<ShiftReportCollaborators value={[2, 3, 4]} onChange={jest.fn()} />, {
        route,
        history,
      });
      expect(await screen.findAllByRole('listitem', { name: 'user avatar item' })).toHaveLength(3);
      expect(await screen.findByRole('presentation', { name: 'Generic user' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'shiftReport.collaborators.edit' })).toBeInTheDocument();
    });

    it('opens the collaborators modal and edits collaborators', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/test-project-id/shift-reports/shift-report-1/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: null,
            lastEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            total: 0,
          },
          shiftReports: [],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        getApiProjectsProjectIdPeopleMockHandler(() => [
          teamMemberFactory({
            id: 2,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'Another contributor' }),
          }),
          teamMemberFactory({
            id: 3,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'A contributor' }),
          }),
          teamMemberFactory({
            id: 4,
            role: 'admin',
            user: userBasicDetailsFactory({ name: 'An admin' }),
          }),
          teamMemberFactory({
            id: 5,
            role: 'owner',
            user: userBasicDetailsFactory({ name: 'An owner' }),
          }),
        ])
      );

      let value: number[] = [2, 3];
      const handleChange = jest.fn((newValue) => {
        value = newValue;
      });
      const { rerender } = render(<ShiftReportCollaborators value={value} onChange={handleChange} />, {
        route,
        history,
      });

      userEvent.click(
        screen.getByRole('button', {
          name: 'shiftReport.collaborators.edit',
        })
      );
      userEvent.click(
        await screen.findByRole('button', {
          name: 'shiftReport.collaboratorsModal.actions.addCollaborators',
        })
      );

      await userEvent.click(await screen.findByRole('radio', { name: /An admin/ }));
      await userEvent.click(screen.getByRole('button', { name: 'shiftReport.collaboratorsModal.actions.add' }));
      userEvent.click(await screen.findByRole('button', { name: 'actions.save' }));

      await waitFor(() => {
        expect(handleChange).toHaveBeenCalledWith([2, 3, 4]);
      });

      rerender(<ShiftReportCollaborators value={value} onChange={handleChange} />);

      expect(screen.getAllByRole('listitem', { name: 'user avatar item' })).toHaveLength(3);
    });

    it('opens the collaborators modal and removes collaborators', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/test-project-id/shift-reports/shift-report-1/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: null,
            lastEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            total: 0,
          },
          shiftReports: [],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        getApiProjectsProjectIdPeopleMockHandler(() => [
          teamMemberFactory({
            id: 2,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'Another contributor' }),
          }),
          teamMemberFactory({
            id: 3,
            role: 'contributor',
            user: userBasicDetailsFactory({ name: 'A contributor' }),
          }),
          teamMemberFactory({
            id: 4,
            role: 'admin',
            user: userBasicDetailsFactory({ name: 'An admin' }),
          }),
          teamMemberFactory({
            id: 5,
            role: 'owner',
            user: userBasicDetailsFactory({ name: 'An owner' }),
          }),
        ])
      );

      let value: number[] = [2];
      const handleChange = jest.fn((newValue) => {
        value = newValue;
      });
      const { rerender } = render(<ShiftReportCollaborators value={value} onChange={handleChange} />, {
        route,
        history,
      });

      await userEvent.click(
        await screen.findByRole('button', {
          name: 'shiftReport.collaborators.edit',
        })
      );
      await userEvent.click(await screen.findByRole('button', { name: 'delete' }));

      await waitFor(() => {
        expect(screen.getByText('shiftReport.collaboratorsModal.noCollaborators')).toBeInTheDocument();
      });

      await userEvent.click(await screen.findByRole('button', { name: 'actions.save' }));

      await waitFor(() => {
        expect(handleChange).toHaveBeenCalledWith([]);
      });

      rerender(<ShiftReportCollaborators value={value} onChange={handleChange} />);

      expect(screen.getByRole('button', { name: 'shiftReport.collaborators.addCollaborators' })).toBeInTheDocument();
    });
  });
});
