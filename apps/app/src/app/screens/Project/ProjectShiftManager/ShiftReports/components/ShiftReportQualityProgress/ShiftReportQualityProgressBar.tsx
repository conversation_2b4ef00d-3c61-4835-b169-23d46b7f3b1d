import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportQualityIndicatorsSchema } from '@shape-construction/api/src/types';
import { PopoverMenu, ProgressBar } from '@shape-construction/arch-ui';
import type { ProgressBarActiveColor } from '@shape-construction/arch-ui/src/ProgressBar/ProgressBar';
import React from 'react';
import { ShiftReportQualityDetails } from '../ShiftReportQualityDetails/ShiftReportQualityDetails';
import { type ReportQualityLabels, getLabelByReportQualityPercentage } from '../getLabelByReportQualityPercentage';

const reportQualityLabelColorMap: Record<ReportQualityLabels, ProgressBarActiveColor> = {
  notuseful: 'danger',
  thebasics: 'warning',
  good: 'primary',
  verygood: 'success',
  comprehensive: 'success',
};

type ShiftReportQualityProgressBarProps = {
  qualityIndicators: ShiftReportQualityIndicatorsSchema;
};

export const ShiftReportQualityProgressBar: React.FC<ShiftReportQualityProgressBarProps> = ({ qualityIndicators }) => {
  const messages = useMessageGetter('shiftReport.qualityLabel');
  const currentProgress = qualityIndicators.currentScore.percentage.completed;
  const qualityLabel = getLabelByReportQualityPercentage(currentProgress) || 'notuseful';

  return (
    <PopoverMenu.Root>
      <PopoverMenu.Trigger>
        <ProgressBar.Root progress={currentProgress} color={reportQualityLabelColorMap[qualityLabel]}>
          <ProgressBar.Header showProgress>
            <ProgressBar.Title>{messages('title')}</ProgressBar.Title>
          </ProgressBar.Header>
        </ProgressBar.Root>
      </PopoverMenu.Trigger>

      <PopoverMenu.Content side="bottom" align="start" sideOffset={10}>
        <div className="md:max-h-50vh overflow-y-auto">
          <ShiftReportQualityDetails qualityIndicators={qualityIndicators} />
        </div>
      </PopoverMenu.Content>
    </PopoverMenu.Root>
  );
};
