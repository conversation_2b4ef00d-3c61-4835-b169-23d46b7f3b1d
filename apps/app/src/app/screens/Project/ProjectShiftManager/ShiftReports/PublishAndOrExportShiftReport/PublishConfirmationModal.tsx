import { useMessageGetter } from '@messageformat/react';
import { Button, ConfirmationModal, IconBadge } from '@shape-construction/arch-ui';
import { QuestionMarkCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import React from 'react';

interface PublishConfirmationModalProps {
  open: boolean;
  onClose: () => void;
  handlePublish: () => void;
  handlePublishAndExport: () => void;
}

export const PublishConfirmationModal = ({
  open,
  onClose,
  handlePublish,
  handlePublishAndExport,
}: PublishConfirmationModalProps) => {
  const messages = useMessageGetter('shiftReport.publishModal');

  return (
    <ConfirmationModal.Root open={open} onClose={onClose}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="info">
            <QuestionMarkCircleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>{messages('subTitle')}</ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={onClose}>
          {messages('actions.cancel')}
        </Button>
        <Button color="primary" variant="contained" size="md" onClick={handlePublish}>
          {messages('actions.publish')}
        </Button>
        <Button color="primary" variant="contained" size="md" onClick={handlePublishAndExport}>
          {messages('actions.publishAndExport')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
