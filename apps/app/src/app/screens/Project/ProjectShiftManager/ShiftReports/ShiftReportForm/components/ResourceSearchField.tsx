import { useMessageGetter } from '@messageformat/react';
import type {
  PostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationRequestSchema,
  ProjectSchema,
  ResourceKindSchema,
  ResourceSchema,
  ShiftReportSchema,
} from '@shape-construction/api/src/types';
import { Search } from '@shape-construction/arch-ui';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useClickAway } from '@shape-construction/hooks';
import { SearchLoading } from 'app/components/Search/SearchLoading';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useSearchFieldAutofocus } from 'app/pages/projects/[projectId]/weekly-planner/plan/hooks/useSearchFieldAutofocus';
import { useCreateResource, useShiftReportsResources } from 'app/queries/resources/resources';
import React, { type MouseEvent, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router';

type Params = {
  projectId: ProjectSchema['id'];
  shiftReportId: ShiftReportSchema['id'];
};

type CreateOptionButtonProps = {
  optionTerm: string;
  createOption: (keyword: string) => void;
};

const CreateOptionButton = ({ optionTerm, createOption }: CreateOptionButtonProps) => {
  const messages = useMessageGetter('shiftReport.form.resourceSearchField');

  return (
    <Search.Button
      role="button"
      className="w-full truncate border-t border-neutral-subtlest"
      onClick={() => createOption(optionTerm)}
      onMouseDown={(event: MouseEvent) => event.preventDefault()}
    >
      <PlusIcon className="w-3 h-3 mr-2" />
      {messages('createButton', { term: optionTerm })}
    </Search.Button>
  );
};

export type ResourceSearchFieldProps = {
  kind: ResourceKindSchema;
  label: string;
  value?: ResourceSchema['id'] | null;
  onChange: (value: ResourceSchema['id'] | null) => void;
  onBlur: (callback?: () => void) => void;
  name?: string;
  displayName?: string | null | undefined;
};

export const ResourceSearchField: React.FC<ResourceSearchFieldProps> = ({
  kind,
  label,
  value,
  onChange,
  onBlur,
  name,
  displayName,
}) => {
  const messages = useMessageGetter('shiftReport.form.resourceSearchField');
  const { projectId, shiftReportId } = useParams<Params>() as Params;
  const project = useCurrentProject();

  const [selectedValue, setSelectedValue] = useState<ResourceSchema | null>();
  const [searchTerm, setSearchTerm] = useState('');
  const clearSearchTerm = () => setSearchTerm('');
  const isSearchMode = Boolean(searchTerm);

  const [optionsOpen, setOptionsOpen] = useState(false);
  const toggleOptions = () => setOptionsOpen(!optionsOpen);
  const openOptions = () => setOptionsOpen(true);
  const closeOptions = () => setOptionsOpen(false);
  const searchInputRef = useRef<HTMLDivElement>(null);
  useClickAway(searchInputRef, () => {
    // Carefull when changing these lines of code.
    // This condition prevents close from happening on every click away
    if (optionsOpen) {
      closeOptions();
    }
  });

  const { shouldAutofocus, blurSearchField } = useSearchFieldAutofocus();

  const teamId = project?.currentTeamId;
  const {
    data: searchedResources,
    isLoading: isLoadingSearch,
    refetch,
  } = useShiftReportsResources(projectId, shiftReportId, kind, { search: searchTerm });
  const { mutate: createResource } = useCreateResource();

  useEffect(() => {
    if (searchedResources && selectedValue === undefined) {
      const initialSelectedValue = searchedResources.entries.find((entry) => entry.id === value);
      setSelectedValue(initialSelectedValue);
    }
  }, [searchedResources, selectedValue, value]);

  const createOption = (name: PostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationRequestSchema['name']) => {
    createResource(
      {
        projectId,
        teamId: teamId!,
        kind,
        data: { name },
      },
      {
        onSuccess: (resource) => {
          selectResource(resource);
          closeOptions();
        },
      }
    );
  };

  const resetSearchState = () => clearSearchTerm();

  const handleOnSearch = (search: string) => {
    if (search.length > 1 || search.length === 0) {
      setSearchTerm(search);
      openOptions();
    }
  };

  const handleKeyboardEvent = (event: React.KeyboardEvent<HTMLElement>) => {
    if (event.key === 'Enter' && !optionsOpen) {
      openOptions();
    } else if (['Escape', 'Tab'].includes(event.key)) {
      resetSearchState();
      closeOptions();
    }
  };

  const selectResource = (resource: ResourceSchema | null) => {
    setSelectedValue(resource);
    onChange(resource ? resource.id : null);
    resetSearchState();
  };

  const onOptionSelect = (resource: ResourceSchema | null) => {
    // If there are no results for search term, create option
    if (searchTerm && !resource && searchedResources?.entries?.length === 0) {
      createOption(searchTerm);
      return;
    }

    selectResource(resource);
    closeOptions();
  };

  const emitBlurEvent = () => {
    onBlur(() => refetch());
  };

  const clearSelection = () => {
    selectResource(null);
    closeOptions();
    emitBlurEvent();
  };

  const renderResults = () => {
    const results = searchedResources?.entries;
    const hasZeroResults = results?.length === 0;

    if (isLoadingSearch) {
      return <SearchLoading />;
    }

    if (hasZeroResults) {
      return (
        <>
          <Search.Option disabled value={null} className="bg-transparent! pb-3">
            <div className="w-full truncate">
              <span className="text-neutral-subtlest">{messages('noResults')}</span>
            </div>
          </Search.Option>
          {isSearchMode && <CreateOptionButton optionTerm={searchTerm} createOption={createOption} />}
        </>
      );
    }

    if (!results) return null;

    return (
      <>
        {results.map((resource) => (
          <Search.Option
            key={resource.id}
            value={resource}
            className="px-3 py-2"
            // onMouseDown={(e) => e.preventDefault()}
          >
            <div className="w-full truncate">
              <span className="block truncate font-medium leading-5 text-gray-900">{resource.name}</span>
            </div>
          </Search.Option>
        ))}
        {isSearchMode && <CreateOptionButton optionTerm={searchTerm} createOption={createOption} />}
      </>
    );
  };

  const searchFieldError = !selectedValue && !optionsOpen && searchTerm.length > 0 ? messages('error') : undefined;

  const getDisplayName = (option: ResourceSchema) => {
    if (searchTerm) return searchTerm;

    if (option?.name) return option.name;

    return displayName ?? '';
  };

  return (
    <Search.Root
      ref={searchInputRef}
      label={label}
      nullable
      by="id"
      value={selectedValue ?? null}
      onChange={onOptionSelect}
      onBlur={emitBlurEvent}
    >
      <div className="-mt-1">
        <Search.Field
          data-cy={`resource-search-field-${kind}`}
          readOnly={!!selectedValue}
          error={searchFieldError}
          className="w-full truncate min-w-[180px] bg-transparent focus:bg-white hover:bg-white cursor-pointer"
          placeholder={messages('placeholder')}
          displayValue={getDisplayName}
          selectedValue={selectedValue}
          onClear={clearSelection}
          onChange={handleOnSearch}
          withSearchIcon={false}
          onClick={toggleOptions}
          autoFocus={shouldAutofocus}
          onBlur={blurSearchField}
          onKeyDown={handleKeyboardEvent}
          name={name}
        />
      </div>
      {optionsOpen && (
        <div className="w-[inherit]">
          <Search.Options static className="absolute z-popover mt-1.5 min-w-[unset]">
            {renderResults()}
          </Search.Options>
        </div>
      )}
    </Search.Root>
  );
};
