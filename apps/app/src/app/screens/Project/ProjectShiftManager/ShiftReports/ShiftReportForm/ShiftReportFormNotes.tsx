import { useMessageGetter } from '@messageformat/react';
import { InputTextArea } from '@shape-construction/arch-ui';
import { useShiftReportNotesDisabled } from 'app/hooks/useShiftReportInputsDisabled';
import React from 'react';
import { FormSectionHeader } from '../components/FormSection';
import { useShiftReportFormContext } from './ShiftReportForm';

export const ShiftReportFormNotes = () => {
  const messages = useMessageGetter('shiftReport.form');
  const notesDisabled = useShiftReportNotesDisabled();
  const { register } = useShiftReportFormContext();

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <legend>
        <FormSectionHeader>{messages('notes')}</FormSectionHeader>
      </legend>

      <InputTextArea {...register('notes')} disabled={notesDisabled} rows={6} />
    </fieldset>
  );
};
