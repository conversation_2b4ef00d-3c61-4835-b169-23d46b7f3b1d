import { yupResolver } from '@hookform/resolvers/yup';
import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportSchema, TeamMemberSchema, TeamSchema } from '@shape-construction/api/src/types';
import { Button, Modal } from '@shape-construction/arch-ui';
import { ChevronLeftIcon, PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useSuspenseQuery } from '@tanstack/react-query';
import { getProjectContributorsQueryOptions } from 'app/queries/projects/people';
import React, { useCallback, useMemo } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { useParams } from 'react-router';
import * as Yup from 'yup';
import { ShiftReportAddCollaborator } from './ShiftReportAddCollaborator';
import { ShiftReportCollaboratorsList } from './ShiftReportCollaboratorsList';

export type Step = 'list' | 'add';

type Params = {
  projectId: string;
  shiftReportId: string;
};

type ShiftReportCollaboratorsModalProps = {
  teamId?: TeamSchema['id'];
  step: Step;
  setStep: (step: Step) => void;
  openModal: boolean;
  setOpenModal: (openModal: boolean) => void;
  value: ShiftReportSchema['collaboratorsTeamMemberIds'];
  onSubmit: (value: ShiftReportSchema['collaboratorsTeamMemberIds']) => void;
};

type FormValues = {
  newCollaborators: number[];
  collaborators: TeamMemberSchema[];
};

const formSchema: Yup.SchemaOf<FormValues> = Yup.object({
  newCollaborators: Yup.array(),
  collaborators: Yup.array(),
});

export const ShiftReportCollaboratorsModal = ({
  teamId,
  step,
  setStep,
  openModal,
  setOpenModal,
  value,
  onSubmit,
}: ShiftReportCollaboratorsModalProps) => {
  const messages = useMessageGetter('shiftReport.collaboratorsModal');
  const actionMessages = useMessageGetter('actions');
  const { projectId } = useParams() as Params;
  const { data: projectPeople } = useSuspenseQuery(getProjectContributorsQueryOptions(projectId));

  const defaultValues = useMemo(
    () => ({
      newCollaborators: [],
      collaborators: projectPeople.filter((person) => value.includes(person.id)),
    }),
    [projectPeople, value]
  );

  const form = useForm<FormValues>({
    defaultValues,
    resolver: yupResolver(formSchema),
  });
  const {
    fields: collaborators,
    append: appendCollaborators,
    replace: replaceCollaborators,
  } = useFieldArray({
    control: form.control,
    name: 'collaborators',
    keyName: '_id',
  });

  const { watch, reset, setValue, handleSubmit } = form;
  const newCollaborators = watch('newCollaborators');

  const allPossibleCollaborators = useMemo(
    () => projectPeople.filter((person) => !collaborators.some((collaborator) => collaborator.id === person.id)),
    [projectPeople, collaborators]
  );

  const handleAddSelectedCollaborators = useCallback(() => {
    const newCollaboratorsToAdd = allPossibleCollaborators.filter((collaborator) =>
      newCollaborators.includes(collaborator.id)
    );
    if (newCollaboratorsToAdd.length > 0) {
      appendCollaborators(newCollaboratorsToAdd);
      setValue('newCollaborators', []);
      setStep('list');
    }
  }, [allPossibleCollaborators, newCollaborators, setValue, setStep, appendCollaborators]);

  const handleDeleteCollaborator = useCallback(
    (id: TeamMemberSchema['id']) => {
      const updatedCollaborators = collaborators.filter((collaborator) => collaborator.id !== id);
      replaceCollaborators(updatedCollaborators);
    },
    [collaborators, replaceCollaborators]
  );

  const handleChangeAddCollaborator = (newValues: number[]) => {
    setValue('newCollaborators', newValues);
  };

  const submitForm = (formValues: FormValues) => {
    onSubmit(formValues.collaborators.map(({ id }) => id));
    setOpenModal(false);
  };

  const handleGoBack = () => {
    setStep('list');
  };

  const closeModal = () => {
    setOpenModal(false);
    reset(defaultValues);
  };

  const excludeFromOptions = useMemo(() => collaborators.map(({ id }) => id), [collaborators]);

  const getModalElements = () => {
    const elements = {
      title: messages('title'),
      subTitle: messages('subTitle'),
      formContent: (
        <ShiftReportCollaboratorsList collaborators={collaborators} handleDelete={handleDeleteCollaborator} />
      ),
      footerButtons: (
        <>
          <div className="mr-auto">
            <Button
              color="primary"
              variant="outlined"
              size="md"
              leadingIcon={PlusCircleIcon}
              onClick={() => {
                setStep('add');
              }}
            >
              {messages('actions.addCollaborators')}
            </Button>
          </div>
          <Button color="secondary" variant="outlined" size="md" onClick={closeModal}>
            {actionMessages('cancel')}
          </Button>
          <Button color="primary" variant="contained" size="md" type="submit" onClick={handleSubmit(submitForm)}>
            {actionMessages('save')}
          </Button>
        </>
      ),
    };
    if (step === 'add') {
      elements.title = messages('addTitle');
      elements.subTitle = messages('addSubTitle');
      elements.formContent = (
        <ShiftReportAddCollaborator
          teamId={teamId}
          excludeFromOptions={excludeFromOptions}
          value={newCollaborators}
          onChange={handleChangeAddCollaborator}
        />
      );
      elements.footerButtons = (
        <>
          <Button color="secondary" variant="outlined" size="md" leadingIcon={ChevronLeftIcon} onClick={handleGoBack}>
            {messages('actions.goBack')}
          </Button>
          <Button
            color="primary"
            variant="contained"
            size="md"
            type="submit"
            disabled={newCollaborators.length === 0}
            onClick={handleAddSelectedCollaborators}
          >
            {messages('actions.add')}
          </Button>
        </>
      );
    }
    return elements;
  };

  const modalElements = getModalElements();

  return (
    <Modal.Root open={openModal} onClose={closeModal}>
      <Modal.Header onClose={closeModal}>
        <Modal.Title>{modalElements.title}</Modal.Title>
        <Modal.SubTitle>{modalElements.subTitle}</Modal.SubTitle>
      </Modal.Header>
      <Modal.Content className="px-0 pb-px">
        <form onSubmit={handleSubmit(submitForm)} className="h-full sm:h-auto">
          {modalElements.formContent}
        </form>
      </Modal.Content>
      <Modal.Footer>{modalElements.footerButtons}</Modal.Footer>
    </Modal.Root>
  );
};
