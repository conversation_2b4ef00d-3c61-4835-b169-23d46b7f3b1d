import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Button, ConfirmationModal, IconBadge } from '@shape-construction/arch-ui';
import { InboxOutIcon } from '@shape-construction/arch-ui/src/Icons/custom';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useShiftReportRestore } from 'app/queries/shiftReports/shiftReports';
import React from 'react';
import { useLocation, useNavigate, useParams } from 'react-router';

type Params = {
  projectId: string;
};

export interface RestoreConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  shiftReportId: ShiftReportBasicDetailsSchema['id'];
  tab?: string;
}

export const RestoreConfirmationModal = ({ isOpen, onClose, shiftReportId, tab }: RestoreConfirmationModalProps) => {
  const navigate = useNavigate();
  const location = useLocation();
  const messages = useMessageGetter('shiftReport.restoreModal');
  const { projectId } = useParams<Params>() as Params;
  const { mutate: restoreShiftReport, isPending } = useShiftReportRestore();

  const handleRestore = () => {
    restoreShiftReport(
      {
        projectId,
        shiftReportId,
      },
      {
        onSuccess: () => {
          navigate(`/projects/${projectId}/shift-reports/${shiftReportId}`, {
            state: { tab: tab || location.state?.tab },
          });

          showSuccessToast({
            message: messages('feedbackMessage'),
            alignContent: 'start',
          });
        },
        onSettled: () => {
          onClose();
        },
      }
    );
  };

  return (
    <ConfirmationModal.Root open={isOpen} onClose={onClose}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="info">
            <InboxOutIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>{messages('subTitle')}</ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button
          color="secondary"
          variant="outlined"
          size="md"
          aria-label={messages('actions.cancel')}
          onClick={onClose}
          disabled={isPending}
        >
          {messages('actions.cancel')}
        </Button>
        <Button
          color="primary"
          variant="contained"
          size="md"
          aria-label={messages('actions.restore')}
          onClick={handleRestore}
          disabled={isPending}
        >
          {messages('actions.restore')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
