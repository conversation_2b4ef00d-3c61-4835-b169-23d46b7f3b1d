import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportQualityIndicatorsSchema } from '@shape-construction/api/src/types';
import { Badge, Divider } from '@shape-construction/arch-ui';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import React from 'react';
import { qualityLabelBadgeThemeMap } from '../ShiftReportQualityIndicatorsInfoTable';
import { getLabelByReportQualityPercentage } from '../getLabelByReportQualityPercentage';
import { DetailsListHeader } from './DetailsListHeader';
import { DetailsListProgressBar } from './DetailsListProgressBar';
import { DetailsListText } from './DetailsListText';

type ShiftReportQualityDetailsProps = {
  qualityIndicators: ShiftReportQualityIndicatorsSchema;
};

export const ShiftReportQualityDetails: React.FC<ShiftReportQualityDetailsProps> = ({ qualityIndicators }) => {
  const progressPopoverMessages = useMessageGetter('shiftReport.progressPopover');
  const qualityLabelMessages = useMessageGetter('shiftReport.qualityLabel');

  const currentScore = qualityIndicators.currentScore;
  const isDeactivated = currentScore.percentage.completed === 0;

  const basics = qualityIndicators.basics;
  const basicsProgress = basics.percentage;
  const basicsItems = basics.items;
  const basicsProgressFinished = basicsProgress.completed === basicsProgress.total;

  const people = qualityIndicators.people;
  const peopleProgress = people.percentage;
  const peopleProgressFinished = peopleProgress.completed === peopleProgress.total;

  const equipment = qualityIndicators.equipment;
  const equipmentProgress = equipment.percentage;
  const equipmentProgressFinished = equipmentProgress.completed === equipmentProgress.total;

  const material = qualityIndicators.material;
  const materialProgress = material.percentage;
  const materialProgressFinished = material.percentage.completed === material.percentage.total;

  const evidence = qualityIndicators.evidence;
  const evidenceProgress = evidence.percentage;
  const evidenceItems = evidence.items;
  const evidenceFinished = evidenceProgress.completed === evidenceProgress.total;
  const evidenceItemsPercentage = evidenceItems.completed ? (evidenceItems.completed * 100) / evidenceItems.total : 0;

  const allocations = qualityIndicators.allocations;
  const allocationsProgress = allocations.percentage;
  const allocationsItems = allocations.items;
  const allocationsProgressFinished = allocationsProgress.completed === allocationsProgress.total;
  const allocationsItemsPercentage = allocationsItems.completed
    ? (allocationsItems.completed * 100) / allocationsItems.total
    : 0;

  const qualityLabel = getLabelByReportQualityPercentage(currentScore.percentage.completed) ?? 'notuseful';

  return (
    <div className="px-4 py-3">
      <div className="w-full lg:w-72">
        <div className="flex py-2 justify-between items-center">
          <div className="text-sm leading-5 font-medium text-gray-700">{progressPopoverMessages('currentScore')}</div>
          <Badge label={qualityLabelMessages(qualityLabel)} theme={qualityLabelBadgeThemeMap[qualityLabel]} />
        </div>
      </div>
      <Divider orientation="horizontal" />
      <div>
        <DetailsListHeader
          title={progressPopoverMessages('basics.title')}
          progressPercentagesDescription={progressPopoverMessages('basics.titleInfo', {
            complete: basicsProgress.completed,
            total: basicsProgress.total,
          })}
          isComplete={basicsProgressFinished}
        />
        <DetailsListText text={progressPopoverMessages('basics.addReportDate')} isComplete={basicsItems.reportDate} />
        <DetailsListText text={progressPopoverMessages('basics.addReportTitle')} isComplete={basicsItems.reportTitle} />
        <DetailsListText
          text={progressPopoverMessages('basics.addWeatherDescription')}
          isComplete={basicsItems.weatherDescription}
        />
        <DetailsListText
          text={progressPopoverMessages('basics.addProgressDowntime')}
          isComplete={basicsItems.activityOrDowntime}
        />
        {isDeactivated && (
          <div className="flex py-2 space-x-1">
            <InformationCircleIcon className="h-4 w-4 text-indigo-500" />
            <p
              key={progressPopoverMessages('info.description')}
              className="text-xs leading-4 font-normal text-blue-700"
            >
              {progressPopoverMessages('info.description')}
            </p>
          </div>
        )}
      </div>
      <Divider orientation="horizontal" />
      <div>
        <DetailsListHeader
          title={progressPopoverMessages('people.title')}
          progressPercentagesDescription={progressPopoverMessages('people.titleInfo', {
            complete: peopleProgress.completed,
            total: peopleProgress.total,
          })}
          isComplete={peopleProgressFinished}
          isDeactivated={isDeactivated}
        />
        <DetailsListText
          text={progressPopoverMessages('people.addPeopleToReport')}
          isComplete={peopleProgressFinished}
          isDeactivated={isDeactivated}
        />
      </div>
      <Divider orientation="horizontal" />
      <div>
        <DetailsListHeader
          title={progressPopoverMessages('equipment.title')}
          progressPercentagesDescription={progressPopoverMessages('equipment.titleInfo', {
            complete: equipmentProgress.completed,
            total: equipmentProgress.total,
          })}
          isComplete={equipmentProgressFinished}
          isDeactivated={isDeactivated}
        />
        <DetailsListText
          text={progressPopoverMessages('equipment.addEquipmentToReport')}
          isComplete={equipmentProgressFinished}
          isDeactivated={isDeactivated}
        />
      </div>
      <Divider orientation="horizontal" />
      <div>
        <DetailsListHeader
          title={progressPopoverMessages('material.title')}
          progressPercentagesDescription={progressPopoverMessages('material.titleInfo', {
            complete: materialProgress.completed,
            total: materialProgress.total,
          })}
          isComplete={materialProgressFinished}
          isDeactivated={isDeactivated}
        />
        <DetailsListText
          text={progressPopoverMessages('material.addMaterialToReport')}
          isComplete={materialProgressFinished}
          isDeactivated={isDeactivated}
        />
      </div>
      <Divider orientation="horizontal" />
      <div>
        <DetailsListHeader
          title={progressPopoverMessages('evidence.title')}
          progressPercentagesDescription={progressPopoverMessages('evidence.titleInfo', {
            complete: evidenceProgress.completed,
            total: evidenceProgress.total,
          })}
          isComplete={evidenceFinished}
          isDeactivated={isDeactivated}
        />
        <DetailsListText
          text={progressPopoverMessages('evidence.reportsEvidenced', {
            complete: evidenceItems.completed,
            total: evidenceItems.total,
          })}
          isDeactivated={isDeactivated}
          isComplete={evidenceFinished}
        />
        <DetailsListProgressBar
          currentProgress={evidenceItemsPercentage}
          isComplete={evidenceFinished}
          isDeactivated={isDeactivated}
          description={progressPopoverMessages('evidence.reportsEvidenced', {
            complete: evidenceItems.completed,
            total: evidenceItems.total,
          })}
        />
      </div>
      <Divider orientation="horizontal" />
      <div>
        <DetailsListHeader
          title={progressPopoverMessages('allocations.title')}
          progressPercentagesDescription={progressPopoverMessages('allocations.titleInfo', {
            complete: allocationsProgress.completed,
            total: allocationsProgress.total,
          })}
          isComplete={allocationsProgressFinished}
          isDeactivated={isDeactivated}
        />
        <DetailsListText
          text={progressPopoverMessages('allocations.resourcesAllocated', {
            complete: allocationsItems.completed,
            total: allocationsItems.total,
          })}
          isDeactivated={isDeactivated}
          isComplete={allocationsProgressFinished}
        />
        <DetailsListProgressBar
          currentProgress={allocationsItemsPercentage}
          isComplete={allocationsProgressFinished}
          isDeactivated={isDeactivated}
          description={progressPopoverMessages('allocations.resourcesAllocated', {
            complete: allocationsItems.completed,
            total: allocationsItems.total,
          })}
        />
      </div>
    </div>
  );
};
