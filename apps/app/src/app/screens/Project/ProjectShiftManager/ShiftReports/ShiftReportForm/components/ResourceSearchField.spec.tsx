import { projectFactory } from '@shape-construction/api/factories/projects';
import { resourceFactory } from '@shape-construction/api/factories/resources';
import { factoryList } from '@shape-construction/api/factories/utils';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-reports';
import {} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { postApiProjectsProjectIdTeamsTeamIdResourcesKindMockHandler } from '@shape-construction/api/handlers-factories/projects/teams';
import { type ResourceListSchema, resourceKindEnum } from '@shape-construction/api/src/types';
import { createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { ResourceSearchField } from './ResourceSearchField';

describe('<ResourceSearchField />', () => {
  describe('when there is no search term', () => {
    describe('when user clicks the input field', () => {
      it('renders the list of results', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const resources = factoryList(resourceFactory, 5, (index) => ({
          id: String(index),
          name: `equipment-${index}`,
          kind: resourceKindEnum.equipment,
        }));
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(() => ({ entries: resources }))
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        render(
          <ResourceSearchField
            label="Equipment"
            kind={resourceKindEnum.equipment}
            value={null}
            onChange={jest.fn()}
            onBlur={jest.fn()}
          />,
          { history, route }
        );

        await userEvent.click(screen.getByLabelText('Equipment'));

        expect(await screen.findAllByRole('option')).toHaveLength(5);
        expect(screen.getByRole('option', { name: 'equipment-0' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-1' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-2' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-3' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-4' })).toBeInTheDocument();
      });
    });

    describe('when user press enter on input field', () => {
      it('renders the list of results', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const resources = factoryList(resourceFactory, 5, (index) => ({
          id: String(index),
          name: `equipment-${index}`,
          kind: resourceKindEnum.equipment,
        }));
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(() => ({ entries: resources }))
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        render(
          <ResourceSearchField
            label="Equipment"
            kind={resourceKindEnum.equipment}
            value={null}
            onChange={jest.fn()}
            onBlur={jest.fn()}
          />,
          { history, route }
        );

        await userEvent.click(screen.getByLabelText('Equipment'));

        expect(await screen.findAllByRole('option')).toHaveLength(5);
        expect(screen.getByRole('option', { name: 'equipment-0' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-1' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-2' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-3' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-4' })).toBeInTheDocument();

        await userEvent.type(screen.getByPlaceholderText('shiftReport.form.resourceSearchField.placeholder'), '{esc}');

        await waitFor(() => expect(screen.queryAllByRole('option')).not.toHaveLength(5));

        await userEvent.type(
          screen.getByPlaceholderText('shiftReport.form.resourceSearchField.placeholder'),
          '{enter}'
        );

        expect(await screen.findAllByRole('option')).toHaveLength(5);
        expect(screen.getByRole('option', { name: 'equipment-0' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-1' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-2' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-3' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'equipment-4' })).toBeInTheDocument();
      });
    });

    describe('when there are no results', () => {
      it('renders an empty results message', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(() => ({ entries: [] }))
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        render(
          <ResourceSearchField
            label="Equipment"
            kind={resourceKindEnum.equipment}
            value={null}
            onChange={jest.fn()}
            onBlur={jest.fn()}
          />,
          { history, route }
        );

        await userEvent.click(screen.getByLabelText('Equipment'));

        expect(await screen.findByText('shiftReport.form.resourceSearchField.noResults')).toBeInTheDocument();
      });
    });
  });

  describe('when there is a search term', () => {
    describe('when searching for resources', () => {
      it('renders the proper results', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        let resources = factoryList(resourceFactory, 5, (index) => ({
          id: String(index),
          name: `equipment-${index}`,
          kind: resourceKindEnum.equipment,
        }));
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(({ request }) => {
            resources = resources.filter((resource) =>
              resource.name.includes(new URL(request.url).searchParams.get('search') || '')
            );
            return { entries: resources };
          })
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        render(
          <ResourceSearchField
            label="Equipment"
            kind={resourceKindEnum.equipment}
            value={null}
            onChange={jest.fn()}
            onBlur={jest.fn()}
          />,
          { history, route }
        );

        await userEvent.click(screen.getByPlaceholderText('shiftReport.form.resourceSearchField.placeholder'));
        await userEvent.clear(screen.getByPlaceholderText('shiftReport.form.resourceSearchField.placeholder'));
        await userEvent.type(
          screen.getByPlaceholderText('shiftReport.form.resourceSearchField.placeholder'),
          'equipment-1'
        );

        expect(await screen.findAllByRole('option')).toHaveLength(1);
        expect(await screen.findByRole('option', { name: /equipment-1/i })).toBeInTheDocument();
      });
    });

    describe('when there are no results', () => {
      describe('when create option button is clicked', () => {
        it('selects the newly created option', async () => {
          const project = projectFactory({
            id: 'project-0',
          });
          const resource = resourceFactory({
            id: 'resource-1',
            name: 'equipment-1',
            kind: resourceKindEnum.equipment,
          });
          const resources: ResourceListSchema = { entries: [] };
          server.use(
            getApiProjectsProjectIdMockHandler(() => project),
            getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(() => resources),
            postApiProjectsProjectIdTeamsTeamIdResourcesKindMockHandler(() => resource)
          );
          const history = createMemoryHistory({
            initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
          });
          const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
          const spyOnChange = jest.fn();
          render(
            <ResourceSearchField
              label="Equipment"
              kind={resourceKindEnum.equipment}
              value={null}
              onChange={spyOnChange}
              onBlur={jest.fn()}
            />,
            { history, route }
          );

          await userEvent.click(screen.getByPlaceholderText('shiftReport.form.resourceSearchField.placeholder'));
          await userEvent.clear(screen.getByPlaceholderText('shiftReport.form.resourceSearchField.placeholder'));
          await userEvent.type(
            screen.getByPlaceholderText('shiftReport.form.resourceSearchField.placeholder'),
            'equipment-1'
          );
          await userEvent.click(
            await screen.findByRole('button', {
              name: /shiftReport.form.resourceSearchField.createButton/,
            })
          );

          await waitFor(() => {
            expect(spyOnChange).toBeCalledWith(resource.id);
          });
        });
      });

      describe('when user presses Enter', () => {
        it('creates a new option and calls onChange', async () => {
          const project = projectFactory({
            id: 'project-0',
          });
          const newCreatedResource = resourceFactory({
            id: 'new-resource-0',
            name: 'New non-existant Resource',
            kind: resourceKindEnum.equipment,
          });
          let resources = factoryList(resourceFactory, 5, (index) => ({
            id: String(index),
            name: `equipment-${index}`,
            kind: resourceKindEnum.equipment,
          }));
          server.use(
            getApiProjectsProjectIdMockHandler(() => project),
            getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(({ request }) => {
              resources = resources.filter((resource) =>
                resource.name.includes(new URL(request.url).searchParams.get('search') || '')
              );
              return { entries: resources };
            }),
            postApiProjectsProjectIdTeamsTeamIdResourcesKindMockHandler(() => newCreatedResource)
          );
          const history = createMemoryHistory({
            initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
          });
          const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
          const spyOnChange = jest.fn();
          const spyOnBlur = jest.fn();
          render(
            <ResourceSearchField
              label="Equipment"
              kind={resourceKindEnum.equipment}
              value={null}
              onChange={spyOnChange}
              onBlur={spyOnBlur}
            />,
            { history, route }
          );
          await userEvent.type(screen.getByRole('combobox', { name: 'Equipment' }), 'New non-existant Resource');
          await screen.findByRole('button', {
            name: /shiftReport.form.resourceSearchField.createButton/,
          });

          await userEvent.keyboard('[Enter]');

          await waitFor(() => {
            expect(spyOnChange).toHaveBeenCalledWith(newCreatedResource.id);
          });
        });
      });
    });
  });

  describe('when user selects an option', () => {
    it('calls the onChange handler with correct parameters', async () => {
      const project = projectFactory({
        id: 'project-0',
      });
      let resources = factoryList(resourceFactory, 5, (index) => ({
        id: String(index),
        name: `equipment-${index}`,
        kind: resourceKindEnum.equipment,
      }));
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(({ request }) => {
          resources = resources.filter((resource) =>
            resource.name.includes(new URL(request.url).searchParams.get('search') || '')
          );
          return { entries: resources };
        })
      );
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
      });
      const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
      const spyOnChange = jest.fn();
      render(
        <ResourceSearchField
          label="Equipment"
          kind={resourceKindEnum.equipment}
          value={null}
          onChange={spyOnChange}
          onBlur={jest.fn()}
        />,
        {
          history,
          route,
        }
      );

      await userEvent.type(
        screen.getByPlaceholderText('shiftReport.form.resourceSearchField.placeholder'),
        'equipment-2'
      );
      await userEvent.click(await screen.findByRole('option', { name: 'equipment-2' }));

      expect(spyOnChange).toHaveBeenCalledWith('2');
    });
  });

  describe('when user clicks on the clear button', () => {
    it('removes the selected option', async () => {
      const project = projectFactory({
        id: 'project-0',
      });
      let resources = factoryList(resourceFactory, 5, (index) => ({
        id: String(index),
        name: `equipment-${index}`,
        kind: resourceKindEnum.equipment,
      }));
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(({ request }) => {
          resources = resources.filter((resource) =>
            resource.name.includes(new URL(request.url).searchParams.get('search') || '')
          );
          return { entries: resources };
        })
      );
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
      });
      const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
      render(
        <ResourceSearchField
          label="Equipment"
          kind={resourceKindEnum.equipment}
          value="0"
          onChange={jest.fn()}
          onBlur={jest.fn()}
        />,
        {
          history,
          route,
        }
      );

      expect(await screen.findByDisplayValue('equipment-0')).toBeInTheDocument();
      await userEvent.click(screen.getByRole('button', { name: 'Clear' }));

      await waitFor(() => expect(screen.queryByDisplayValue('equipment-0')).not.toBeInTheDocument());
    });
  });

  describe('when displayName is provided', () => {
    describe('when resource is not loaded (pagination)', () => {
      it('displays the displayName as a fallback', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(() => ({ entries: [] }))
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        render(
          <ResourceSearchField
            label="Equipment"
            kind={resourceKindEnum.equipment}
            value="0"
            onChange={jest.fn()}
            onBlur={jest.fn()}
            displayName="Bobcat DIS-93"
          />,
          { history, route }
        );

        expect(screen.getByDisplayValue('Bobcat DIS-93')).toBeInTheDocument();
      });
    });

    describe('when value exists and resource is found in search results', () => {
      it('selects API resource name over displayName', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const resources = [
          resourceFactory({
            id: '0',
            name: 'Bobcat API-93',
            kind: resourceKindEnum.equipment,
          }),
        ];
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(() => ({ entries: resources }))
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };

        render(
          <ResourceSearchField
            label="Equipment"
            kind={resourceKindEnum.equipment}
            value="0"
            onChange={jest.fn()}
            onBlur={jest.fn()}
            displayName="Bobcal DIS-93"
          />,
          { history, route }
        );

        await waitFor(() => {
          expect(screen.getByDisplayValue('Bobcat API-93')).toBeInTheDocument();
        });
      });
    });

    describe('when resource has undefined name', () => {
      it('falls back to displayName', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const resources = [
          resourceFactory({
            id: '0',
            name: undefined,
            kind: resourceKindEnum.equipment,
          }),
        ];
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(() => ({ entries: resources }))
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/shift-report-0`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        render(
          <ResourceSearchField
            label="Equipment"
            kind={resourceKindEnum.equipment}
            value="0"
            displayName="Bobcal DIS-93"
            onChange={jest.fn()}
            onBlur={jest.fn()}
          />,
          { history, route }
        );

        await waitFor(() => {
          expect(screen.getByDisplayValue('Bobcal DIS-93')).toBeInTheDocument();
        });
      });
    });
  });
});
