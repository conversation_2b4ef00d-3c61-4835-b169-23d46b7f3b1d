import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportSchema } from '@shape-construction/api/src/types';
import { Table } from '@shape-construction/arch-ui';
import { fullLongLocationPath } from 'app/components/Utils/locations';
import { useProjectLocations } from 'app/queries/projects/locations';
import classNames from 'clsx';
import React from 'react';
import { FormSectionHeader } from '../components/FormSection';
import { ResourcesDocumentsPreview } from './components/ResourcesDocumentsPreview';
import { ShiftActivityPreview } from './components/ShiftActivityPreview';

type ShiftReportActivityProps = {
  shiftReport: ShiftReportSchema;
};

export const ShiftReportActivity = ({
  shiftReport: { activities, projectId, id: shiftReportId },
}: ShiftReportActivityProps) => {
  const messages = useMessageGetter('shiftReport.form');

  const { data: locations } = useProjectLocations(projectId!);

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <legend>
        <FormSectionHeader className="mb-4">{messages('progress')}</FormSectionHeader>
      </legend>

      <div
        className="overflow-auto rounded-md shadow ring-1 ring-black/5
         [&_div]:overflow-visible [&_div]:shadow-none [&_div]:ring-0"
      >
        <Table.Container>
          <Table>
            <Table.Heading>
              <Table.Row className="[&_th]:pl-3 [&_th]:normal-case">
                <Table.Header>{messages('description')}</Table.Header>
                <Table.Header>{messages('location')}</Table.Header>
                <Table.Header>{messages('progress')}</Table.Header>
                <Table.Header>{messages('units')}</Table.Header>
                <Table.Header>{messages('plannedUnplanned')}</Table.Header>
                <Table.Header>{messages('comment')}</Table.Header>
              </Table.Row>
            </Table.Heading>
            <Table.Body className=" [&_td]:text-gray-800">
              {activities.length ? (
                activities.map((activity) => {
                  const { id, description, quantity, units, planned, comment, locationId, shiftActivityId } = activity;

                  return (
                    <React.Fragment key={id}>
                      <Table.Row
                        className={classNames({
                          'last:border-b-0': !shiftActivityId,
                        })}
                      >
                        <Table.Cell className="whitespace-pre-line">{description}</Table.Cell>
                        <Table.Cell className="whitespace-pre-line">
                          {fullLongLocationPath(locations, locationId)}
                        </Table.Cell>
                        <Table.Cell>{quantity}</Table.Cell>
                        <Table.Cell>{units}</Table.Cell>
                        <Table.Cell>{planned}</Table.Cell>
                        <Table.Cell className="whitespace-pre-line">{comment}</Table.Cell>
                      </Table.Row>
                      {!!activity.documentCount && (
                        <Table.Row>
                          <Table.Cell colSpan={6} width={100} className="pt-0 [&_div]:overflow-x-auto">
                            <ResourcesDocumentsPreview
                              projectId={projectId}
                              shiftReportId={shiftReportId}
                              resource={activity}
                              resourceType="activities"
                            />
                          </Table.Cell>
                        </Table.Row>
                      )}
                      <ShiftActivityPreview shiftActivityId={shiftActivityId} projectId={projectId!} />
                    </React.Fragment>
                  );
                })
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={5}>
                    <span className="text-gray-400">{messages('noEntries')}</span>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </Table.Container>
      </div>
    </fieldset>
  );
};
