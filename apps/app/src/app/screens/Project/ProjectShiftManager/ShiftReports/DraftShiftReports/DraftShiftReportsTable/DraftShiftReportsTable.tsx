import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { Table } from '@shape-construction/arch-ui';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { keepPreviousData } from '@tanstack/react-query';
import { usePagination } from 'app/hooks/usePagination';
import { useProject } from 'app/queries/projects/projects';
import { useShiftReportsDraft } from 'app/queries/shiftReports/shiftReports';
import React from 'react';
import { useParams } from 'react-router';
import { ShiftReportsPlaceholder } from '../../components/ShiftReportsPlaceholder';
import { ShiftReportsTableSkeleton } from '../../components/ShiftReportsTableSkeleton';
import { SortableTableHeader } from '../../components/SortableTableHeader';
import { TablePagination } from '../../components/TablePagination';
import { DraftShiftReportsTableRow } from './DraftShiftReportsTableRow';

type Params = {
  projectId: ProjectSchema['id'];
};

export const DraftShiftReportsTable = () => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const { projectId } = useParams<Params>() as Params;
  const { data: projectData } = useProject(projectId);
  const pagination = usePagination();
  const params = { after: pagination.after, before: pagination.before };
  const { data: shiftReportsDraftData, isLoading } = useShiftReportsDraft(projectId, params, {
    query: { placeholderData: keepPreviousData },
  });
  const tableMessages = useMessageGetter('shiftReport.list.table');

  if (isLoading) return <ShiftReportsTableSkeleton />;

  if (!shiftReportsDraftData || !projectData) return null;

  if (!isLoading && !shiftReportsDraftData.shiftReports?.length) {
    return <ShiftReportsPlaceholder status="draft" />;
  }

  const tableBody = (
    <Table.Body className="[&_tr]:cursor-pointer [&_tr]:border-b [&_tr]:bg-white [&_tr]:last:border-b-0">
      {shiftReportsDraftData.shiftReports.map((rowData) => (
        <DraftShiftReportsTableRow
          key={rowData.id}
          rowData={rowData}
          project={projectData}
          isLargeScreen={isLargeScreen}
        />
      ))}
    </Table.Body>
  );

  const tablePagination = (
    <TablePagination
      meta={shiftReportsDraftData.meta}
      count={shiftReportsDraftData.shiftReports.length}
      onNext={pagination.onNext}
      onPrevious={pagination.onPrevious}
    />
  );

  return (
    <Table.Container data-cy="draft-shift-reports" className="container max-w-7xl">
      <Table>
        {isLargeScreen && (
          <Table.Heading>
            <Table.Row>
              <SortableTableHeader label={tableMessages('date')} />
              <SortableTableHeader label={tableMessages('shift')} />
              <SortableTableHeader label={tableMessages('author')} />
              <SortableTableHeader label={tableMessages('team')} />
              <SortableTableHeader label={tableMessages('collaborators')} />
              <Table.Header />
            </Table.Row>
          </Table.Heading>
        )}
        {tableBody}
        {tablePagination}
      </Table>
    </Table.Container>
  );
};
