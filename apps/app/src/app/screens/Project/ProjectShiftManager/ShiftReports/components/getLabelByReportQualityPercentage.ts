type ReportQualityPercentageTreshold = 0 | 20 | 40 | 60 | 80;
export type ReportQualityLabels = 'notuseful' | 'thebasics' | 'good' | 'verygood' | 'comprehensive';

const qualityLabelFloorPercentageMap: Record<ReportQualityPercentageTreshold, ReportQualityLabels> = {
  0: 'notuseful',
  20: 'thebasics',
  40: 'good',
  60: 'verygood',
  80: 'comprehensive',
};

const reversedQualityLabelFloorPercentageMapEntries = Object.entries(qualityLabelFloorPercentageMap).reverse();

export const getLabelByReportQualityPercentage = (percentage: number) => {
  if (percentage < 0 || percentage > 100) return null;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [_, color] = reversedQualityLabelFloorPercentageMapEntries.find(
    ([threshold]) => percentage >= Number.parseInt(threshold, 10)
  ) || [null, 'notuseful']; // fallback to make typescript happy. Should never be reached.

  return color;
};
