import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportSchema } from '@shape-construction/api/src/types';
import { LogoPreview } from 'app/components/UI/LogoPreview/LogoPreview';
import React from 'react';
import { FormSectionHeader, FormSectionLabelValue } from '../components/FormSection';
import { ProjectName } from '../components/ProjectName';

type ShiftReportProjectDetailsProps = {
  shiftReport: ShiftReportSchema;
};

export const ShiftReportProjectDetails = ({
  shiftReport: {
    projectNumber,
    contractorName,
    internalDocumentReferenceNumber,
    clientDocumentReferenceNumber,
    contractorLogo,
  },
}: ShiftReportProjectDetailsProps) => {
  const messages = useMessageGetter('shiftReport.form');
  return (
    <fieldset className="grid grid-cols-1 gap-6 md:grid-cols-3">
      <legend>
        <FormSectionHeader className="mb-4">{messages('projectDetails')}</FormSectionHeader>
      </legend>

      <ProjectName />
      <FormSectionLabelValue label={messages('projectNumber')} value={projectNumber} />
      <div className="hidden md:block" />

      <FormSectionLabelValue label={messages('teamName')} value={contractorName} />
      <div className="block text-sm font-medium text-gray-700">
        {messages('teamLogo')}
        <div className="relative mt-1 flex items-center">
          <LogoPreview url={contractorLogo?.url} className="w-16 h-16 max-w-16 mr-3" />
        </div>
      </div>
      <div className="hidden lg:block" />

      <FormSectionLabelValue label={messages('docRefNumberInternal')} value={internalDocumentReferenceNumber} />
      <FormSectionLabelValue label={messages('docRefNumberClient')} value={clientDocumentReferenceNumber} />
    </fieldset>
  );
};
