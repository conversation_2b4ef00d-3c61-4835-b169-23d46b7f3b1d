import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { sharedCursorPaginationMetaFactory } from '@shape-construction/api/factories/sharedCursorPagination';
import { shiftReportsListItemFactory } from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdShiftReportsDraftMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-reports';
import {
  getApiProjectsProjectIdPeopleMockHandler,
  getApiProjectsProjectIdPeopleTeamMemberIdMockHandler,
  getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/team-members';
import { createMemoryHistory } from 'history';

import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, waitFor, waitForElementToBeRemoved } from 'tests/test-utils';
import { DraftShiftReportsTable } from './DraftShiftReportsTable';

describe('DraftShiftReportsTable', () => {
  describe('when there are draft shift reports', () => {
    it('renders draft shift reports table rows', async () => {
      const project = projectFactory({
        id: 'project-0',
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsDraftMockHandler(() => ({
          shiftReports: [shiftReportsListItemFactory()],
          meta: sharedCursorPaginationMetaFactory(),
        })),
        getApiProjectsProjectIdPeopleMockHandler(() => {
          const projectId = project.id;
          const teamMembers = [
            teamMemberFactory({
              id: 1,
              projectId,
              status: 'joined',
              user: userBasicDetailsFactory({ name: 'John Doe' }),
            }),
          ];
          return teamMembers;
        })
      );

      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/drafts'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:tab' };

      render(<DraftShiftReportsTable />, { history, route });

      expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.date' })).toBeInTheDocument();
      expect(await screen.findAllByRole('cell', { name: 'shift reports date' })).toHaveLength(1);
    });
    it('renders table with collaborators', async () => {
      const project = projectFactory({
        id: 'project-0',
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsDraftMockHandler(() => ({
          shiftReports: [
            shiftReportsListItemFactory({
              teamMemberId: 1,
              collaboratorsTeamMemberIds: [2, 3],
            }),
          ],
          meta: sharedCursorPaginationMetaFactory(),
        })),
        getApiProjectsProjectIdPeopleMockHandler(() => {
          const projectId = project.id;
          const teamMembers = [
            teamMemberFactory({
              id: 1,
              projectId,
              user: userBasicDetailsFactory({ name: 'John Doe' }),
            }),
            teamMemberFactory({
              id: 2,
              projectId,
              user: userBasicDetailsFactory({ name: 'Shape User' }),
            }),
          ];
          return teamMembers;
        })
      );

      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/drafts'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:tab' };

      render(<DraftShiftReportsTable />, { history, route });

      await screen.findByRole('columnheader', { name: 'shiftReport.list.table.collaborators' });
      expect(await screen.findAllByRole('listitem', { name: 'user avatar item' })).toHaveLength(2);
    });
    describe('when draft shift reports has removed author', () => {
      it('renders draft shift reports with removed author', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const removedTeamMember1 = teamMemberFactory({
          id: 1,
          projectId: project.id,
          status: 'joined',
          user: undefined,
        });
        const teamMember2 = teamMemberFactory({
          id: 2,
          projectId: project.id,
          status: 'joined',
          user: userBasicDetailsFactory({ name: 'John Doe' }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsDraftMockHandler(() => ({
            shiftReports: [
              shiftReportsListItemFactory({
                id: 'shift-report-1',
                teamMemberId: 1,
              }),
              shiftReportsListItemFactory({
                id: 'shift-report-2',
                teamMemberId: 2,
              }),
            ],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => [removedTeamMember1, teamMember2]),
          getApiProjectsProjectIdPeopleTeamMemberIdMockHandler(({ params }) => {
            const teamMember = params.teamMemberId === '1' ? removedTeamMember1 : teamMember2;
            return teamMember;
          })
        );

        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/drafts'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:tab' };

        render(<DraftShiftReportsTable />, { history, route });

        await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));

        expect(await screen.findAllByRole('cell', { name: 'shift reports reporter' })).toHaveLength(2);
        expect(await screen.findByRole('presentation', { name: 'Generic user' })).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.list.table.removedUser')).toBeInTheDocument();
      });
    });
  });
  describe('when there is no draft shift reports', () => {
    describe('when user has permission to create a report', () => {
      it('renders shift reports placeholder with create report button', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsDraftMockHandler(() => ({
            shiftReports: [],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/drafts'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:tab' };

        render(<DraftShiftReportsTable />, { history, route });

        await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));
        await waitFor(() =>
          expect(screen.queryByRole('columnheader', { name: 'shiftReport.list.table.date' })).not.toBeInTheDocument()
        );
        expect(await screen.findByRole('button', { name: 'shiftReport.new.title' })).toBeInTheDocument();
        expect(await screen.findByText(/shiftReport.list.placeholder.info/)).toBeInTheDocument();
        expect(await screen.findByText(/shiftReport.list.placeholder.cta/)).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.list.placeholder.noReportsInProgress')).toBeInTheDocument();
      });
    });
    describe('when user does not have permission to create a report', () => {
      it('renders shift reports placeholder with no create report button', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: false,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsDraftMockHandler(() => ({
            shiftReports: [],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
        );

        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/drafts'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:tab' };

        render(<DraftShiftReportsTable />, { history, route });

        expect(await screen.findByText('shiftReport.list.placeholder.infoViewer')).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.list.placeholder.noReportsViewer')).toBeInTheDocument();
        await waitFor(() => {
          expect(screen.queryByRole('button', { name: 'shiftReport.new.title' })).not.toBeInTheDocument();
        });
        await waitFor(() => {
          expect(screen.queryByText(/shiftReport.list.placeholder.cta/)).not.toBeInTheDocument();
        });
      });
    });
  });
});
