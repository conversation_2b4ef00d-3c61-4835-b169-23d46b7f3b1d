import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { sharedCursorPaginationMetaFactory } from '@shape-construction/api/factories/sharedCursorPagination';
import {
  shiftReportsAvailableActions,
  shiftReportsListItemFactory,
} from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdShiftReportsMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-reports';
import {
  getApiProjectsProjectIdPeopleMockHandler,
  getApiProjectsProjectIdPeopleTeamMemberIdMockHandler,
  getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/team-members';
import { createMemoryHistory } from 'history';

import React from 'react';
import { server } from 'tests/mock-server';
import { createMatchMedia, render, screen, waitFor, waitForElementToBeRemoved } from 'tests/test-utils';
import { PublishedShiftReportsTable } from './PublishedShiftReportsTable';

describe('PublishedShiftReportsTable', () => {
  describe('when there are published shift reports', () => {
    describe('when user has permission to create a report', () => {
      it('renders published shift reports table rows', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [shiftReportsListItemFactory()],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => {
            const projectId = project.id;
            const teamMembers = [
              teamMemberFactory({
                id: 1,
                projectId,
                status: 'joined',
                user: userBasicDetailsFactory({ name: 'John Doe' }),
              }),
            ];
            return teamMembers;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports'],
        });
        const route = { path: '/projects/:projectId/shift-reports' };

        render(<PublishedShiftReportsTable />, { history, route });

        await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));
        expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.date' })).toBeInTheDocument();
        expect(await screen.findAllByRole('cell', { name: 'shift reports date' })).toHaveLength(1);
        expect(screen.getByRole('button', { name: 'shiftReport.list.table.actions.duplicate' })).toBeInTheDocument();
      });

      describe('when user is on a mobile device', () => {
        afterEach(() => {
          window.matchMedia = createMatchMedia();
        });

        it('renders published shift reports table rows', async () => {
          window.matchMedia = createMatchMedia(320);
          const project = projectFactory({
            id: 'project-0',
          });
          server.use(
            getApiProjectsProjectIdMockHandler(() => project),
            getApiProjectsProjectIdShiftReportsMockHandler(() => ({
              shiftReports: [shiftReportsListItemFactory()],
              meta: sharedCursorPaginationMetaFactory(),
            })),
            getApiProjectsProjectIdPeopleMockHandler(() => {
              const projectId = project.id;
              const teamMembers = [
                teamMemberFactory({
                  id: 1,
                  projectId,
                  status: 'joined',
                  user: userBasicDetailsFactory({ name: 'John Doe' }),
                }),
              ];
              return teamMembers;
            })
          );
          const history = createMemoryHistory({
            initialEntries: ['/projects/project-0/shift-reports'],
          });
          const route = { path: '/projects/:projectId/shift-reports' };

          render(<PublishedShiftReportsTable />, { history, route });

          await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));
          expect(screen.getByRole('button', { name: 'shift-report-actions-dropdown-button' })).toBeInTheDocument();
        });
      });
    });

    describe('when user has permission to archive a report', () => {
      it('renders published shift reports table rows with dropdown menu', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({ createShiftReport: false }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [
              shiftReportsListItemFactory({
                availableActions: shiftReportsAvailableActions({
                  archive: true,
                  restore: false,
                }),
              }),
            ],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => {
            const projectId = project.id;
            const teamMembers = [
              teamMemberFactory({
                id: 1,
                projectId,
                status: 'joined',
                user: userBasicDetailsFactory({ name: 'John Doe' }),
              }),
            ];
            return teamMembers;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports'],
        });
        const route = { path: '/projects/:projectId/shift-reports' };

        render(<PublishedShiftReportsTable />, { history, route });

        await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));
        expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.date' })).toBeInTheDocument();
        expect(await screen.findAllByRole('cell', { name: 'shift reports date' })).toHaveLength(1);
        expect(screen.getByRole('button', { name: 'shiftReport.list.table.actions.archive' })).toBeInTheDocument();
      });
    });

    describe('when user does not have permission to create neither to archive a report', () => {
      it('renders published shift reports table rows with no dropdown button', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({ createShiftReport: false }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [
              shiftReportsListItemFactory({
                availableActions: shiftReportsAvailableActions({
                  archive: false,
                  restore: false,
                }),
              }),
            ],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => {
            const projectId = project.id;
            const teamMembers = [
              teamMemberFactory({
                id: 1,
                projectId,
                status: 'joined',
                user: userBasicDetailsFactory({ name: 'John Doe' }),
              }),
            ];
            return teamMembers;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports'],
        });
        const route = { path: '/projects/:projectId/shift-reports' };

        render(<PublishedShiftReportsTable />, { history, route });

        expect(await screen.findAllByRole('cell', { name: 'shift reports date' })).toHaveLength(1);
        expect(
          screen.queryByRole('button', { name: 'shiftReport.list.table.actions.archive' })
        ).not.toBeInTheDocument();
        expect(
          screen.queryByRole('button', { name: 'shiftReport.list.table.actions.duplicate' })
        ).not.toBeInTheDocument();
      });
    });

    describe('when published shift reports has removed author', () => {
      it('renders published shift reports with removed author', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const removedTeamMember1 = teamMemberFactory({
          id: 1,
          projectId: project.id,
          status: 'joined',
          user: undefined,
        });
        const teamMember2 = teamMemberFactory({
          id: 2,
          projectId: project.id,
          status: 'joined',
          user: userBasicDetailsFactory({ name: 'John Doe' }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [
              shiftReportsListItemFactory({
                id: 'shift-report-1',
                teamMemberId: 1,
              }),
              shiftReportsListItemFactory({
                id: 'shift-report-2',
                teamMemberId: 2,
              }),
            ],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => [removedTeamMember1, teamMember2]),
          getApiProjectsProjectIdPeopleTeamMemberIdMockHandler(({ params }) => {
            const teamMember = params.teamMemberId === '1' ? removedTeamMember1 : teamMember2;
            return teamMember;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports'],
        });
        const route = { path: '/projects/:projectId/shift-reports' };

        render(<PublishedShiftReportsTable />, { history, route });

        await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));

        expect(await screen.findAllByRole('cell', { name: 'shift reports reporter' })).toHaveLength(2);
        expect(await screen.findByRole('presentation', { name: 'Generic user' })).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.list.table.removedUser')).toBeInTheDocument();
      });
    });
  });

  describe('when there is no published shift reports', () => {
    describe('when user has permission to create a report', () => {
      it('renders shift reports placeholder with create report button', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
        );

        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports'],
        });
        const route = { path: '/projects/:projectId/shift-reports' };

        render(<PublishedShiftReportsTable />, { history, route });

        await waitFor(() =>
          expect(screen.queryByRole('columnheader', { name: 'shiftReport.list.table.date' })).not.toBeInTheDocument()
        );
        expect(await screen.findByRole('button', { name: 'shiftReport.new.title' })).toBeInTheDocument();
        expect(await screen.findByText(/shiftReport.list.placeholder.info/)).toBeInTheDocument();
        expect(await screen.findByText(/shiftReport.list.placeholder.cta/)).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.list.placeholder.noReports')).toBeInTheDocument();
      });
    });

    describe('when user does not have permission to create a report', () => {
      it('renders shift reports placeholder with no create report button', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: false,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
        );

        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports'],
        });
        const route = { path: '/projects/:projectId/shift-reports' };

        render(<PublishedShiftReportsTable />, { history, route });

        expect(await screen.findByText('shiftReport.list.placeholder.infoViewer')).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.list.placeholder.noReportsViewer')).toBeInTheDocument();
        await waitFor(() => {
          expect(screen.queryByRole('button', { name: 'shiftReport.new.title' })).not.toBeInTheDocument();
        });
        await waitFor(() => {
          expect(screen.queryByText(/shiftReport.list.placeholder.cta/)).not.toBeInTheDocument();
        });
      });
    });
  });
});
