import { issueFactory } from '@shape-construction/api/factories/issues';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { shiftReportsFactory } from '@shape-construction/api/factories/shiftReports';
import { factoryList } from '@shape-construction/api/factories/utils';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdIssuesMockHandler } from '@shape-construction/api/handlers-factories/projects/issues';
import { createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen } from 'tests/test-utils';
import { IssueSearchField } from './IssueSearchField';

describe('IssueSearchField', () => {
  describe('when there is no search term', () => {
    describe('when user clicks the input field', () => {
      it('renders the list of paginated results', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const shiftReport = shiftReportsFactory();
        let shiftIssues = factoryList(issueFactory, 5, (index) => ({
          id: String(index),
          title: `title-${index}`,
          referenceNumber: `A000${index}`,
        }));
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdIssuesMockHandler(async ({ request }) => {
            const url = new URL(request.url);
            shiftIssues = shiftIssues.filter((issue) => issue.title?.includes(url.searchParams.get('search') || ''));
            return {
              issues: shiftIssues,
              meta: {
                currentPage: 1,
                pageSize: shiftIssues.length,
                totalEntries: shiftIssues.length,
                totalPages: 2,
              },
            };
          })
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/${shiftReport.id}`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        const { user } = render(<IssueSearchField linkShiftIssue={() => {}} />, { history, route });

        await user.click(screen.getByPlaceholderText('shiftReport.form.searchIssuePlaceholder'));

        expect(await screen.findAllByRole('option')).toHaveLength(6); // new issue link is an option
        expect(screen.getByRole('option', { name: 'title-0 A0000' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'title-1 A0001' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'title-2 A0002' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'title-3 A0003' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'title-4 A0004' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'shiftReport.form.createNewIssueLink' })).toBeInTheDocument();
        expect(
          screen.getByRole('button', { name: 'load-more-shiftReport.form.searchIssueSuffix' })
        ).toBeInTheDocument();
      });
    });
  });

  describe('when there is a search term', () => {
    describe('when searching for title', () => {
      it('renders the proper results', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const shiftReport = shiftReportsFactory();
        let shiftIssues = factoryList(issueFactory, 5, (index) => ({
          id: String(index),
          title: `title-${index}`,
          referenceNumber: `A000${index}`,
        }));
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdIssuesMockHandler(async ({ request }) => {
            const url = new URL(request.url);
            shiftIssues = shiftIssues.filter((issue) => issue.title?.includes(url.searchParams.get('search') || ''));
            return {
              issues: shiftIssues,
              meta: {
                currentPage: 1,
                pageSize: shiftIssues.length,
                totalEntries: shiftIssues.length,
                totalPages: 1,
              },
            };
          })
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/${shiftReport.id}`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        const { user } = render(<IssueSearchField linkShiftIssue={() => {}} />, { history, route });

        await user.clear(screen.getByPlaceholderText('shiftReport.form.searchIssuePlaceholder'));
        await user.type(screen.getByPlaceholderText('shiftReport.form.searchIssuePlaceholder'), 'title-1');

        expect(await screen.findAllByRole('option')).toHaveLength(2);
        expect(screen.getByRole('option', { name: 'title-1 A0001' })).toBeInTheDocument();
        expect(screen.getByRole('option', { name: 'shiftReport.form.createNewIssueLink' })).toBeInTheDocument();
        expect(
          screen.queryByRole('button', { name: 'load-more-shiftReport.form.searchIssueSuffix' })
        ).not.toBeInTheDocument();
      });
    });

    describe('when searching for reference code', () => {
      it('renders the proper results', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const shiftReport = shiftReportsFactory();
        let shiftIssues = factoryList(issueFactory, 5, (index) => ({
          id: String(index),
          title: `title-${index}`,
          referenceNumber: `A000${index}`,
        }));
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdIssuesMockHandler(async ({ request }) => {
            const url = new URL(request.url);
            shiftIssues = shiftIssues.filter((issue) =>
              issue.referenceNumber?.includes(url.searchParams.get('search') || '')
            );
            return {
              issues: shiftIssues,
              meta: {
                currentPage: 1,
                pageSize: shiftIssues.length,
                totalEntries: shiftIssues.length,
                totalPages: 1,
              },
            };
          })
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/${shiftReport.id}`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        const { user } = render(<IssueSearchField linkShiftIssue={() => {}} />, { history, route });

        await user.clear(screen.getByPlaceholderText('shiftReport.form.searchIssuePlaceholder'));
        await user.type(screen.getByPlaceholderText('shiftReport.form.searchIssuePlaceholder'), 'A0002');

        expect(await screen.findAllByRole('option')).toHaveLength(2);
        expect(screen.getByRole('option', { name: 'title-2 A0002' })).toBeInTheDocument();
        expect(
          screen.queryByRole('button', { name: 'load-more-shiftReport.form.searchIssueSuffix' })
        ).not.toBeInTheDocument();
      });
    });

    describe('and there are no results', () => {
      it('renders an empty results message', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const shiftReport = shiftReportsFactory();
        let shiftIssues = factoryList(issueFactory, 5, (index) => ({
          id: String(index),
          title: `title-${index}`,
          referenceNumber: `A000${index}`,
        }));
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdIssuesMockHandler(async ({ request }) => {
            const url = new URL(request.url);
            shiftIssues = shiftIssues.filter((issue) =>
              issue.referenceNumber?.includes(url.searchParams.get('search') || '')
            );
            return {
              issues: shiftIssues,
              meta: {
                currentPage: 1,
                pageSize: shiftIssues.length,
                totalEntries: shiftIssues.length,
                totalPages: 1,
              },
            };
          })
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/shift_reports/${shiftReport.id}`],
        });
        const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
        const { user } = render(<IssueSearchField linkShiftIssue={() => {}} />, { history, route });

        await user.clear(screen.getByPlaceholderText('shiftReport.form.searchIssuePlaceholder'));
        await user.type(screen.getByPlaceholderText('shiftReport.form.searchIssuePlaceholder'), 'title-6');

        expect(await screen.findByText('search.noResults.title')).toBeInTheDocument();
        expect(await screen.findByText('search.noResults.subTitle')).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.form.createNewIssueLink')).toBeInTheDocument();
        expect(
          screen.queryByRole('button', { name: 'load-more-shiftReport.form.searchIssueSuffix' })
        ).not.toBeInTheDocument();
      });
    });
  });

  describe('when user selects an issue', () => {
    it('calls linkShiftIssue', async () => {
      const project = projectFactory({
        id: 'project-0',
      });
      const shiftReport = shiftReportsFactory();
      let shiftIssues = factoryList(issueFactory, 5, (index) => ({
        id: String(index),
        title: `title-${index}`,
        referenceNumber: `A000${index}`,
      }));
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdIssuesMockHandler(async ({ request }) => {
          const url = new URL(request.url);
          shiftIssues = shiftIssues.filter((issue) =>
            issue.referenceNumber?.includes(url.searchParams.get('search') || '')
          );
          return {
            issues: shiftIssues,
            meta: {
              currentPage: 1,
              pageSize: shiftIssues.length,
              totalEntries: shiftIssues.length,
              totalPages: 1,
            },
          };
        })
      );
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/shift_reports/${shiftReport.id}`],
      });
      const route = { path: '/projects/:projectId/shift_reports/:shiftReportId' };
      const spyOnLinkShiftIssue = jest.fn();
      const { user } = render(<IssueSearchField linkShiftIssue={spyOnLinkShiftIssue} />, { history, route });

      await user.type(screen.getByPlaceholderText('shiftReport.form.searchIssuePlaceholder'), 'A0002');
      await user.click(await screen.findByRole('option', { name: 'title-2 A0002' }));

      expect(spyOnLinkShiftIssue).toHaveBeenCalledWith('2');
    });
  });
});
