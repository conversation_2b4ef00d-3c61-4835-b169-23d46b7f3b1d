import { useMessageGetter } from '@messageformat/react';
import { Button, ConfirmationModal, IconBadge } from '@shape-construction/arch-ui';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import React from 'react';
import { renderMarkup } from 'react-render-markup';

interface OverrideWarningModalProps {
  report: string;
  open: boolean;
  onClose: () => void;
  onSubmit: () => void;
}

export const OverrideWarningModal = ({ report, open, onClose, onSubmit }: OverrideWarningModalProps) => {
  const messages = useMessageGetter('shiftReport.modal');

  return (
    <ConfirmationModal.Root open={open} onClose={onClose}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="danger">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('overrideTitle')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>
          <p className="mb-2">{renderMarkup(messages('actionMessage', { reportTitle: report }))}</p>
          <p>{messages('warningMessage')}</p>
        </ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={onClose}>
          {messages('actions.cancel')}
        </Button>
        <Button color="danger" variant="contained" size="md" onClick={onSubmit}>
          {messages('actions.override')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
