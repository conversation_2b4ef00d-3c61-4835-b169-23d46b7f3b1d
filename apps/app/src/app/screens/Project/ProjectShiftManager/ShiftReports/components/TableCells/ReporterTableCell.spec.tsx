import { userBasicDetailsFactory, userFactory } from '@shape-construction/api/factories/users';
import React from 'react';
import { render, screen } from 'tests/test-utils';
import { ReporterTableCell } from './ReporterTableCell';

const TestingTable: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <table>
    <tbody>{children}</tbody>
  </table>
);

const userBasicDetails = userBasicDetailsFactory({
  id: 'user-0',
  avatarUrl: 'avatarUrl',
  firstName: '<PERSON>',
  lastName: '<PERSON><PERSON>',
  name: '<PERSON>',
});
const user = userFactory(userBasicDetails);

describe('ReporterTableCell', () => {
  describe('when I am the reporter', () => {
    it('renders (You) text', async () => {
      const reporter = userBasicDetails;

      render(
        <TestingTable>
          <tr>
            <ReporterTableCell user={reporter} />
          </tr>
        </TestingTable>,
        { user }
      );

      expect(screen.getByRole('cell', { name: 'shift reports reporter' })).toHaveTextContent('<PERSON> (You)');
    });
  });

  describe('when I am not the reporter', () => {
    it('does not render (You) text', async () => {
      const reporter = userBasicDetailsFactory({
        id: 'user-1',
        avatarUrl: 'avatarUrl',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Doe',
        name: 'Jane Doe',
      });

      render(
        <TestingTable>
          <tr>
            <ReporterTableCell user={reporter} />
          </tr>
        </TestingTable>,
        { user }
      );

      expect(screen.getByRole('cell', { name: 'shift reports reporter' })).toHaveTextContent('Jane Doe');
    });
  });
});
