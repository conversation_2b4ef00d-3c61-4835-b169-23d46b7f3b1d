import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, ShiftReportCompletionSchema } from '@shape-construction/api/src/types';
import { StackedList, StackedListItem } from '@shape-construction/arch-ui';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import { ClipboardDocumentListIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { ChevronRightIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import type { User as AvatarUserType } from '@shape-construction/arch-ui/src/types/User';
import { formatISODateToWeekDayMonthString, getTodayISODate } from '@shape-construction/utils/DateTime';
import React from 'react';
import { Link } from 'react-router';

type CalendarDayReportsListProps = {
  project: ProjectSchema;
  date: string;
  reports: ShiftReportCompletionSchema[];
  completionAvatarUsers: AvatarUserType[];
};

export const CalendarDayReportsList: React.FC<CalendarDayReportsListProps> = ({
  project,
  date,
  reports,
  completionAvatarUsers,
}) => {
  const managerViewMessages = useMessageGetter('shiftReport.managerView');
  const hasCompletedReports = reports.length > 0;
  const todayISODate = getTodayISODate(project.timezone);

  return (
    <StackedList>
      <StackedListItem>
        <div className="w-full flex justify-center text-xs leading-none font-normal text-gray-500">
          {todayISODate === date ? managerViewMessages('today') : formatISODateToWeekDayMonthString(date)}
        </div>
      </StackedListItem>
      {hasCompletedReports ? (
        reports.map((report) => {
          const author = completionAvatarUsers.find((user) => user.id === report.authorNewId);

          return (
            <StackedListItem key={report.id}>
              <Link
                to={`/projects/${project.id}/shift-reports/${report.id}`}
                key={report.id}
                className="flex items-center justify-between w-full space-x-3"
              >
                <div className="w-full flex flex-col space-y-1 truncate">
                  <div className="text-sm leading-5 font-medium text-gray-800 truncate">{report.reportTitle}</div>
                  <div className="flex space-x-1">
                    <Avatar size="xs" text={author?.name!} imgURL={author?.avatarUrl} />
                    <div className="text-xs leading-4 font-normal text-gray-700">{author?.name}</div>
                  </div>
                </div>
                <ChevronRightIcon width={20} height={20} className="text-gray-400 shrink-0" />
              </Link>
            </StackedListItem>
          );
        })
      ) : (
        <StackedListItem>
          <div className="flex-1 flex flex-col items-center justify-center space-y-4">
            <ClipboardDocumentListIcon width={48} height={48} className="text-gray-400" />
            <div className="text-sm leading-5 font-normal text-gray-500">
              {managerViewMessages('reportsList.emptyList')}
            </div>
          </div>
        </StackedListItem>
      )}
    </StackedList>
  );
};
