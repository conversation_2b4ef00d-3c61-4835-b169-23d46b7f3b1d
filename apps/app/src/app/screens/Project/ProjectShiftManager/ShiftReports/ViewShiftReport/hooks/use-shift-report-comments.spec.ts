import { shiftReportCommentFactory } from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory, userFactory } from '@shape-construction/api/factories/users';
import {
  getApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler,
  postApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import type { ShiftReportCommentSchema } from '@shape-construction/api/src/types';
import { buildQueryClient } from 'app/queries/query.client.builder';
import { delay } from 'msw';
import { richTextFactory } from 'tests/factories/richTextFactory';
import { server } from 'tests/mock-server';
import { act, renderHook, waitFor } from 'tests/test-utils';
import { useShiftReportComments, useShiftReportCommentsActions } from './use-shift-report-comments';

const userBasicDetails = userBasicDetailsFactory({ id: 'user-0' });
const user = userFactory(userBasicDetails);

type InterceptorsProps = {
  comments: ShiftReportCommentSchema[];
};

const setupInterceptors = ({ comments }: InterceptorsProps) => {
  const mockedComments: ShiftReportCommentSchema[] = [...comments];

  server.use(
    getApiProjectsProjectIdPeopleMockHandler(() => [teamMemberFactory({ user: userBasicDetails })]),
    postApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler(async () => {
      await delay(100);
      const comment = shiftReportCommentFactory();
      mockedComments.push(comment);

      return comment;
    }),
    getApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMockHandler(() => ({
      comments: mockedComments,
      meta: {
        total: mockedComments.length,
        firstEntryCursor: 'first-cursor',
        lastEntryCursor: 'last-cursor',
        hasNextPage: false,
        hasPreviousPage: false,
      },
    }))
  );
};

describe('useShiftReportComments', () => {
  it('returns the comments flattened', async () => {
    setupInterceptors({
      comments: [shiftReportCommentFactory({ id: 'comment-0' }), shiftReportCommentFactory({ id: 'comment-1' })],
    });

    const { result: queryResult } = renderHook(() =>
      useShiftReportComments('project-0', 'shiftReport-0', 'collaborators')
    );

    await waitFor(() => expect(queryResult.current.data).toHaveLength(2));
  });
});

describe('when sending a message', () => {
  it('handles comment status when succeeds', async () => {
    setupInterceptors({ comments: [] });

    const queryClient = buildQueryClient();
    const { result: queryResult } = renderHook(
      () => useShiftReportComments('project-0', 'shiftReport-0', 'collaborators'),
      { queryClient }
    );
    const { result: mutationResult } = renderHook(
      () => useShiftReportCommentsActions('project-0', 'shiftReport-0', 'collaborators'),
      { user, queryClient }
    );

    await waitFor(() => expect(mutationResult.current.createComment).toBeDefined());

    act(() => {
      mutationResult.current.createComment.mutate({
        plain_text: 'Example',
        rich_text: richTextFactory('Example'),
      });
    });

    await waitFor(() => expect(queryResult.current.data).toHaveLength(1));
    await waitFor(() => expect(queryResult.current.data[0].status).toBe('sending'));
    await waitFor(() => expect(queryResult.current.data[0].status).toBe(undefined));
  });
});
