import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Table } from '@shape-construction/arch-ui';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { keepPreviousData } from '@tanstack/react-query';
import { usePagination } from 'app/hooks/usePagination';
import { useProject } from 'app/queries/projects/projects';
import { useShiftReports } from 'app/queries/shiftReports/shiftReports';
import React, { useState } from 'react';
import { useParams } from 'react-router';
import { ArchiveConfirmationModal } from '../../components/ArchiveConfirmationModal/ArchiveConfirmationModal';
import { ShiftReportsPlaceholder } from '../../components/ShiftReportsPlaceholder';
import { ShiftReportsTableSkeleton } from '../../components/ShiftReportsTableSkeleton';
import { SortableTableHeader } from '../../components/SortableTableHeader';
import { TablePagination } from '../../components/TablePagination';
import { PublishedShiftReportsTableRow } from './PublishedShiftReportsTableRow';

type Params = {
  projectId: ProjectSchema['id'];
};

export const PublishedShiftReportsTable = () => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const { projectId } = useParams<Params>() as Params;
  const { data: projectData } = useProject(projectId);
  const pagination = usePagination();
  const params = { after: pagination.after, before: pagination.before };
  const [reportId, setReportId] = useState<ShiftReportBasicDetailsSchema['id']>('');
  const [openArchiveModal, setOpenArchiveModal] = useState<boolean>(false);
  const { data: shiftReportsData, isLoading } = useShiftReports(projectId, params, {
    query: { placeholderData: keepPreviousData },
  });
  const tableMessages = useMessageGetter('shiftReport.list.table');

  if (isLoading) return <ShiftReportsTableSkeleton />;

  if (!shiftReportsData || !projectData) return null;

  if (!isLoading && !shiftReportsData.shiftReports?.length) {
    return <ShiftReportsPlaceholder status="published" />;
  }

  const handleOpenArchiveModal = (rowDataId: ShiftReportBasicDetailsSchema['id']) => {
    setReportId(rowDataId);
    setOpenArchiveModal(true);
  };

  const tableBody = (
    <Table.Body className="[&_tr]:cursor-pointer [&_tr]:border-b [&_tr]:bg-white [&_tr]:last:border-b-0">
      {shiftReportsData.shiftReports.map((rowData) => (
        <PublishedShiftReportsTableRow
          key={rowData.id}
          rowData={rowData}
          project={projectData}
          isLargeScreen={isLargeScreen}
          handleOpenArchiveModal={handleOpenArchiveModal}
        />
      ))}
    </Table.Body>
  );

  const tablePagination = (
    <TablePagination
      meta={shiftReportsData.meta}
      count={shiftReportsData.shiftReports.length}
      onNext={pagination.onNext}
      onPrevious={pagination.onPrevious}
    />
  );

  return (
    <>
      <Table.Container data-cy="published-shift-reports" className="container max-w-7xl">
        <Table>
          {isLargeScreen && (
            <Table.Heading>
              <Table.Row>
                <SortableTableHeader label={tableMessages('date')} />
                <SortableTableHeader label={tableMessages('shift')} />
                <SortableTableHeader label={tableMessages('author')} />
                <SortableTableHeader label={tableMessages('team')} />
                <Table.Header />
              </Table.Row>
            </Table.Heading>
          )}
          {tableBody}
          {tablePagination}
        </Table>
      </Table.Container>
      <ArchiveConfirmationModal
        isOpen={openArchiveModal}
        onClose={() => setOpenArchiveModal(false)}
        shiftReportId={reportId}
      />
    </>
  );
};
