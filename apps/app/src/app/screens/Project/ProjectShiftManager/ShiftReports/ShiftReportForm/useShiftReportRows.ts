import { useUpdateShiftReport } from 'app/queries/shiftReports/shiftReports';
import {
  type ShiftReportFormValues,
  type ShiftReportRowsPathname,
  useShiftReportFormContext,
} from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/ShiftReportForm';
import { useCallback } from 'react';
import { type FieldArrayWithId, useFieldArray } from 'react-hook-form';
import { useParams } from 'react-router';
import { transformPathnameIntoObjectWithEndValue } from './formUtils';

type Params = {
  projectId: string;
  shiftReportId: string;
};

type FieldRows<T extends ShiftReportRowsPathname> = (FieldArrayWithId<ShiftReportFormValues, T, 'uuid'> & {
  id: string;
})[];

export const useShiftReportRows = <T extends ShiftReportRowsPathname>(pathname: T) => {
  const { projectId, shiftReportId } = useParams<Params>() as Params;
  const { mutate: updateShiftReport, isPending: isAddingRow } = useUpdateShiftReport();

  const { control, getValues } = useShiftReportFormContext();
  // We defined the keyName as uuid to prevent overriding the server state id
  const fieldArray = useFieldArray({ control, name: pathname, keyName: 'uuid' });
  const fieldRows = fieldArray.fields as FieldRows<T>;

  const handleAddRow = useCallback(
    (defaultValues?: any) => {
      updateShiftReport({
        projectId,
        shiftReportId,
        data: transformPathnameIntoObjectWithEndValue(pathname, getValues(), [{ ...defaultValues }]),
      });
    },
    [getValues, pathname, projectId, shiftReportId, updateShiftReport]
  );

  const handleAddRows = useCallback(
    (defaultRows: any[]) => {
      updateShiftReport({
        projectId,
        shiftReportId,
        data: transformPathnameIntoObjectWithEndValue(pathname, getValues(), [...defaultRows]),
      });
    },
    [getValues, pathname, projectId, shiftReportId, updateShiftReport]
  );

  const handleDeleteRows = useCallback(() => {
    const rowsToDelete = getValues(pathname)?.map((row) => ({ id: row.id, _destroy: 'true' })) || [];

    updateShiftReport({
      projectId,
      shiftReportId,
      data: transformPathnameIntoObjectWithEndValue(pathname, getValues(), rowsToDelete),
    });
  }, [getValues, pathname, projectId, shiftReportId, updateShiftReport]);

  return {
    isAddingRow,
    fieldRows,
    handleAddRow,
    handleAddRows,
    handleDeleteRows,
  };
};
