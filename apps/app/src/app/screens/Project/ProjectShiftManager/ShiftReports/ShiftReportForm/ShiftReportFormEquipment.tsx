import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportEquipmentSchema } from '@shape-construction/api/src';
import { InputNumber, InputText } from '@shape-construction/arch-ui';
import { THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import { WrenchScrewdriverIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useShiftReportRow } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRow';
import { useShiftReportRows } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRows';
import React, { useMemo } from 'react';
import { Controller } from 'react-hook-form';
import { useParams } from 'react-router';
import { FormSectionHeader } from '../components/FormSection';
import { ShiftReportLineItemsDocuments } from '../components/ShiftReportDocuments/ShiftReportLineItemsDocuments';
import { useShiftReportsResourcesDocuments } from '../components/ShiftReportDocuments/hooks/useShiftReportsResourcesDocuments';
import { ActivityLinkFormRow } from './ActivityLinkFormRow';
import { DownTimeLinkFormRow } from './DownTimeLinkFormRow';
import {
  type EquipmentItem,
  type ShiftReportRowPathname,
  type ShiftReportRowsPathname,
  useShiftReportFormContext,
} from './ShiftReportForm';
import type { FocusSection } from './autofocusSectionAtom';
import { FormBlock } from './components/FormBlock';
import { FormBlockActions } from './components/FormBlockActions';
import { MultipleFormRows } from './components/MultipleFormRows';
import { ResourceSearchField } from './components/ResourceSearchField';
import { useShiftReportFormAutofocus } from './hooks/useShiftReportFormAutofocus';
import { useResourceLinks } from './useResourceLinks';

const pathname = 'equipments';
const FOCUS_SECTION: FocusSection = pathname;
const FOCUS_SECTION_ACTIVITIES: FocusSection = 'activities';
const FOCUS_SECTION_DOWNTIME: FocusSection = 'down_times';

type EquipmentFormRowProps = {
  field: EquipmentItem & Partial<Pick<ShiftReportEquipmentSchema, 'description'>>;
  index: number;
  path: Extract<ShiftReportRowPathname, `equipments.${number}`>;
  last?: boolean;
};

export const EquipmentFormRow: React.FC<EquipmentFormRowProps> = ({
  field: equipment,
  index: rowIndex,
  path,
  last,
}) => {
  const messages = useMessageGetter('shiftReport.form');
  const { projectId, shiftReportId } = useParams<{ projectId: string; shiftReportId: string }>();
  const { control, submitForm } = useShiftReportFormContext();
  const { user, register, handleDeleteRow, getValues } = useShiftReportRow(path);
  const { activities: activityLinks, down_times: downTimeLinks } = equipment;

  const activitiesPath: ShiftReportRowsPathname = `${path}.activities`;
  const {
    fieldRows: activityRows,
    handleAddRow: handleAddActivityRow,
    isAddingRow: isAddingActivityRow,
  } = useShiftReportRows(activitiesPath);

  const downTimesPath: ShiftReportRowsPathname = `${path}.down_times`;
  const {
    fieldRows: downTimeRows,
    handleAddRow: handleAddDownTimeRow,
    isAddingRow: isAddingDownTimeRow,
  } = useShiftReportRows(downTimesPath);

  const { activityLinksNum, downTimeLinksNum, disableHours } = useResourceLinks({
    activityLinks,
    downTimeLinks,
    rowIndex,
    path,
  });

  const { shouldAutofocus, setAutofocusSection } = useShiftReportFormAutofocus(FOCUS_SECTION, last);

  const { attachmentsBadge, displayDocumentsGallery, documents, deleteDocument } = useShiftReportsResourcesDocuments({
    projectId,
    shiftReportId,
    resource: equipment,
    resourceType: pathname,
    fieldDocumentCount: getValues(`${path}.document_count` as any) || 0,
  });

  const activityLinkLabel = messages('allocateProgress');
  const activityBadgeLabel = messages('progressBadge', { activityLinksNum });
  const allocateDowntimeLabel = messages('allocateDowntime');

  const badges = useMemo(
    () =>
      [
        {
          theme: THEME.GREEN,
          label: equipment.hours ? `${equipment.hours}h` : '',
        },
        {
          theme: THEME.BLUE,
          label: activityLinksNum > 0 ? activityBadgeLabel : '',
        },
        {
          theme: THEME.PINK,
          label: downTimeLinksNum > 0 ? messages('downTimeBadge', { downTimeLinksNum }) : '',
        },
        attachmentsBadge || { label: '' },
      ].filter((b) => !!b.label),
    [activityLinksNum, downTimeLinksNum, equipment.hours, messages, activityBadgeLabel, attachmentsBadge]
  );

  const allocateActions = [
    {
      label: activityLinkLabel,
      onClick: (e: any) => {
        setAutofocusSection(FOCUS_SECTION_ACTIVITIES);
        handleAddActivityRow();
      },
      icon: PlusCircleIcon,
      isLoading: isAddingActivityRow,
    },
    {
      label: allocateDowntimeLabel,
      onClick: (e: any) => {
        setAutofocusSection(FOCUS_SECTION_DOWNTIME);
        handleAddDownTimeRow();
      },
      icon: PlusCircleIcon,
      isLoading: isAddingDownTimeRow,
    },
  ];

  return (
    <div className="flex gap-x-2" data-testid={path}>
      <FormBlock
        rowIndex={rowIndex}
        user={user}
        onDelete={handleDeleteRow}
        title={equipment.description}
        defaultTitle={messages('equipmentDefaultTitle')}
        badges={badges}
        shouldAutofocus={shouldAutofocus}
        actions={<FormBlockActions triggerLabel={messages('allocateTriggerLabel')} items={allocateActions} />}
        rowId={equipment.id}
        rowType={pathname}
        resource={equipment}
        path={path}
      >
        <div className="grid w-full grid-cols-1 gap-y-2 gap-x-3 lg:grid-cols-12">
          <div className="lg:col-span-2">
            <InputText
              {...register(`${path}.equipment_id`)}
              label={messages('equipmentId')}
              autoFocus={shouldAutofocus}
            />
          </div>
          <div className="flex w-full flex-col gap-y-2 gap-x-3 lg:col-span-10 lg:flex-row">
            <div className="grow">
              <Controller
                control={control}
                name={`${path}.equipment_resource_id`}
                render={({ field }) => (
                  <ResourceSearchField
                    kind="equipment"
                    label={messages('description')}
                    value={field.value}
                    onChange={field.onChange}
                    onBlur={submitForm}
                    name={`${path}.equipment_resource_id`}
                    displayName={equipment?.description}
                  />
                )}
              />
            </div>
            <div className="grid grid-cols-2 gap-y-2 gap-x-3">
              <div className="min-w-[100px] lg:max-w-[100px]">
                <InputNumber {...register(`${path}.quantity`, { valueAsNumber: true })} label={messages('quantity')} />
              </div>
              <div className="min-w-[100px] lg:max-w-[100px]">
                <InputNumber
                  {...register(`${path}.hours`, {
                    valueAsNumber: true,
                  })}
                  disabled={disableHours}
                  label={messages('hours')}
                />
              </div>
            </div>
          </div>
          {displayDocumentsGallery && (
            <div className="lg:col-span-6 2xl:col-span-12" data-cy={`${pathname}.${rowIndex}.gallery`}>
              <span className="mb-1 block text-sm font-medium text-gray-700">{messages('attachments')}</span>
              <ShiftReportLineItemsDocuments
                documents={documents}
                deleteDocument={(documentId) =>
                  deleteDocument({
                    projectId: projectId!,
                    shiftReportId: shiftReportId!,
                    resourceType: pathname,
                    resourceId: equipment.id!,
                    documentId,
                  })
                }
                resourceId={equipment.id!}
                resourceType={pathname}
              />
            </div>
          )}
        </div>
        <MultipleFormRows
          textButton
          addRowLabel={activityLinkLabel}
          fields={activityRows}
          formRowComponent={ActivityLinkFormRow}
          onAddRow={() => {
            setAutofocusSection(FOCUS_SECTION_ACTIVITIES);
            handleAddActivityRow();
          }}
          isAddingRow={isAddingActivityRow}
          path={activitiesPath}
          hideAddRow={activityRows.length === 0}
        />
        <MultipleFormRows
          textButton
          addRowLabel={allocateDowntimeLabel}
          fields={downTimeRows}
          formRowComponent={DownTimeLinkFormRow}
          onAddRow={() => {
            setAutofocusSection(FOCUS_SECTION_DOWNTIME);
            handleAddDownTimeRow();
          }}
          isAddingRow={isAddingDownTimeRow}
          path={downTimesPath}
          hideAddRow={downTimeRows.length === 0}
        />
      </FormBlock>
    </div>
  );
};

export const ShiftReportFormEquipment = () => {
  const messages = useMessageGetter('shiftReport.form');
  const { fieldRows, handleAddRow, isAddingRow } = useShiftReportRows(pathname);
  const { setAutofocusSection } = useShiftReportFormAutofocus(FOCUS_SECTION);

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <legend>
        <FormSectionHeader>{messages('equipment')}</FormSectionHeader>
      </legend>
      <MultipleFormRows
        addRowLabel={messages('addEquipment')}
        showEmptyState
        icon={<WrenchScrewdriverIcon className="w-12 h-12 lg:w-14 lg:h-14" />}
        emptyDescription={messages('equipmentDescription')}
        emptySubDescription={messages('equipmentSubDescription')}
        fields={fieldRows}
        formRowComponent={EquipmentFormRow}
        onAddRow={() => {
          setAutofocusSection(FOCUS_SECTION);
          handleAddRow();
        }}
        isAddingRow={isAddingRow}
        path={pathname}
      />
    </fieldset>
  );
};
