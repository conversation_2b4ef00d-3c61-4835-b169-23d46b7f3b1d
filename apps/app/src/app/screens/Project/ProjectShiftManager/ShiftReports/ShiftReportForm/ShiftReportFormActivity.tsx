import { useMessageGetter } from '@messageformat/react';
import type { LocationSchema, ShiftActivitySchema } from '@shape-construction/api/src/types';
import { Button, InputNumber, InputSelect, InputText, InputTextArea } from '@shape-construction/arch-ui';
import { ClipboardDocumentCheckIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { MapPinIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { LinkedActivity } from 'app/components/ShiftManager/Activities/LinkedActivity/LinkedActivity';
import { truncatedLocationPath } from 'app/components/Utils/locations';
import { useShiftActivity } from 'app/queries/activities/activities';
import { useShiftReportRow } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRow';
import { useShiftReportRows } from 'app/screens/Project/ProjectShiftManager/ShiftReports/ShiftReportForm/useShiftReportRows';
import React, { useCallback, useEffect } from 'react';
import { useParams } from 'react-router';
import { ActivitiesImport } from '../components/ActivitiesImport/ActivitiesImport';
import { FormSectionHeader } from '../components/FormSection';
import { ShiftReportLineItemsDocuments } from '../components/ShiftReportDocuments/ShiftReportLineItemsDocuments';
import { useShiftReportsResourcesDocuments } from '../components/ShiftReportDocuments/hooks/useShiftReportsResourcesDocuments';
import {
  type ActivityItem,
  type ShiftReportRowPathname,
  plannedUnplannedOptions,
  unitOptions,
} from './ShiftReportForm';
import type { FocusSection } from './autofocusSectionAtom';
import { ActivityLocation } from './components/ActivityLocation';
import { ActivitySearchField } from './components/ActivitySearchField';
import { FormBlock } from './components/FormBlock';
import { MultipleFormRows } from './components/MultipleFormRows';
import { ProgressHint } from './components/ProgressHint';
import { useShiftReportFormAutofocus } from './hooks/useShiftReportFormAutofocus';

const pathname = 'activities';
const FOCUS_SECTION: FocusSection = 'activity';

type ActivitiesFormRowProps = {
  field: ActivityItem;
  index: number;
  path: Extract<ShiftReportRowPathname, `activities.${number}`>;
  last?: boolean;
};

export const ActivitiesFormRow: React.FC<ActivitiesFormRowProps> = ({
  field: activity,
  index: rowIndex,
  path,
  last,
}) => {
  const { value: isStreamliningProgressLogs } = useFeatureFlag('streamlining-progress-logs');
  const { projectId, shiftReportId } = useParams<{ projectId: string; shiftReportId: string }>();
  const messages = useMessageGetter('shiftReport.form');
  const { user, register, setValue, getValues, handleDeleteRow, locations, submitForm, getFieldState } =
    useShiftReportRow(path);

  const isActivityDirty = getFieldState(`${path}.shift_activity_id`).isDirty;

  const { data: shiftActivity, isSuccess } = useShiftActivity(projectId!, getValues(`${path}.shift_activity_id`)!);

  const setLocation = useCallback(
    (locationId: LocationSchema['id'] | null) => {
      setValue(`${path}.location_id`, locationId, { shouldDirty: true });
      submitForm();
    },
    [path, setValue, submitForm]
  );

  const prefillProgress = useCallback(
    (linkedActivity: ShiftActivitySchema) => {
      if (!getValues(`${path}.description`)) {
        setValue(`${path}.description`, linkedActivity.description, { shouldDirty: true });
      }

      if (!getValues(`${path}.location_id`)) setLocation(linkedActivity.locationId);
    },
    [getValues, path, setLocation, setValue]
  );

  const linkShiftActivity = (activityId: string) => {
    /* This bugger doesn't set the value synchronously.
     * We want to trigger the form update after the setValue updates the form.
     * We are doing it using the useEffect above.
     */
    setValue(`${path}.shift_activity_id`, activityId, {
      shouldDirty: true,
    });
  };

  const unlinkShiftActivity = () => {
    linkShiftActivity('');
    showSuccessToast({
      message: messages('unlinkToastMessage', { activityName: shiftActivity?.description }),
      alignContent: 'start',
      Actions: (
        <Button size="xs" color="white" variant="outlined" onClick={() => linkShiftActivity(shiftActivity!.id)}>
          {messages('unlinkToastUndo')}
        </Button>
      ),
    });
  };

  const locationId = getValues(`${path}.location_id`);
  const subHeader: React.ReactNode = (
    <div className="flex gap-x-1 pt-1 items-center">
      <MapPinIcon className="h-5 w-5 text-gray-400" />
      {!locationId ? (
        <span className="text-gray-400">{messages('noLocation')}</span>
      ) : (
        <span className="truncate text-xs">{truncatedLocationPath(locations, locationId)}</span>
      )}
    </div>
  );

  const { shouldAutofocus } = useShiftReportFormAutofocus(FOCUS_SECTION, last);

  const { attachmentsBadge, displayDocumentsGallery, documents, deleteDocument } = useShiftReportsResourcesDocuments({
    projectId,
    shiftReportId,
    resource: activity,
    resourceType: pathname,
    fieldDocumentCount: getValues(`${path}.document_count` as any) || 0,
  });

  const badges = attachmentsBadge ? [attachmentsBadge] : undefined;

  // Needed to trigger a form submit when activity has changed
  useEffect(() => {
    if (isActivityDirty) {
      submitForm();
    }
  }, [submitForm, isActivityDirty]);

  useEffect(() => {
    if (isSuccess) {
      prefillProgress(shiftActivity);
    }
  }, [isSuccess, prefillProgress, shiftActivity]);

  return (
    <FormBlock
      rowIndex={rowIndex}
      user={user}
      onDelete={handleDeleteRow}
      title={activity.description}
      subHeader={subHeader}
      defaultTitle={messages('activityDefaultTitle')}
      badges={badges}
      shouldAutofocus={shouldAutofocus}
      rowId={activity.id}
      rowType={pathname}
      resource={activity}
      path={path}
    >
      <div className="mb-3">
        {shiftActivity && <LinkedActivity shiftActivity={shiftActivity} unlinkActivity={unlinkShiftActivity} />}
        {!shiftActivity && <ActivitySearchField linkShiftActivity={linkShiftActivity} />}
      </div>
      <div className="grid w-full grid-cols-1 gap-y-2 gap-x-3 lg:grid-cols-2 ">
        <div className="flex w-full flex-col gap-y-2 gap-x-3 lg:col-span-1 lg:flex-row flex-wrap">
          <div className="flex w-full flex-col gap-y-2 gap-x-3 lg:flex-row flex-wrap">
            <div className="grow">
              <InputText
                {...register(`${path}.description`)}
                label={messages('description')}
                autoFocus={shouldAutofocus}
              />
            </div>
            <div className="grow">
              <label htmlFor="location_id" className="mb-1 block text-sm font-medium text-gray-700">
                {messages('location')}
                <input type="hidden" {...register(`${path}.location_id`)} />
              </label>
              <ActivityLocation
                locations={locations}
                locationId={getValues(`${path}.location_id`)}
                setLocation={setLocation}
              />
            </div>
          </div>
          {isStreamliningProgressLogs && (
            <InputSelect
              {...register(`${path}.planned`)}
              label={messages('plannedUnplanned')}
              options={plannedUnplannedOptions}
            />
          )}
          <div className="min-w-[100px] lg:max-w-[100px]">
            {isStreamliningProgressLogs ? (
              <InputNumber {...register(`${path}.quantity`, { valueAsNumber: true })} label={messages('quantity')} />
            ) : (
              <InputNumber {...register(`${path}.quantity`, { valueAsNumber: true })} label={messages('progress')} />
            )}
          </div>
          <div className="min-w-[144px]">
            <InputSelect {...register(`${path}.units`)} label={messages('units')} options={unitOptions} />
          </div>
          {!isStreamliningProgressLogs && (
            <InputSelect
              {...register(`${path}.planned`)}
              label={messages('plannedUnplanned')}
              options={plannedUnplannedOptions}
            />
          )}
        </div>
        <div className="flex w-full flex-col gap-y-2 gap-x-3 lg:col-span-1 lg:flex-row">
          <div className="grow [&_label]:h-full [&_label]:flex [&_label]:flex-col [&_label_div.relative]:grow">
            <InputTextArea {...register(`${path}.comment`)} label={messages('comment')} className="grow h-full" />
          </div>
        </div>
        {displayDocumentsGallery && (
          <div className="lg:col-span-6 2xl:col-span-12" data-cy={`${pathname}.${rowIndex}.gallery`}>
            {isStreamliningProgressLogs ? (
              <span className="mb-1 block text-sm font-medium text-gray-700">{messages('evidence')}</span>
            ) : (
              <span className="mb-1 block text-sm font-medium text-gray-700">{messages('attachments')}</span>
            )}

            <ShiftReportLineItemsDocuments
              documents={documents}
              deleteDocument={(documentId) =>
                deleteDocument({
                  projectId: projectId!,
                  shiftReportId: shiftReportId!,
                  resourceType: pathname,
                  resourceId: activity.id!,
                  documentId,
                })
              }
              resourceId={activity.id!}
              resourceType={pathname}
            />
          </div>
        )}
      </div>
    </FormBlock>
  );
};

export const ShiftReportFormActivity = () => {
  const { value: isStreamliningProgressLogs } = useFeatureFlag('streamlining-progress-logs');
  const messages = useMessageGetter('shiftReport.form');
  const { fieldRows, handleAddRow, isAddingRow } = useShiftReportRows(pathname);
  const { setAutofocusSection } = useShiftReportFormAutofocus(FOCUS_SECTION);
  const hasActivities = fieldRows.length > 0;
  return (
    <fieldset className="grid grid-cols-1 gap-6">
      {isStreamliningProgressLogs ? (
        <div className="flex justify-between">
          <legend>
            <FormSectionHeader>{messages('progress')}</FormSectionHeader>
          </legend>
          <div className="ml-auto">
            <ActivitiesImport canRemoveActivities={hasActivities} />
          </div>
        </div>
      ) : (
        <legend className="flex justify-center items-baseline gap-x-2">
          <FormSectionHeader>{messages('progress')}</FormSectionHeader>
          <ProgressHint />
        </legend>
      )}
      <MultipleFormRows
        addRowLabel={messages('addProgress')}
        showEmptyState
        icon={<ClipboardDocumentCheckIcon className="w-12 h-12 lg:w-14 lg:h-14" />}
        emptyDescription={messages('progressDescription')}
        fields={fieldRows}
        formRowComponent={ActivitiesFormRow}
        onAddRow={() => {
          setAutofocusSection(FOCUS_SECTION);
          handleAddRow();
        }}
        isAddingRow={isAddingRow}
        path={pathname}
      />
    </fieldset>
  );
};
