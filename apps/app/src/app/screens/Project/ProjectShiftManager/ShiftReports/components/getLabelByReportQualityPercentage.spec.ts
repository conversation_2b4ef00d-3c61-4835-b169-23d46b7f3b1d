import { getLabelByReportQualityPercentage } from './getLabelByReportQualityPercentage';

describe('getLabelByReportQualityPercentage', () => {
  it('returns null for a number bellow 0', () => {
    expect(getLabelByReportQualityPercentage(-1)).toBe(null);
  });

  it('returns notuseful for a number between 0 and 19', () => {
    expect(getLabelByReportQualityPercentage(0)).toBe('notuseful');
    expect(getLabelByReportQualityPercentage(19)).toBe('notuseful');
  });

  it('returns thebasics for a number between 20 and 39', () => {
    expect(getLabelByReportQualityPercentage(20)).toBe('thebasics');
    expect(getLabelByReportQualityPercentage(39)).toBe('thebasics');
  });

  it('returns good for a number between 40 and 59', () => {
    expect(getLabelByReportQualityPercentage(40)).toBe('good');
    expect(getLabelByReportQualityPercentage(59)).toBe('good');
  });

  it('returns verygood for a number between 60 and 79', () => {
    expect(getLabelByReportQualityPercentage(60)).toBe('verygood');
    expect(getLabelByReportQualityPercentage(79)).toBe('verygood');
  });

  it('returns comprehensive for a number greater than 80', () => {
    expect(getLabelByReportQualityPercentage(80)).toBe('comprehensive');
    expect(getLabelByReportQualityPercentage(100)).toBe('comprehensive');
  });

  it('returns null for a number greater than 100', () => {
    expect(getLabelByReportQualityPercentage(101)).toBe(null);
  });
});
