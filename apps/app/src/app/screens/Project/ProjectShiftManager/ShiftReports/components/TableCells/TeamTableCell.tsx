import type { TeamBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Table } from '@shape-construction/arch-ui';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import type { TableCellProps } from '@shape-construction/arch-ui/src/Table/components/TableCell';
import React from 'react';

interface TeamTableCellProps extends TableCellProps {
  displayName: TeamBasicDetailsSchema['displayName'];
}

export const TeamTableCell = ({ displayName, ...restProps }: TeamTableCellProps) => (
  <Table.Cell aria-label="shift reports team" {...restProps}>
    <div className="flex items-center gap-x-2 md:[&_div]:hidden">
      <Avatar size="xs" text={displayName || ''} />
      {displayName}
    </div>
  </Table.Cell>
);
