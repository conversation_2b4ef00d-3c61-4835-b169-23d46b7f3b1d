import { projectFactory } from '@shape-construction/api/factories/projects';
import {
  shiftReportCommentAvailableActionsFactory,
  shiftReportCommentFactory,
} from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import {
  getApiProjectsProjectIdPeopleMockHandler,
  getApiProjectsProjectIdPeopleTeamMemberIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/team-members';
import { createMemoryHistory } from 'history';

import React from 'react';
import { richTextFactory } from 'tests/factories/richTextFactory';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { ShiftReportCommentItem, type ShiftReportCommentItemProps } from './ShiftReportCommentItem';

const setupInterceptors = (interceptors: Parameters<typeof server.use> = []) => {
  const project = projectFactory({ id: 'project-0' });
  server.use(
    getApiProjectsProjectIdMockHandler(() => project),
    getApiProjectsProjectIdPeopleMockHandler(() => [teamMemberFactory()])
  );
  server.use(...interceptors);
};

const setupTest = (props: Partial<ShiftReportCommentItemProps>) => {
  const comment = shiftReportCommentFactory();
  const route = { path: '/projects/:projectId/shift-reports/:shiftReportId' };
  const history = createMemoryHistory({
    initialEntries: ['/projects/project-0/shift-reports/shift-report-0'],
  });

  return render(<ShiftReportCommentItem onDelete={jest.fn()} comment={comment} {...props} />, {
    history,
    route,
  });
};

describe('ShiftReportCommentItem', () => {
  it('renders the comment information', async () => {
    const teamMember = teamMemberFactory({
      id: 1,
      user: userBasicDetailsFactory({ name: 'John Doe', avatarUrl: 'avatar-url' }),
    });
    const comment = shiftReportCommentFactory({
      createdAt: '2000-01-01T20:00:00.000Z',
      richText: richTextFactory('Check this out'),
      teamMemberId: teamMember.id,
    });
    setupInterceptors([getApiProjectsProjectIdPeopleTeamMemberIdMockHandler(() => teamMember)]);

    setupTest({ comment });

    expect(await screen.findByText('Check this out')).toBeInTheDocument();
    expect(await screen.findByText('John Doe')).toBeInTheDocument();
    expect(await screen.findByText('01-Jan-2000 20:00')).toBeInTheDocument();
    expect(await screen.findByRole('img')).toHaveAttribute('src', 'avatar-url');
  });

  describe('when user has permission to delete a comment', () => {
    it('invokes the onDelete when deleting the comment', async () => {
      const teamMember = teamMemberFactory({
        id: 1,
        user: userBasicDetailsFactory({ name: 'John Doe', avatarUrl: 'avatar-url' }),
      });
      const comment = shiftReportCommentFactory({
        createdAt: '2000-01-01 20:00',
        richText: richTextFactory('Check this out'),
        availableActions: shiftReportCommentAvailableActionsFactory({ delete: true }),
        teamMemberId: teamMember.id,
      });
      setupInterceptors([getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])]);
      const spyOnDelete = jest.fn();
      setupTest({ comment, onDelete: spyOnDelete });

      await userEvent.click(await screen.findByRole('button', { name: 'shiftReport.comments.delete' }));

      await waitFor(() => expect(spyOnDelete).toBeCalled());
    });
  });

  describe('when user does not have permission to delete a comment', () => {
    it('does not show the delete option', async () => {
      const teamMember = teamMemberFactory({
        id: 1,
        user: userBasicDetailsFactory({ name: 'John Doe', avatarUrl: 'avatar-url' }),
      });
      const comment = shiftReportCommentFactory({
        createdAt: '2000-01-01 20:00',
        richText: richTextFactory('Check this out'),
        availableActions: shiftReportCommentAvailableActionsFactory({ delete: false }),
        teamMemberId: teamMember.id,
      });
      setupInterceptors([getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])]);

      setupTest({ comment });

      expect(screen.queryByRole('button', { name: 'shiftReport.comments.delete' })).not.toBeInTheDocument();
    });
  });
});
