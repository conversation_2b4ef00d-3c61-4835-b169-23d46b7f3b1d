import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Button, ConfirmationModal, IconBadge } from '@shape-construction/arch-ui';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useShiftReportArchive } from 'app/queries/shiftReports/shiftReports';
import React from 'react';
import { useNavigate, useParams } from 'react-router';

type Params = {
  projectId: string;
};

export interface ArchiveConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  shiftReportId: ShiftReportBasicDetailsSchema['id'];
}

export const ArchiveConfirmationModal = ({ isOpen, onClose, shiftReportId }: ArchiveConfirmationModalProps) => {
  const navigate = useNavigate();
  const messages = useMessageGetter('shiftReport.archiveModal');
  const { projectId } = useParams<Params>() as Params;
  const { mutate: archiveShiftReport, isPending } = useShiftReportArchive();

  const handleArchive = () => {
    archiveShiftReport(
      {
        projectId,
        shiftReportId,
      },
      {
        onSuccess: () => {
          navigate(`/projects/${projectId}/shift-reports/${shiftReportId}`);

          showSuccessToast({
            message: messages('feedbackMessage'),
            alignContent: 'start',
          });
        },
        onSettled: () => {
          onClose();
        },
      }
    );
  };

  return (
    <ConfirmationModal.Root open={isOpen} onClose={onClose}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="danger">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>{messages('subTitle')}</ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button
          color="secondary"
          variant="outlined"
          size="md"
          aria-label={messages('actions.cancel')}
          onClick={onClose}
          disabled={isPending}
        >
          {messages('actions.cancel')}
        </Button>
        <Button
          color="danger"
          variant="contained"
          size="md"
          aria-label={messages('actions.archive')}
          onClick={handleArchive}
          disabled={isPending}
        >
          {messages('actions.archive')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
