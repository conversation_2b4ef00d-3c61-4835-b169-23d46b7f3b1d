import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { shiftReportsAvailableActions, shiftReportsFactory } from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import {
  getApiProjectsProjectIdShiftReportsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
  patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { createMemoryHistory } from 'history';

import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { ShiftReportActionDropdown } from './ShiftReportActionDropdown';

describe('ShiftReportActionDropdown', () => {
  describe('when Pre-fill button is clicked', () => {
    it('shows the warning modal', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: null,
            lastEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            total: 0,
          },
          shiftReports: [],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(<ShiftReportActionDropdown previousShiftReport={shiftReportsFactory()} onOverride={jest.fn()} />, {
        history,
        route,
      });

      userEvent.click(await screen.findByRole('button', { name: 'shift-report-actions' }));
      userEvent.click(await screen.findByRole('menuitem', { name: 'shiftReport.new.prefillFromPrevious' }));

      expect(await screen.findByText('shiftReport.modal.overrideTitle')).toBeInTheDocument();
    });
  });

  describe('when visibility is changed', () => {
    it('changes the label', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        id: 'report-1',
        visibility: 'private',
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchedShiftReport = shiftReportsFactory({
        id: 'report-1',
        visibility: 'public',
        projectNumber: 'project-0',
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: null,
            lastEntryCursor: null,
            hasNextPage: false,
            hasPreviousPage: false,
            total: 0,
          },
          shiftReports: [],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => patchedShiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(<ShiftReportActionDropdown previousShiftReport={shiftReportsFactory()} onOverride={jest.fn()} />, {
        history,
        route,
      });

      userEvent.click(await screen.findByRole('button', { name: 'shift-report-actions' }));

      expect(await screen.findByText('shiftReport.form.visibility.private')).toBeInTheDocument();
      expect(await screen.findByRole('switch', { name: 'visibility' })).toBeInTheDocument();

      userEvent.click(await screen.findByRole('switch', { name: 'visibility' }));

      expect(await screen.findByText('shiftReport.form.visibility.public')).toBeInTheDocument();
    });
  });

  describe('collaborators', () => {
    describe('when there are no collaborators', () => {
      it('displays Add collaborators button', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            meta: {
              firstEntryCursor: null,
              lastEntryCursor: null,
              hasNextPage: false,
              hasPreviousPage: false,
              total: 0,
            },
            shiftReports: [],
          })),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
          getApiProjectsProjectIdPeopleMockHandler(() => [
            teamMemberFactory({
              id: 2,
              role: 'contributor',
              user: userBasicDetailsFactory({ name: 'Another contributor' }),
            }),
            teamMemberFactory({
              id: 3,
              role: 'contributor',
              user: userBasicDetailsFactory({ name: 'A contributor' }),
            }),
            teamMemberFactory({
              id: 4,
              role: 'admin',
              user: userBasicDetailsFactory({ name: 'An admin' }),
            }),
            teamMemberFactory({
              id: 5,
              role: 'owner',
              user: userBasicDetailsFactory({ name: 'An owner' }),
            }),
          ])
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

        render(<ShiftReportActionDropdown previousShiftReport={shiftReportsFactory()} onOverride={jest.fn()} />, {
          history,
          route,
        });

        userEvent.click(await screen.findByRole('button', { name: 'shift-report-actions' }));

        expect(
          await screen.findByRole('menuitem', { name: 'shiftReport.collaborators.addCollaborators' })
        ).toBeInTheDocument();
      });

      it('opens the collaborators modal and adds new collaborators', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          id: 'report-1',
          projectId: 'project-0',
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
          collaboratorsTeamMemberIds: undefined,
        });
        const patchedShiftReport = shiftReportsFactory({
          id: 'report-1',
          projectId: 'project-0',
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
          collaboratorsTeamMemberIds: [2, 3],
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            meta: {
              firstEntryCursor: null,
              lastEntryCursor: null,
              hasNextPage: false,
              hasPreviousPage: false,
              total: 0,
            },
            shiftReports: [],
          })),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
          getApiProjectsProjectIdPeopleMockHandler(() => [
            teamMemberFactory({
              id: 2,
              role: 'contributor',
              user: userBasicDetailsFactory({ name: 'Another contributor' }),
            }),
            teamMemberFactory({
              id: 3,
              role: 'contributor',
              user: userBasicDetailsFactory({ name: 'A contributor' }),
            }),
            teamMemberFactory({
              id: 4,
              role: 'admin',
              user: userBasicDetailsFactory({ name: 'An admin' }),
            }),
            teamMemberFactory({
              id: 5,
              role: 'owner',
              user: userBasicDetailsFactory({ name: 'An owner' }),
            }),
          ]),
          patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => patchedShiftReport)
        );

        const history = createMemoryHistory({
          initialEntries: ['/projects/test-project-id/shift-reports/shift-report-1/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
        render(<ShiftReportActionDropdown previousShiftReport={shiftReportsFactory()} onOverride={jest.fn()} />, {
          history,
          route,
        });

        userEvent.click(await screen.findByRole('button', { name: 'shift-report-actions' }));
        userEvent.click(
          await screen.findByRole('menuitem', {
            name: 'shiftReport.collaborators.addCollaborators',
          })
        );
        userEvent.click(await screen.findByRole('radio', { name: /Another contributor/ }));
        userEvent.click(await screen.findByRole('radio', { name: /A contributor/ }));
        userEvent.click(await screen.findByRole('button', { name: 'shiftReport.collaboratorsModal.actions.add' }));
        userEvent.click(await screen.findByRole('button', { name: 'actions.save' }));
        userEvent.click(await screen.findByRole('button', { name: 'shift-report-actions' }));

        expect(
          await screen.findByRole('menuitem', {
            name: 'shiftReport.collaborators.editCollaborators',
          })
        ).toBeInTheDocument();

        userEvent.click(
          await screen.findByRole('menuitem', {
            name: 'shiftReport.collaborators.editCollaborators',
          })
        );

        expect(await screen.findByText(/Another contributor/)).toBeInTheDocument();
        expect(await screen.findByText(/A contributor/)).toBeInTheDocument();
      });
    });

    describe('when there are collaborators', () => {
      it('displays the edit collaborators button', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          teamMemberId: 1,
          collaboratorsTeamMemberIds: [2, 3, 4],
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            meta: {
              firstEntryCursor: null,
              lastEntryCursor: null,
              hasNextPage: false,
              hasPreviousPage: false,
              total: 0,
            },
            shiftReports: [],
          })),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
          getApiProjectsProjectIdPeopleMockHandler(() => [
            teamMemberFactory({
              id: 1,
              role: 'owner',
              user: userBasicDetailsFactory({ name: 'An owner' }),
            }),
            teamMemberFactory({
              id: 2,
              role: 'contributor',
              user: userBasicDetailsFactory({ name: 'Another contributor' }),
            }),
            teamMemberFactory({
              id: 3,
              role: 'contributor',
              user: userBasicDetailsFactory({ name: 'A contributor' }),
            }),
          ])
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
        render(<ShiftReportActionDropdown previousShiftReport={shiftReportsFactory()} onOverride={jest.fn()} />, {
          history,
          route,
        });

        await userEvent.click(await screen.findByRole('button', { name: 'shift-report-actions' }));

        expect(
          await screen.findByRole('menuitem', { name: 'shiftReport.collaborators.editCollaborators' })
        ).toBeInTheDocument();
      });

      it('opens the collaborators modal and edits collaborators', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          id: 'report-1',
          projectId: 'project-0',
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
          collaboratorsTeamMemberIds: [2, 3],
        });
        const patchedShiftReport = shiftReportsFactory({
          id: 'report-1',
          projectId: 'project-0',
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
          collaboratorsTeamMemberIds: [2, 3, 4],
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            meta: {
              firstEntryCursor: null,
              lastEntryCursor: null,
              hasNextPage: false,
              hasPreviousPage: false,
              total: 0,
            },
            shiftReports: [],
          })),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
          getApiProjectsProjectIdPeopleMockHandler(() => [
            teamMemberFactory({
              id: 2,
              role: 'contributor',
              user: userBasicDetailsFactory({ name: 'Another contributor' }),
            }),
            teamMemberFactory({
              id: 3,
              role: 'contributor',
              user: userBasicDetailsFactory({ name: 'A contributor' }),
            }),
            teamMemberFactory({
              id: 4,
              role: 'admin',
              user: userBasicDetailsFactory({ name: 'An admin' }),
            }),
            teamMemberFactory({
              id: 5,
              role: 'owner',
              user: userBasicDetailsFactory({ name: 'An owner' }),
            }),
          ]),
          patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => patchedShiftReport)
        );

        const history = createMemoryHistory({
          initialEntries: ['/projects/test-project-id/shift-reports/shift-report-1/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
        render(<ShiftReportActionDropdown previousShiftReport={shiftReportsFactory()} onOverride={jest.fn()} />, {
          history,
          route,
        });

        userEvent.click(await screen.findByRole('button', { name: 'shift-report-actions' }));
        userEvent.click(
          await screen.findByRole('menuitem', {
            name: 'shiftReport.collaborators.editCollaborators',
          })
        );
        expect(await screen.findByText(/Another contributor/)).toBeInTheDocument();
        expect(await screen.findByText(/A contributor/)).toBeInTheDocument();

        userEvent.click(
          await screen.findByRole('button', {
            name: 'shiftReport.collaboratorsModal.actions.addCollaborators',
          })
        );
        userEvent.click(await screen.findByRole('radio', { name: /An admin/ }));

        userEvent.click(await screen.findByRole('button', { name: 'shiftReport.collaboratorsModal.actions.add' }));

        expect(await screen.findByText(/Another contributor/)).toBeInTheDocument();
        expect(await screen.findByText(/A contributor/)).toBeInTheDocument();
        expect(await screen.findByText(/An admin/)).toBeInTheDocument();
      });

      it('opens the collaborators modal and removes collaborators', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          id: 'report-1',
          projectId: 'project-0',
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
          collaboratorsTeamMemberIds: [2],
        });
        const patchedShiftReport = shiftReportsFactory({
          id: 'report-1',
          projectId: 'project-0',
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: true,
          }),
          collaboratorsTeamMemberIds: undefined,
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            meta: {
              firstEntryCursor: null,
              lastEntryCursor: null,
              hasNextPage: false,
              hasPreviousPage: false,
              total: 0,
            },
            shiftReports: [],
          })),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
          getApiProjectsProjectIdPeopleMockHandler(() => [
            teamMemberFactory({
              id: 2,
              role: 'contributor',
              user: userBasicDetailsFactory({ name: 'Another contributor' }),
            }),
          ]),
          patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => patchedShiftReport)
        );

        const history = createMemoryHistory({
          initialEntries: ['/projects/test-project-id/shift-reports/shift-report-1/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
        render(<ShiftReportActionDropdown previousShiftReport={shiftReportsFactory()} onOverride={jest.fn()} />, {
          history,
          route,
        });

        userEvent.click(await screen.findByRole('button', { name: 'shift-report-actions' }));
        userEvent.click(
          await screen.findByRole('menuitem', {
            name: 'shiftReport.collaborators.editCollaborators',
          })
        );

        expect(await screen.findByText(/Another contributor/)).toBeInTheDocument();

        userEvent.click(await screen.findByRole('button', { name: 'delete' }));

        await waitFor(() => {
          expect(screen.getByText('shiftReport.collaboratorsModal.noCollaborators')).toBeInTheDocument();
        });

        userEvent.click(await screen.findByRole('button', { name: 'actions.save' }));
        userEvent.click(await screen.findByRole('button', { name: 'shift-report-actions' }));

        expect(
          await screen.findByRole('menuitem', {
            name: 'shiftReport.collaborators.addCollaborators',
          })
        ).toBeInTheDocument();
      });
    });
  });
});
