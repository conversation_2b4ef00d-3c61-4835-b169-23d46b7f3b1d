import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Table } from '@shape-construction/arch-ui';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { keepPreviousData } from '@tanstack/react-query';
import { usePagination } from 'app/hooks/usePagination';
import { useProject } from 'app/queries/projects/projects';
import { useShiftReportsArchive } from 'app/queries/shiftReports/shiftReports';
import React, { useState } from 'react';
import { useParams } from 'react-router';
import { RestoreConfirmationModal } from '../../components/RestoreConfirmationModal/RestoreConfirmationModal';
import { ShiftReportsPlaceholder } from '../../components/ShiftReportsPlaceholder';
import { ShiftReportsTableSkeleton } from '../../components/ShiftReportsTableSkeleton';
import { SortableTableHeader } from '../../components/SortableTableHeader';
import { TablePagination } from '../../components/TablePagination';
import { ArchivedShiftReportsTableRow } from './ArchivedShiftReportsTableRow';

type Params = {
  projectId: ProjectSchema['id'];
};

export const ArchivedShiftReportsTable = () => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const { projectId } = useParams<Params>() as Params;
  const { data: projectData } = useProject(projectId);
  const [reportId, setReportId] = useState<ShiftReportBasicDetailsSchema['id']>('');
  const [openRestoreModal, setOpenRestoreModal] = useState<boolean>(false);

  const pagination = usePagination();
  const params = { after: pagination.after, before: pagination.before };
  const { data: shiftReportsData, isLoading } = useShiftReportsArchive(projectId, params, {
    query: { placeholderData: keepPreviousData },
  });
  const tableMessages = useMessageGetter('shiftReport.list.table');

  if (isLoading) return <ShiftReportsTableSkeleton />;

  if (!shiftReportsData || !projectData) return null;

  if (!isLoading && !shiftReportsData.shiftReports?.length) {
    return <ShiftReportsPlaceholder status="archived" />;
  }

  const handleOpenRestoreModal = (rowDataId: ShiftReportBasicDetailsSchema['id']) => {
    setReportId(rowDataId);
    setOpenRestoreModal(true);
  };

  const tableBody = (
    <Table.Body className="[&_tr]:cursor-pointer [&_tr]:border-b [&_tr]:bg-white [&_tr]:last:border-b-0">
      {shiftReportsData.shiftReports.map((rowData) => (
        <ArchivedShiftReportsTableRow
          key={rowData.id}
          rowData={rowData}
          project={projectData}
          isLargeScreen={isLargeScreen}
          handleOpenRestoreModal={handleOpenRestoreModal}
        />
      ))}
    </Table.Body>
  );

  const tablePagination = (
    <TablePagination
      meta={shiftReportsData.meta}
      count={shiftReportsData.shiftReports.length}
      onNext={pagination.onNext}
      onPrevious={pagination.onPrevious}
    />
  );

  return (
    <>
      <Table.Container data-cy="archived-shift-reports" className="container max-w-7xl">
        <Table>
          {isLargeScreen && (
            <Table.Heading>
              <Table.Row>
                <SortableTableHeader label={tableMessages('date')} />
                <SortableTableHeader label={tableMessages('shift')} />
                <SortableTableHeader label={tableMessages('author')} />
                <SortableTableHeader label={tableMessages('team')} />
                <Table.Header />
              </Table.Row>
            </Table.Heading>
          )}
          {tableBody}
          {tablePagination}
        </Table>
      </Table.Container>
      <RestoreConfirmationModal
        isOpen={openRestoreModal}
        onClose={() => setOpenRestoreModal(false)}
        shiftReportId={reportId}
        tab="archived"
      />
    </>
  );
};
