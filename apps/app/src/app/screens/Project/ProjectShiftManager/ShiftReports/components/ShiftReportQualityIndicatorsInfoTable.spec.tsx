import React from 'react';
import { render, screen } from 'tests/test-utils';
import { ShiftReportQualityIndicatorsInfoTable } from './ShiftReportQualityIndicatorsInfoTable';

describe('ShiftReportQualityIndicatorsInfoTable', () => {
  it('renders corrctly', () => {
    render(<ShiftReportQualityIndicatorsInfoTable />);

    expect(screen.getByText('shiftReport.qualityTableInfo.title')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.qualityTableInfo.info')).toBeInTheDocument();

    expect(screen.getByText('shiftReport.qualityLabel.notuseful')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.qualityLabel.thebasics')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.qualityLabel.good')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.qualityLabel.verygood')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.qualityLabel.comprehensive')).toBeInTheDocument();

    expect(screen.getByText('shiftReport.qualityTableInfo.percentageRange.notuseful')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.qualityTableInfo.percentageRange.thebasics')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.qualityTableInfo.percentageRange.good')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.qualityTableInfo.percentageRange.verygood')).toBeInTheDocument();
    expect(screen.getByText('shiftReport.qualityTableInfo.percentageRange.comprehensive')).toBeInTheDocument();
  });
});
