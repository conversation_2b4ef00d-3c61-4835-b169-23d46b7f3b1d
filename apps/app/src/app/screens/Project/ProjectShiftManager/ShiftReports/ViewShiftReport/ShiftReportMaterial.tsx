import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportSchema } from '@shape-construction/api/src/types';
import { Table } from '@shape-construction/arch-ui';
import classNames from 'clsx';
import React from 'react';
import { FormSectionHeader } from '../components/FormSection';
import { ActivityLinkPreview } from './components/ActivityLinkPreview';
import { DownTimeLinkPreview } from './components/DownTimeLinkPreview';
import { ResourcesDocumentsPreview } from './components/ResourcesDocumentsPreview';

type ShiftReportMaterialProps = {
  shiftReport: ShiftReportSchema;
};

export const ShiftReportMaterial = ({
  shiftReport: { materials, activities, downTimes, projectId, id: shiftReportId },
}: ShiftReportMaterialProps) => {
  const messages = useMessageGetter('shiftReport.form');

  return (
    <fieldset className="grid grid-cols-1 gap-6">
      <legend>
        <FormSectionHeader className="mb-4">{messages('material')}</FormSectionHeader>
      </legend>

      <div
        className="overflow-auto rounded-md shadow ring-1 ring-black/5
         [&_div]:overflow-visible [&_div]:shadow-none [&_div]:ring-0"
      >
        <Table.Container>
          <Table>
            <Table.Heading>
              <Table.Row className="[&_th]:pl-3 [&_th]:normal-case">
                <Table.Header>{messages('description')}</Table.Header>
                <Table.Header>{messages('quantity')}</Table.Header>
                <Table.Header>{messages('units')}</Table.Header>
              </Table.Row>
            </Table.Heading>
            <Table.Body className="[&_td]:text-gray-800">
              {materials.length ? (
                materials.map((material) => {
                  const {
                    id,
                    description,
                    quantity,
                    units,
                    activities: activityLinks,
                    downTimes: downTimeLinks,
                  } = material;

                  return (
                    <React.Fragment key={id}>
                      <Table.Row
                        className={classNames({
                          'border-b last:border-b-0': !activityLinks?.length && !downTimeLinks?.length,
                        })}
                      >
                        <Table.Cell className="whitespace-pre-line">{description}</Table.Cell>
                        <Table.Cell>{quantity}</Table.Cell>
                        <Table.Cell>{units}</Table.Cell>
                      </Table.Row>
                      {!!material.documentCount && (
                        <Table.Row>
                          <Table.Cell colSpan={3} width={100} className="pt-0 [&_div]:overflow-x-auto">
                            <ResourcesDocumentsPreview
                              projectId={projectId}
                              shiftReportId={shiftReportId}
                              resource={material}
                              resourceType="materials"
                            />
                          </Table.Cell>
                        </Table.Row>
                      )}
                      <ActivityLinkPreview
                        activityLinks={activityLinks}
                        activities={activities}
                        hideBorder={!!downTimeLinks?.length}
                      />
                      <DownTimeLinkPreview downTimeLinks={downTimeLinks} downTimes={downTimes} />{' '}
                    </React.Fragment>
                  );
                })
              ) : (
                <Table.Row>
                  <Table.Cell colSpan={3}>
                    <span className="text-gray-400">{messages('noEntries')}</span>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table>
        </Table.Container>
      </div>
    </fieldset>
  );
};
