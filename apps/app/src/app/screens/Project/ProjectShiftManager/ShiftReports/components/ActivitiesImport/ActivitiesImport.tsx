import { useMessageGetter } from '@messageformat/react';
import { Button } from '@shape-construction/arch-ui';
import { useModal } from '@shape-construction/hooks';
import { AddActivitiesModal } from 'app/components/Activities/AddActivitiesModal/AddActivitiesModal';
import React from 'react';
import { useShiftReportRows } from '../../ShiftReportForm/useShiftReportRows';

const pathname = 'activities';

type ActivitiesImportProps = {
  canRemoveActivities?: boolean;
};

export const ActivitiesImport: React.FC<ActivitiesImportProps> = ({ canRemoveActivities }) => {
  const messages = useMessageGetter('shiftReport.form.linkProgress');
  const { handleAddRows, handleDeleteRows } = useShiftReportRows(pathname);
  const {
    open: isAddActiviesModalOpen,
    openModal: openAddActivitiesModal,
    closeModal: closeAddActivitiesModal,
  } = useModal(false);

  const handleLinkActivities = async (ids: string[]) => {
    const activityIds = ids.map((id) => ({ shift_activity_id: id }));

    handleAddRows(activityIds);
  };

  return (
    <div className="space-x-2">
      <Button size="md" color="primary" variant="text" onClick={handleDeleteRows} disabled={!canRemoveActivities}>
        {messages('removeAllActivities')}
      </Button>
      <Button size="md" color="primary" variant="outlined" onClick={openAddActivitiesModal}>
        {messages('linkActivities')}
      </Button>
      {isAddActiviesModalOpen && (
        <AddActivitiesModal
          onClose={closeAddActivitiesModal}
          onSelectActivities={async (ids) => handleLinkActivities(ids)}
          isSelecting={false}
        />
      )}
    </div>
  );
};
