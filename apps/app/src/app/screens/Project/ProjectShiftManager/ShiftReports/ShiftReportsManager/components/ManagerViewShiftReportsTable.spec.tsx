import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { sharedCursorPaginationMetaFactory } from '@shape-construction/api/factories/sharedCursorPagination';
import { shiftReportsListItemFactory } from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdShiftReportsMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-reports';
import {
  getApiProjectsProjectIdPeopleMockHandler,
  getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/team-members';
import * as useFiltersStateCount from 'app/components/Filters/hooks/useFiltersParams';
import { useConstructionRoles } from 'app/components/People/constructionRoles/hooks/useConstructionRoles';
import { createMemoryHistory } from 'history';

import React from 'react';
import { constructionRoleFactory } from 'tests/factories/constructionRoles';
import { server } from 'tests/mock-server';
import { render, screen, waitForElementToBeRemoved, within } from 'tests/test-utils';
import { ManagerViewShiftReportsTable } from './ManagerViewShiftReportsTable';

jest.mock('app/components/People/constructionRoles/hooks/useConstructionRoles');

const mockUseConstructionRoles = (props = {}) => {
  (useConstructionRoles as jest.Mock).mockImplementation(() => ({
    isLoading: false,
    constructionRoles: constructionRoleFactory(),
    ...props,
  }));
};

describe('<ManagerViewShiftReportsTable />', () => {
  beforeEach(() => {
    mockUseConstructionRoles();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders shift reports list', async () => {
    const project = projectFactory({
      id: 'project-0',
    });
    server.use(
      getApiProjectsProjectIdMockHandler(() => project),
      getApiProjectsProjectIdShiftReportsMockHandler(() => ({
        shiftReports: [
          shiftReportsListItemFactory({
            reportDate: '2024-03-05',
            shiftType: 'shiftType-0',
            contractorName: 'contractor',
          }),
        ],
        meta: sharedCursorPaginationMetaFactory(),
      })),
      getApiProjectsProjectIdPeopleMockHandler(() => {
        const projectId = project.id;
        const teamMembers = [
          teamMemberFactory({
            id: 1,
            projectId,
            status: 'joined',
            user: userBasicDetailsFactory({ name: 'John Doe' }),
          }),
        ];
        return teamMembers;
      })
    );
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/shift-reports/manager'],
    });
    const route = { path: '/projects/:projectId/shift-reports/manager' };

    render(<ManagerViewShiftReportsTable />, { history, route });

    await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));
    expect(await screen.findAllByRole('columnheader')).toHaveLength(6);
    expect(await screen.findAllByRole('checkbox')).toHaveLength(2); // header + 1 report
    expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.date' })).toBeInTheDocument();
    expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.shift' })).toBeInTheDocument();
    expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.author' })).toBeInTheDocument();
    expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.team' })).toBeInTheDocument();
    expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.quality' })).toBeInTheDocument();
    expect(await screen.findAllByText('05-Mar-2024')).toHaveLength(1);
    expect(await screen.findAllByText('shiftType-0')).toHaveLength(1);
    expect(await screen.findAllByText('contractor')).toHaveLength(1);
    expect(await screen.findAllByText('John Doe')).toHaveLength(1);
    expect(await screen.findAllByText('shiftReport.qualityLabel.thebasics')).toHaveLength(1);
  });

  describe('when clicking on a row', () => {
    describe('when clicking on checkbox cell', () => {
      it('does not redirect', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [shiftReportsListItemFactory()],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => {
            const projectId = project.id;
            const teamMembers = [
              teamMemberFactory({
                id: 1,
                projectId,
                status: 'joined',
                user: userBasicDetailsFactory({ name: 'John Doe' }),
              }),
            ];
            return teamMembers;
          })
        );

        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/manager'],
        });
        const route = { path: '/projects/:projectId/shift-reports/manager' };
        const { user } = render(<ManagerViewShiftReportsTable />, { history, route });

        const [firstRow] = await screen.findAllByRole('row');
        await user.click(within(firstRow).getByRole('checkbox'));

        expect(history.location.pathname).toBe('/projects/project-0/shift-reports/manager');
      });
    });

    describe('when clicking on other cells', () => {
      it('redirects to a shift reports', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [shiftReportsListItemFactory()],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => {
            const projectId = project.id;
            const teamMembers = [
              teamMemberFactory({
                id: 1,
                projectId,
                status: 'joined',
                user: userBasicDetailsFactory({ name: 'John Doe' }),
              }),
            ];
            return teamMembers;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/manager'],
        });
        const route = { path: '/projects/:projectId/shift-reports/manager' };
        const { user } = render(<ManagerViewShiftReportsTable />, { history, route });

        await user.click(await screen.findByText('John Doe'));

        expect(history.location.pathname).toBe('/projects/project-0/shift-reports/shift-report-0');
      });
    });
  });

  describe('when no shift reports are available', () => {
    it('renders shift reports list placeholder', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: false,
        }),
      });

      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          shiftReports: [],
          meta: sharedCursorPaginationMetaFactory(),
        })),
        getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
      );

      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/manager'],
      });
      const route = { path: '/projects/:projectId/shift-reports/manager' };

      render(<ManagerViewShiftReportsTable />, { history, route });

      expect(await screen.findByText('shiftReport.list.placeholder.infoViewer')).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.list.placeholder.noReportsViewer')).toBeInTheDocument();
    });
  });

  describe('manager view filters', () => {
    describe('when there are shift reports', () => {
      it('renders filters', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [shiftReportsListItemFactory()],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => {
            const projectId = project.id;
            const teamMembers = [
              teamMemberFactory({
                id: 1,
                projectId,
                status: 'joined',
                user: userBasicDetailsFactory({ name: 'John Doe' }),
              }),
            ];
            return teamMembers;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/manager'],
        });
        const route = { path: '/projects/:projectId/shift-reports/manager' };

        render(<ManagerViewShiftReportsTable />, { history, route });

        expect(
          await screen.findByRole('button', { name: /filters.toolbar.dateSelectFilter.triggerLabel/ })
        ).toBeInTheDocument();
        expect(
          await screen.findByRole('button', { name: /filters.toolbar.peopleSelectFilter.triggerLabel/ })
        ).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.managerView.actions.reportsSelected')).toBeInTheDocument();
        expect(
          await screen.findByRole('button', { name: 'shiftReport.managerView.actions.unselectAll' })
        ).toBeInTheDocument();
      });
    });

    describe('when there are no results from filtering', () => {
      it('renders empty filters placeholder', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: false,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
        );
        jest.spyOn(useFiltersStateCount, 'useFiltersStateCount').mockImplementation(() => ({
          total: 1,
          authors: 0,
          date: 1,
          format: 0,
          locations: 0,
        }));
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports'],
        });
        const route = { path: '/projects/:projectId/shift-reports' };

        render(<ManagerViewShiftReportsTable />, { history, route });

        expect(await screen.findByText('shiftReport.list.placeholder.noFiltersTitle')).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.list.placeholder.noFiltersSubtitle')).toBeInTheDocument();
      });
    });
  });

  describe('multi select', () => {
    describe('when header checkbox is selected', () => {
      it('selects all checkboxes', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [
              shiftReportsListItemFactory({
                id: 'shift-report-0',
              }),
              shiftReportsListItemFactory({
                id: 'shift-report-1',
              }),
            ],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/manager'],
        });
        const route = { path: '/projects/:projectId/shift-reports/manager' };
        const { user } = render(<ManagerViewShiftReportsTable />, { history, route });
        const [firstRow, secondRow, thirdRow] = await screen.findAllByRole('row');
        expect(within(firstRow).getByRole('checkbox')).not.toBeChecked();
        expect(within(secondRow).getByRole('checkbox')).not.toBeChecked();
        expect(within(thirdRow).getByRole('checkbox')).not.toBeChecked();

        await user.click(within(firstRow).getByRole('checkbox'));

        expect(within(firstRow).getByRole('checkbox')).toBeChecked();
        expect(within(secondRow).getByRole('checkbox')).toBeChecked();
        expect(within(thirdRow).getByRole('checkbox')).toBeChecked();
      });
    });

    describe('when row checkbox is selected', () => {
      it('selects only row checkbox', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsMockHandler(() => ({
            shiftReports: [
              shiftReportsListItemFactory({
                id: 'shift-report-0',
              }),
              shiftReportsListItemFactory({
                id: 'shift-report-1',
              }),
            ],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/manager'],
        });
        const route = { path: '/projects/:projectId/shift-reports/manager' };
        const { user } = render(<ManagerViewShiftReportsTable />, { history, route });
        const [firstRow, secondRow, thirdRow] = await screen.findAllByRole('row');
        expect(within(firstRow).getByRole('checkbox')).not.toBeChecked();
        expect(within(secondRow).getByRole('checkbox')).not.toBeChecked();
        expect(within(thirdRow).getByRole('checkbox')).not.toBeChecked();

        await user.click(within(secondRow).getByRole('checkbox'));

        expect(within(firstRow).getByRole('checkbox')).not.toBeChecked();
        expect(within(secondRow).getByRole('checkbox')).toBeChecked();
        expect(within(thirdRow).getByRole('checkbox')).not.toBeChecked();
      });
    });
  });
});
