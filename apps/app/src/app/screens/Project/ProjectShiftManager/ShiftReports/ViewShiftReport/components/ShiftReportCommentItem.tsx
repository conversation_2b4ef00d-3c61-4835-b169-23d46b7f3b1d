import { useMessage, useMessageGetter } from '@messageformat/react';
import type { DocumentSchema } from '@shape-construction/api/src/types';
import { FeedEventMessage, cn } from '@shape-construction/arch-ui';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import type { FeedEventMessageProps } from '@shape-construction/arch-ui/src/FeedEventMessage/FeedEventMessage';
import { formatDateAndTime } from '@shape-construction/utils/DateTime';
import { safeHTMLToReact } from '@shape-construction/utils/dom';
import { useSuspenseQuery } from '@tanstack/react-query';
import { generateHTML } from '@tiptap/react';
import { buildEditorExtensions } from 'app/components/CommentInput/editorExtensions';
import { GalleryViewer } from 'app/components/Gallery/GalleryViewer';
import { getProjectPersonQueryOptions } from 'app/queries/projects/people';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import React, { useMemo } from 'react';
import { useParams } from 'react-router';
import type { StatefulShiftReportComment } from '../hooks/use-shift-report-comments';

const renderComment = (comment: StatefulShiftReportComment) => {
  if (comment.richText) {
    const html = generateHTML(comment.richText, buildEditorExtensions());
    return safeHTMLToReact(html);
  }

  return comment.plainText;
};

type Params = {
  projectId: string;
  shiftReportId: string;
};

export type ShiftReportCommentItemProps = {
  comment: StatefulShiftReportComment;
  onDelete: FeedEventMessageProps['onDelete'];
};

export const ShiftReportCommentItem: React.FC<ShiftReportCommentItemProps> = ({ comment, onDelete }) => {
  const { projectId } = useParams() as Params;
  const { data: project } = useSuspenseQuery(getProjectQueryOptions(projectId));
  const { data: owner } = useSuspenseQuery(getProjectPersonQueryOptions(projectId, comment.teamMemberId));
  const deleteLabel = useMessage('shiftReport.comments.delete');
  const statusLabel = useMessageGetter('shiftReport.comments.state');

  const commentBody = useMemo(() => renderComment(comment), [comment]);

  if (!owner || !project) return null;

  const commentDate = formatDateAndTime(comment.createdAt, project.timezone);
  const canDelete = comment.availableActions?.delete;
  const documents: DocumentSchema[] =
    comment.attachments.map(({ document }) => ({
      ...document,
      availableActions: {
        delete: false,
        edit: false,
      },
    })) || [];

  return (
    <FeedEventMessage
      avatar={<Avatar text={owner.user.name} imgURL={owner.user.avatarUrl} />}
      date={commentDate}
      title={owner.user.name}
      status={comment.status}
      sendingLabel={statusLabel('sending')}
      deletingLabel={statusLabel('deleting')}
      onDelete={canDelete ? onDelete : undefined}
      deleteLabel={deleteLabel}
    >
      <div className={cn({ 'animate-pulse': comment.status === 'sending' })}>
        {commentBody}
        <GalleryViewer documents={documents} projectId={projectId} size="small" />
      </div>
    </FeedEventMessage>
  );
};
