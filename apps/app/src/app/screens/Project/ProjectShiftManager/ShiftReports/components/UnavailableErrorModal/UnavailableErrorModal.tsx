import { useMessageGetter } from '@messageformat/react';
import { Button, ConfirmationModal, IconBadge } from '@shape-construction/arch-ui';
import { ExclamationCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useModal } from '@shape-construction/hooks';
import React, { useCallback, useMemo } from 'react';
import { useNavigate, useParams } from 'react-router';

export const useUnavailableErrorModal = (): {
  open(): void;
  props: UnavailableErrorModalProps;
} => {
  const { open, openModal, closeModal } = useModal(false);
  const navigate = useNavigate();
  const { projectId } = useParams() as { projectId: string };

  const onClose = useCallback(() => {
    closeModal();
    navigate(`/projects/${projectId}/shift-reports/drafts`, {
      replace: true,
    });
  }, [closeModal, navigate, projectId]);

  return useMemo(
    () => ({
      open: openModal,
      props: { isOpen: open, onClose },
    }),
    [openModal, open, onClose]
  );
};

export interface UnavailableErrorModalProps {
  isOpen: boolean;
  onClose: () => void;
  editing?: boolean;
}

export const UnavailableErrorModal: React.FC<UnavailableErrorModalProps> = ({ isOpen, onClose, editing }) => {
  const messages = useMessageGetter('shiftReport.unavailableErrorModal');

  return (
    <ConfirmationModal.Root open={isOpen} onClose={onClose}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="info">
            <ExclamationCircleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>
          {messages('subtitle', { action: editing ? 'editing' : 'viewing' })}
        </ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button
          color="primary"
          variant="contained"
          size="md"
          aria-label={messages('actions.dismiss')}
          onClick={onClose}
        >
          {messages('actions.dismiss')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
