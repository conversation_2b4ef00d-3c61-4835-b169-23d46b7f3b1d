import { useMessageGetter } from '@messageformat/react';
import { useGetApiProjectsProjectIdIssues } from '@shape-construction/api/src/hooks';
import type { IssueSchema, ProjectSchema, ShiftActivitySchema } from '@shape-construction/api/src/types';
import { Search } from '@shape-construction/arch-ui';
import { ArrowTopRightOnSquareIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useClickAway } from '@shape-construction/hooks';
import { keepPreviousData, useInfiniteQuery } from '@tanstack/react-query';
import { NoSearchResults } from 'app/components/Search/NoSearchResults';
import { SearchLoading } from 'app/components/Search/SearchLoading';
import { SearchFieldLoadMoreButton } from 'app/components/SearchFieldLoadMoreButton/SearchFieldLoadMoreButton';
import { Link } from 'app/components/UI/Link/Link';
import { truncatedLocationPath } from 'app/components/Utils/locations';
import { useSearchFieldAutofocus } from 'app/pages/projects/[projectId]/weekly-planner/plan/hooks/useSearchFieldAutofocus';
import { getProjectIssuesInfiniteQueryOptions } from 'app/queries/issues/issues';
import { useProjectLocations } from 'app/queries/projects/locations';
import React, { useRef, useState } from 'react';
import { useLocation, useParams } from 'react-router';

const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  ESCAPE: 'Escape',
} as const;

type Params = {
  projectId: ProjectSchema['id'];
};

type IssueSearchFieldProps = {
  linkShiftIssue: (shiftIssueId: ShiftActivitySchema['id']) => void;
};

export const IssueSearchField: React.FC<IssueSearchFieldProps> = ({ linkShiftIssue }) => {
  const messages = useMessageGetter('shiftReport.form');
  const location = useLocation();
  const { projectId } = useParams<Params>() as Params;
  const { data: locations = [] } = useProjectLocations(projectId);

  const [searchTerm, setSearchTerm] = useState('');
  const clearSearchTerm = () => setSearchTerm('');
  const isSearchMode = Boolean(searchTerm);

  const [optionsOpen, setOptionsOpen] = useState(false);
  const openOptions = () => setOptionsOpen(true);
  const closeOptions = () => setOptionsOpen(false);
  const optionsRef = useRef<HTMLDivElement>(null);
  useClickAway(optionsRef, () => closeOptions());

  const { shouldAutofocus, blurSearchField } = useSearchFieldAutofocus();

  const {
    data: dataLookup,
    isLoading: isLoadingLookup,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    ...getProjectIssuesInfiniteQueryOptions(projectId),
    enabled: !isSearchMode && Boolean(projectId),
    placeholderData: keepPreviousData,
  });

  const { data: dataSearch, isLoading: isLoadingSearch } = useGetApiProjectsProjectIdIssues(
    projectId,
    {
      search: searchTerm,
    },
    {
      query: { enabled: isSearchMode && Boolean(projectId) },
    }
  );

  const handleOnBlur = () => {
    blurSearchField();
    clearSearchTerm();
    closeOptions();
  };

  const handleOnChange = (search: string) => {
    if (search.length > 1 || search.length === 0) {
      setSearchTerm(search);
      openOptions();
    }
  };

  const handleOnEnterOrEscape = (event: React.KeyboardEvent<HTMLElement>) => {
    if (event.key === KEYBOARD_KEYS.ESCAPE || event.key === KEYBOARD_KEYS.ENTER) {
      handleOnBlur();
    }
  };

  const handleOnSelect = (issue: IssueSchema) => linkShiftIssue(issue.id);

  const createIssueLink = (
    <Link
      className="flex items-center justify-center gap-x-2 my-2 text-indigo-600!"
      to={`/projects/${projectId}/issues/new`}
      state={{ background: location }}
      onMouseDown={(e) => {
        e.preventDefault();
      }}
    >
      <ArrowTopRightOnSquareIcon className="w-[16px] h-[16px]" />
      <span className="underline text-sm">{messages('createNewIssueLink')}</span>
    </Link>
  );

  const renderResults = () => {
    const results = isSearchMode ? dataSearch?.issues : dataLookup?.pages.flatMap(({ issues }) => issues);
    const isDataLoading = isSearchMode ? isLoadingSearch : isLoadingLookup;
    const hasMoreToLoad = !isSearchMode && hasNextPage;
    const hasZeroResults = results?.length === 0;

    if (isDataLoading) {
      return <SearchLoading />;
    }

    if (hasZeroResults) {
      return <NoSearchResults>{createIssueLink}</NoSearchResults>;
    }

    if (!results) return null;

    return (
      <>
        {results.map((issue) => (
          <Search.Option key={issue.id} value={issue} onMouseDown={(event) => event.preventDefault()}>
            <div className="w-full truncate">
              <span className="block truncate font-medium leading-5 text-gray-900">{issue.title}</span>
              <div className="flex gap-x-3 text-gray-500 text-xs">
                <span> {issue.referenceNumber}</span>
                {issue.locationId && <span>{truncatedLocationPath(locations, issue.locationId)}</span>}
              </div>
            </div>
          </Search.Option>
        ))}
        {hasMoreToLoad && (
          <SearchFieldLoadMoreButton
            isLoading={isFetchingNextPage}
            onClick={fetchNextPage}
            listName={messages('searchIssueSuffix')}
          />
        )}
        <Search.Option value={0}>
          <div className="w-full truncate">{createIssueLink}</div>
        </Search.Option>
      </>
    );
  };

  return (
    <div className="w-full sm:w-[400px]">
      <Search.Root onChange={handleOnSelect} label={messages('issue')}>
        <Search.Field
          className="w-full truncate"
          placeholder={messages('searchIssuePlaceholder')}
          displayValue={(issue: IssueSchema) => issue.title || ''}
          onChange={handleOnChange}
          withSearchIcon
          variant="bordered"
          autoFocus={shouldAutofocus}
          onBlur={handleOnBlur}
          onFocus={openOptions}
          onKeyUp={handleOnEnterOrEscape}
        />
        {optionsOpen && (
          <div ref={optionsRef}>
            <Search.Options static className="absolute z-popover mt-3 pt-2 pb-0">
              {renderResults()}
            </Search.Options>
          </div>
        )}
      </Search.Root>
    </div>
  );
};
