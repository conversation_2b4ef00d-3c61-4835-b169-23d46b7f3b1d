import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { sharedCursorPaginationMetaFactory } from '@shape-construction/api/factories/sharedCursorPagination';
import {
  shiftReportsAvailableActions,
  shiftReportsListItemFactory,
} from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdShiftReportsArchivedMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-reports';
import {
  getApiProjectsProjectIdPeopleMockHandler,
  getApiProjectsProjectIdPeopleTeamMemberIdMockHandler,
  getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler,
} from '@shape-construction/api/handlers-factories/projects/team-members';
import { createMemoryHistory } from 'history';

import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, waitFor, waitForElementToBeRemoved } from 'tests/test-utils';
import { ArchivedShiftReportsTable } from './ArchivedShiftReportsTable';

describe('ArchivedShiftReportsTable', () => {
  describe('when there are archived shift reports', () => {
    describe('when user has permission to restore a report', () => {
      it('renders archived shift reports table rows', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsArchivedMockHandler(() => ({
            shiftReports: [
              shiftReportsListItemFactory({
                archived: true,
                availableActions: shiftReportsAvailableActions({
                  archive: false,
                  restore: true,
                }),
              }),
            ],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => {
            const projectId = project.id;
            const teamMembers = [
              teamMemberFactory({
                id: 1,
                projectId,
                status: 'joined',
                user: userBasicDetailsFactory({ name: 'John Doe' }),
              }),
            ];
            return teamMembers;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/archived'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:tab' };

        render(<ArchivedShiftReportsTable />, { history, route });

        await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));
        expect(await screen.findByRole('columnheader', { name: 'shiftReport.list.table.date' })).toBeInTheDocument();
        expect(await screen.findAllByRole('cell', { name: 'shift reports date' })).toHaveLength(1);
        expect(screen.getByRole('button', { name: 'shiftReport.list.table.actions.restore' })).toBeInTheDocument();
      });
    });

    describe('when user does not have permission to restore a report', () => {
      it('renders archived shift reports table rows with no dropdown button', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({ createShiftReport: false }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsArchivedMockHandler(() => ({
            shiftReports: [
              shiftReportsListItemFactory({
                archived: true,
                availableActions: shiftReportsAvailableActions({
                  archive: false,
                  restore: false,
                }),
              }),
            ],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => {
            const projectId = project.id;
            const teamMembers = [
              teamMemberFactory({
                id: 1,
                projectId,
                status: 'joined',
                user: userBasicDetailsFactory({ name: 'John Doe' }),
              }),
            ];
            return teamMembers;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/archived'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:tab' };

        render(<ArchivedShiftReportsTable />, { history, route });

        expect(await screen.findAllByRole('cell', { name: 'shift reports date' })).toHaveLength(1);
        expect(
          screen.queryByRole('button', { name: 'shiftReport.list.table.actions.restore' })
        ).not.toBeInTheDocument();
      });
    });

    describe('when archived shift reports has removed author', () => {
      it('renders archived shift reports with removed author', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        const teamMember1 = teamMemberFactory({
          id: 1,
          projectId: project.id,
          status: 'joined',
          user: undefined,
        });
        const teamMember2 = teamMemberFactory({
          id: 2,
          projectId: project.id,
          status: 'joined',
          user: userBasicDetailsFactory({ name: 'John Doe' }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsArchivedMockHandler(() => ({
            shiftReports: [
              shiftReportsListItemFactory({
                id: 'shift-report-1',
                teamMemberId: 1,
                archived: true,
                availableActions: shiftReportsAvailableActions({
                  archive: false,
                  restore: true,
                }),
              }),
              shiftReportsListItemFactory({
                id: 'shift-report-2',
                teamMemberId: 2,
                archived: true,
                availableActions: shiftReportsAvailableActions({
                  archive: false,
                  restore: true,
                }),
              }),
            ],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdPeopleMockHandler(() => [teamMember1, teamMember2]),
          getApiProjectsProjectIdPeopleTeamMemberIdMockHandler(({ params }) => {
            const teamMember = params.teamMemberId === '1' ? teamMember1 : teamMember2;
            return teamMember;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/archived'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:tab' };

        render(<ArchivedShiftReportsTable />, { history, route });

        await waitForElementToBeRemoved(await screen.findByTestId('shift-reports-table-loading'));

        expect(await screen.findAllByRole('cell', { name: 'shift reports reporter' })).toHaveLength(2);
        expect(await screen.findByRole('presentation', { name: 'Generic user' })).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.list.table.removedUser')).toBeInTheDocument();
      });
    });
  });

  describe('when there are no archived shift reports', () => {
    describe('when user has permission to create a report', () => {
      it('renders shift reports placeholder with create report button', async () => {
        const project = projectFactory({
          id: 'project-0',
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsArchivedMockHandler(() => ({
            shiftReports: [],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/archived'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:tab' };

        render(<ArchivedShiftReportsTable />, { history, route });

        await waitFor(() =>
          expect(screen.queryByRole('columnheader', { name: 'shiftReport.list.table.date' })).not.toBeInTheDocument()
        );
        expect(await screen.findByText('shiftReport.list.placeholder.noReportsArchived')).toBeInTheDocument();
        await waitFor(() => {
          expect(screen.queryByText(/shiftReport.list.placeholder.infoViewer/)).not.toBeInTheDocument();
        });
        await waitFor(() => {
          expect(screen.queryByText(/shiftReport.list.placeholder.cta/)).not.toBeInTheDocument();
        });
        await waitFor(() => {
          expect(screen.queryByRole('button', { name: 'shiftReport.new.title' })).not.toBeInTheDocument();
        });
      });
    });

    describe('when user does not have permission to create a report', () => {
      it('renders shift reports placeholder with no create report button', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: false,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsArchivedMockHandler(() => ({
            shiftReports: [],
            meta: sharedCursorPaginationMetaFactory(),
          })),
          getApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMockHandler(() => teamMemberFactory())
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/archived'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:tab' };

        render(<ArchivedShiftReportsTable />, { history, route });

        expect(await screen.findByText('shiftReport.list.placeholder.noReportsArchived')).toBeInTheDocument();
        await waitFor(() => {
          expect(screen.queryByText(/shiftReport.list.placeholder.infoViewer/)).not.toBeInTheDocument();
        });
        await waitFor(() => {
          expect(screen.queryByText(/shiftReport.list.placeholder.cta/)).not.toBeInTheDocument();
        });
        await waitFor(() => {
          expect(screen.queryByRole('button', { name: 'shiftReport.new.title' })).not.toBeInTheDocument();
        });
      });
    });
  });
});
