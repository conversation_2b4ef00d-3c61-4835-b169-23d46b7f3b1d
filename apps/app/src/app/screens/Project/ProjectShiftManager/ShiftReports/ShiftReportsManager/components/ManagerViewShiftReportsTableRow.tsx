import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema, ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Badge, Table, cn } from '@shape-construction/arch-ui';
import { ArrowsRightLeftIcon, UserGroupIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { TableCell } from '@shape-construction/arch-ui/src/Table/components/TableCell';
import React from 'react';
import { useNavigate } from 'react-router';
import { qualityLabelBadgeThemeMap } from '../../components/ShiftReportQualityIndicatorsInfoTable';
import { BadgeTableCell } from '../../components/TableCells/BadgeTableCell';
import { CheckboxTableCell } from '../../components/TableCells/CheckboxTableCell/CheckboxTableCell';
import { DateContent, DateTableCell } from '../../components/TableCells/DateTableCell';
import { TextContent, TextTableCell } from '../../components/TableCells/TextTableCell';
import { UserContent, UserTableCell } from '../../components/TableCells/UserTableCell';
import { getLabelByReportQualityPercentage } from '../../components/getLabelByReportQualityPercentage';

type ManagerViewShiftReportsTableRowProps = {
  rowData: ShiftReportBasicDetailsSchema;
  project: ProjectSchema;
  isLargeScreen: boolean;
  handleSelectCheckbox: () => void;
  isReportSelected: boolean;
};

export const ManagerViewShiftReportsTableRow: React.FC<ManagerViewShiftReportsTableRowProps> = ({
  isLargeScreen,
  project,
  rowData,
  handleSelectCheckbox,
  isReportSelected,
}) => {
  const navigate = useNavigate();
  const messages = useMessageGetter('shiftReport.qualityLabel');

  const qualityLabel = rowData.completionQualityScore
    ? getLabelByReportQualityPercentage(rowData.completionQualityScore)
    : null;

  const handleRowClick = (event: React.SyntheticEvent) => {
    event.preventDefault();
    navigate(`/projects/${project.id}/shift-reports/${rowData.id}`, {
      state: { tab: 'manager' },
    });
  };

  if (!isLargeScreen) {
    return (
      <Table.Row
        key={rowData.id}
        onClick={handleRowClick}
        className={cn('bg-white hover:bg-gray-100', {
          'bg-accent-indigo-subtle hover:bg-accent-indigo-subtle-hovered': isReportSelected,
        })}
      >
        <CheckboxTableCell
          className="content-start md:content-center pl-4"
          handleSelectCheckbox={handleSelectCheckbox}
          checkboxSelected={isReportSelected}
        />
        <Table.Cell className="flex justify-between items-start pl-0">
          <div className="w-full flex flex-col space-y-3">
            <div className="flex space-x-3">
              <div className="flex-1">
                <DateContent date={rowData.reportDate} />
              </div>
              <div className="flex flex-1 space-x-2 items-center">
                <ArrowsRightLeftIcon className="h-5 w-5 text-gray-400" />
                <TextContent text={rowData.shiftType} />
              </div>
            </div>
            <div className="flex space-x-3">
              <div className="flex-1">
                <UserContent projectId={project.id} teamMemberId={rowData.teamMemberId} />
              </div>
              <div className="flex flex-1 space-x-2 items-center">
                <UserGroupIcon className="h-5 w-5 text-gray-400" />
                <TextContent text={rowData.contractorName} />
              </div>
            </div>
            {qualityLabel && (
              <div className="flex space-x-3">
                <div className="flex-1">
                  <Badge label={messages(qualityLabel)} theme={qualityLabelBadgeThemeMap[qualityLabel]} />
                </div>
              </div>
            )}
          </div>
        </Table.Cell>
      </Table.Row>
    );
  }

  return (
    <Table.Row
      key={rowData.id}
      onClick={handleRowClick}
      className={cn('bg-white hover:bg-gray-100', {
        'bg-accent-indigo-subtle hover:bg-accent-indigo-subtle-hovered': isReportSelected,
      })}
    >
      <CheckboxTableCell
        className="px-2 sm:first:pl-4"
        handleSelectCheckbox={handleSelectCheckbox}
        checkboxSelected={isReportSelected}
      />
      <DateTableCell className="px-2" date={rowData.reportDate} />
      <TextTableCell className="px-2" text={rowData.shiftType} />
      <UserTableCell className="px-2" projectId={project.id} teamMemberId={rowData.teamMemberId} />
      <TextTableCell className="px-2" text={rowData.contractorName} />
      {qualityLabel ? (
        <BadgeTableCell theme={qualityLabelBadgeThemeMap[qualityLabel]} label={messages(qualityLabel)} />
      ) : (
        <TableCell />
      )}
    </Table.Row>
  );
};
