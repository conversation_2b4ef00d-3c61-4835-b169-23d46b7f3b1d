import { useMessageGetter } from '@messageformat/react';
import type {
  GetApiProjectsProjectIdShiftReportsArchivedQueryParamsSchema,
  ProjectSchema,
} from '@shape-construction/api/src/types';
import { EmptyState, PopoverMenu, Table } from '@shape-construction/arch-ui';
import { InformationCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { ShapeShiftReportIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { keepPreviousData, useQuery } from '@tanstack/react-query';
import { useDateFilters } from 'app/components/Filters/hooks/useDateFilters';
import { useFiltersStateCount, useFiltersStateParams } from 'app/components/Filters/hooks/useFiltersParams';
import { usePagination } from 'app/hooks/usePagination';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { useShiftReports } from 'app/queries/shiftReports/shiftReports';
import type React from 'react';
import { useMemo } from 'react';
import { useParams } from 'react-router';
import { CheckboxHeader } from '../../components/CheckboxHeader';
import { ShiftReportQualityIndicatorsInfoTable } from '../../components/ShiftReportQualityIndicatorsInfoTable';
import { ShiftReportsPlaceholder } from '../../components/ShiftReportsPlaceholder';
import { ShiftReportsTableSkeleton } from '../../components/ShiftReportsTableSkeleton';
import { SortableTableHeader } from '../../components/SortableTableHeader';
import { TablePagination } from '../../components/TablePagination';
import { useSelectShiftReport } from '../hooks/useSelectShiftReport';
import {
  ManagerViewShiftReportsFilters,
  type ManagerViewShiftReportsFiltersProps,
} from './ManagerViewShiftReportsFilters';
import { ManagerViewShiftReportsTableRow } from './ManagerViewShiftReportsTableRow';

type Params = {
  projectId: ProjectSchema['id'];
};

type FilterValues = ManagerViewShiftReportsFiltersProps['values'];
const defaultValues: FilterValues = {
  authors: [],
  date: {
    date: undefined,
    end_date: undefined,
    relative_date: undefined,
  },
};

export const defaultFilterParams: FilterValues = {
  authors: [],
  date: {
    date: undefined,
    end_date: undefined,
    relative_date: undefined,
  },
};

export const ManagerViewShiftReportsTable: React.FC = () => {
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const tableMessages = useMessageGetter('shiftReport.list.table');
  const placeholderMessages = useMessageGetter('shiftReport.list.placeholder');

  const { projectId } = useParams<Params>() as Params;
  const { data: projectData } = useQuery(getProjectQueryOptions(projectId!));

  const { filters, updateParams } = useFiltersStateParams(defaultFilterParams);
  const { total: filtersCount } = useFiltersStateCount();
  const dates = useDateFilters(filters.date, projectData);
  const {
    handleSelectSingleReport,
    handleSelectAllReportsByPage,
    unselectAllReports,
    areAllReportsSelected,
    isReportSelected,
  } = useSelectShiftReport();

  const pagination = usePagination();
  const params: GetApiProjectsProjectIdShiftReportsArchivedQueryParamsSchema = {
    after: pagination.after,
    before: pagination.before,
    report_date_start: dates.date,
    report_date_end: dates.endDate,
    author_id: filters.authors?.map((id) => Number.parseInt(id)),
  };
  const { data: shiftReportsData, isLoading } = useShiftReports(projectId, params, {
    query: { placeholderData: keepPreviousData },
  });

  const values: FilterValues = useMemo(
    () => ({
      date: filters.date,
      authors: filters.authors,
    }),
    [filters]
  );

  const handleSubmit: ManagerViewShiftReportsFiltersProps['handleSubmit'] = (params) => {
    unselectAllReports();

    updateParams(params);
  };

  if (isLoading) return <ShiftReportsTableSkeleton />;
  if (!shiftReportsData || !projectData) return null;

  const hasData = !!shiftReportsData.shiftReports?.length;
  const showFilters = hasData || filtersCount > 0;
  const showEmptyPlaceholder = !hasData && filtersCount === 0;
  const showEmptyFiltersPlaceholder = !hasData && filtersCount > 0;

  const shiftReportsIds = shiftReportsData.shiftReports.map((report) => report.id);
  const selectedReportsOnPage = shiftReportsIds.filter((id) => isReportSelected(id));
  const isIndeterminateCheckbox =
    selectedReportsOnPage.length > 0 && selectedReportsOnPage.length < shiftReportsIds.length;

  return (
    <section className="space-y-2">
      {showFilters && (
        <ManagerViewShiftReportsFilters
          defaultValues={defaultValues}
          values={values}
          handleSubmit={handleSubmit}
          hasNoResults={showEmptyFiltersPlaceholder}
        />
      )}

      {showEmptyPlaceholder && <ShiftReportsPlaceholder status="published" />}
      {showEmptyFiltersPlaceholder && (
        <EmptyState
          icon={<ShapeShiftReportIcon className="w-12 h-12" />}
          title={placeholderMessages('noFiltersTitle')}
          body={placeholderMessages('noFiltersSubtitle')}
        />
      )}

      {hasData && (
        <Table.Container data-cy="published-shift-reports">
          <Table>
            {isLargeScreen && (
              <Table.Heading>
                <Table.Row>
                  <CheckboxHeader
                    className="p-2 sm:first:pl-4 w-6"
                    handleSelectCheckbox={() => handleSelectAllReportsByPage(shiftReportsIds)}
                    areAllReportsSelected={areAllReportsSelected(shiftReportsIds)}
                    isIndeterminateCheckbox={isIndeterminateCheckbox}
                  />
                  <SortableTableHeader className="p-2" label={tableMessages('date')} />
                  <SortableTableHeader className="p-2" label={tableMessages('shift')} />
                  <SortableTableHeader className="p-2" label={tableMessages('author')} />
                  <SortableTableHeader className="p-2" label={tableMessages('team')} />
                  <SortableTableHeader
                    className="p-2"
                    label={tableMessages('quality')}
                    icon={
                      <PopoverMenu.Root>
                        <PopoverMenu.Trigger>
                          <InformationCircleIcon className="h-4 w-4 text-indigo-500" />
                        </PopoverMenu.Trigger>
                        <PopoverMenu.Content
                          side="bottom"
                          align="start"
                          className="md:max-w-xs md:p-0 flex flex-row items-center"
                        >
                          <ShiftReportQualityIndicatorsInfoTable />
                        </PopoverMenu.Content>
                      </PopoverMenu.Root>
                    }
                  />
                </Table.Row>
              </Table.Heading>
            )}
            {!isLargeScreen && (
              <Table.Heading>
                <Table.Row>
                  <CheckboxHeader
                    className="px-4 pt-1 pb-0.5"
                    handleSelectCheckbox={() => handleSelectAllReportsByPage(shiftReportsIds)}
                    areAllReportsSelected={areAllReportsSelected(shiftReportsIds)}
                    isIndeterminateCheckbox={isIndeterminateCheckbox}
                  />
                </Table.Row>
              </Table.Heading>
            )}
            <Table.Body className="[&_tr]:cursor-pointer [&_tr]:border-b [&_tr]:last:border-b-0">
              {shiftReportsData.shiftReports.map((rowData) => (
                <ManagerViewShiftReportsTableRow
                  isLargeScreen={isLargeScreen}
                  key={rowData.id}
                  project={projectData}
                  rowData={rowData}
                  handleSelectCheckbox={() => handleSelectSingleReport(rowData.id)}
                  isReportSelected={isReportSelected(rowData.id)}
                />
              ))}
            </Table.Body>
            <TablePagination
              meta={shiftReportsData.meta}
              count={shiftReportsData.shiftReports.length}
              onNext={pagination.onNext}
              onPrevious={pagination.onPrevious}
            />
          </Table>
        </Table.Container>
      )}
    </section>
  );
};
