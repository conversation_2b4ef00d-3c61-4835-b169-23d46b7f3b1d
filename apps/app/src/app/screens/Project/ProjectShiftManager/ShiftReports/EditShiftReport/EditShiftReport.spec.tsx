import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import {
  shiftReportQualityIndicatorsFactory,
  shiftReportsActivityFactory,
  shiftReportsAvailableActions,
  shiftReportsContractForceFactory,
  shiftReportsDownTimeFactory,
  shiftReportsEquipmentFactory,
  shiftReportsFactory,
  shiftReportsListItemFactory,
  shiftReportsMaterialFactory,
  shiftReportsPublishFactory,
  shiftReportsSafetyHealthEnvironmentFactory,
} from '@shape-construction/api/factories/shiftReports';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import {
  getApiProjectsProjectIdShiftReportsMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
  getApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsMockHandler,
  patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
  postApiProjectsProjectIdShiftReportsShiftReportIdExportMockHandler,
  postApiProjectsProjectIdShiftReportsShiftReportIdPublishMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import * as useExportsModule from 'app/components/Exports/useExports';
import { createMemoryHistory } from 'history';
import { delay } from 'msw';
import React from 'react';
import { server, waitForRequest } from 'tests/mock-server';
import { createMatchMedia, render, screen, waitFor, within } from 'tests/test-utils';
import type { ShiftReportFormValues } from '../ShiftReportForm/ShiftReportForm';
import { EditShiftReport } from './EditShiftReport';

describe('EditShiftReport', () => {
  const useExportsMock = jest.spyOn(useExportsModule, 'default');

  beforeEach(() => {
    useExportsMock.mockReturnValue({
      pendingExportIds: [],
      exportsQueue: [],
      initToastsFromStorage: jest.fn(),
      addToPendingExports: jest.fn(),
      addToCompletedExports: jest.fn(),
      removeFromCompletedExports: jest.fn(),
      dismissFailedExport: jest.fn(),
    });
  });

  afterEach(() => jest.resetAllMocks());

  describe('when user edits a root field in the form', () => {
    it('saves the field after leaving focus', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchedShiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
        projectNumber: '#000001',
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => patchedShiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      await user.clear(screen.getByLabelText('shiftReport.form.projectNumber'));
      await user.type(screen.getByLabelText('shiftReport.form.projectNumber'), '#000001');
      expect(screen.queryByText('shiftReport.new.draftSaved')).not.toBeInTheDocument();
      expect(screen.queryByText('shiftReport.new.draftSaving')).not.toBeInTheDocument();
      await user.tab();

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => {
        expect(screen.getByLabelText('shiftReport.form.projectNumber')).toHaveValue('#000001');
      });
    });
  });

  describe('when user edits a multi row field in the form', () => {
    it('saves the field after leaving focus', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchedShiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
        activities: [shiftReportsActivityFactory({ description: 'This is a new activity description' })],
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => patchedShiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      await user.clear(screen.getAllByLabelText('shiftReport.form.description')[1]);
      await user.type(
        screen.getAllByLabelText('shiftReport.form.description')[1],
        'This is a new activity description'
      );
      expect(screen.queryByText('shiftReport.new.draftSaved')).not.toBeInTheDocument();
      expect(screen.queryByText('shiftReport.new.draftSaving')).not.toBeInTheDocument();
      await user.tab();

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      expect(screen.getAllByLabelText('shiftReport.form.description')[1]).toHaveValue(
        'This is a new activity description'
      );
    });
  });

  describe('when the user clicks add row', () => {
    it('adds an activity row', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchApiMock = jest.fn();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(async ({ request }) => {
          patchApiMock(await request.json());
          return shiftReport;
        })
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      expect(screen.queryAllByLabelText('shiftReport.form.description')).toHaveLength(4);

      await user.click(screen.getAllByText('shiftReport.form.addSafetyNote')[0]);

      await waitFor(() => expect(patchApiMock).toHaveBeenCalledWith({ safety_health_environments: [{}] }));
    });
  });

  describe('when the user deletes an activity row', () => {
    it('calls api to delete the activity from the form', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchApiMock = jest.fn();
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(async ({ request }) => {
          patchApiMock(await request.json());
          return shiftReport;
        })
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      await user.click((await screen.findAllByLabelText('shiftReport.form.delete'))[0]);

      await waitFor(() =>
        expect(patchApiMock).toHaveBeenCalledWith({ activities: [{ id: 'id-0', _destroy: 'true' }] })
      );
    });
  });

  describe('when the form is being updated', () => {
    it('disables the AddRow button', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const patchedShiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
        activities: [shiftReportsActivityFactory(), shiftReportsActivityFactory()],
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(async () => {
          await delay(1000);
          return patchedShiftReport;
        })
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };
      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      await user.click(screen.getAllByText('shiftReport.form.addSafetyNote')[0]);

      await waitFor(() =>
        expect(screen.getAllByRole('button', { name: 'shiftReport.form.addSafetyNote' })[0]).toBeDisabled()
      );
    });
  });

  describe('when preview button is clicked', () => {
    it('redirects to preview', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      await user.click(await screen.findByRole('button', { name: 'shiftReport.new.preview' }));

      await waitFor(() => {
        expect(history.location.pathname).toBe('/projects/project-0/shift-reports/shift-report-0');
      });
    });
  });

  describe('EditShiftReport page structure', () => {
    it('renders EdiftShiftReport header', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByRole('heading', { name: 'shiftReport.edit.title' })).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      expect(screen.getByRole('button', { name: 'shiftReport.new.preview' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'shiftReport.new.preview' })).toBeInTheDocument();
      expect(screen.getByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      expect(screen.getByText('shiftReport.visibility.private')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'shiftReport.collaborators.addCollaborators' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'shiftReport.new.prefillFromPrevious' })).toBeInTheDocument();
    });

    it('renders EdiftShiftReport form sections', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      expect(await screen.findByRole('heading', { name: 'shiftReport.form.projectDetails' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.shiftDetails' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.weather' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.progress' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.people' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.equipment' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.notes' })).toBeInTheDocument();
      expect(
        await screen.findByRole('heading', { name: 'shiftReport.form.safetyHealthEnvironment' })
      ).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.material' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.downTime' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.documents' })).toBeInTheDocument();
    });
  });

  describe('when user clicks back button', () => {
    it('redirects back to shift reports draft page', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      await user.click(screen.getByRole('button', { name: 'navigation.back' }));

      expect(history.location.pathname).toBe('/projects/project-0/shift-reports/drafts');
    });
  });

  describe('when user has permission to publish shift report', () => {
    it('Publishes the shift report, shows confirmation modal and redirects to view details page', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          publish: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        postApiProjectsProjectIdShiftReportsShiftReportIdPublishMockHandler(() =>
          shiftReportsPublishFactory({ shiftReport: shiftReport })
        ),
        postApiProjectsProjectIdShiftReportsShiftReportIdExportMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      await user.click(await screen.findByRole('button', { name: 'shiftReport.new.publish' }));

      expect(await screen.findByRole('dialog')).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.publishModal.title')).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.publishModal.subTitle')).toBeInTheDocument();
      expect(
        await screen.findByRole('button', { name: 'shiftReport.publishModal.actions.cancel' })
      ).toBeInTheDocument();
      expect(
        await screen.findByRole('button', { name: 'shiftReport.publishModal.actions.publish' })
      ).toBeInTheDocument();
      expect(
        await screen.findByRole('button', {
          name: 'shiftReport.publishModal.actions.publishAndExport',
        })
      ).toBeInTheDocument();

      await user.click(await screen.findByRole('button', { name: 'shiftReport.publishModal.actions.publish' }));

      await waitFor(() => expect(screen.queryByRole('dialog')).not.toBeInTheDocument());
      expect(history.location.pathname).toBe('/projects/project-0/shift-reports/shift-report-0');
      expect(
        screen.queryByRole('button', {
          name: 'shiftReport.publishModal.actions.publishAndExport',
        })
      ).not.toBeInTheDocument();
    });
  });

  describe('when user has permission to publish and export shift report', () => {
    it('Publishes and exports the shift report, shows confirmation modal and redirects to view details page', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          publish: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        postApiProjectsProjectIdShiftReportsShiftReportIdPublishMockHandler(() =>
          shiftReportsPublishFactory({ shiftReport: shiftReport })
        ),
        postApiProjectsProjectIdShiftReportsShiftReportIdExportMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      await user.click(await screen.findByRole('button', { name: 'shiftReport.new.publish' }));

      await user.click(
        await screen.findByRole('button', {
          name: 'shiftReport.publishModal.actions.publishAndExport',
        })
      );

      expect(await screen.findByRole('dialog')).toBeInTheDocument();
      expect(screen.getByText('shiftReport.exportOptionsModal.title')).toBeInTheDocument();
      await user.click(screen.getByText('PDF'));
      expect(screen.getByText('shiftReport.exportOptionsModal.actions.export')).toBeInTheDocument();
      await user.click(screen.getByRole('button', { name: 'shiftReport.exportOptionsModal.actions.export' }));

      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
      await waitFor(() => {
        expect(history.location.pathname).toBe('/projects/project-0/shift-reports/shift-report-0');
      });
      expect(
        screen.queryByRole('button', {
          name: 'shiftReport.publishModal.actions.publishAndExport',
        })
      ).not.toBeInTheDocument();
    });
  });

  describe('when user cannot edit shift report', () => {
    it('hides edit title and form', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: false,
          publish: false,
          export: false,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      await waitFor(() =>
        expect(screen.queryByRole('heading', { name: 'shiftReport.edit.title' })).not.toBeInTheDocument()
      );
      await waitFor(() =>
        expect(screen.queryByRole('form', { name: 'shiftReport.form.shiftReportForm' })).not.toBeInTheDocument()
      );
    });
  });

  describe('when user cannot publish shift report', () => {
    it('disables publish button', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
          publish: false,
          export: false,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      expect(await screen.findByRole('button', { name: 'shiftReport.new.publish' })).toBeDisabled();
    });
  });

  describe('when user is a collaborator', () => {
    it('displays author name and collaborator message', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        teamMemberId: 2,
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: false,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
        getApiProjectsProjectIdPeopleMockHandler(() => {
          const teamMembers = [
            teamMemberFactory({
              id: 1,
              role: 'admin',
              user: userBasicDetailsFactory({ name: 'An admin' }),
            }),
            teamMemberFactory({
              id: 2,
              role: 'owner',
              user: userBasicDetailsFactory({ name: 'An owner' }),
            }),
          ];
          return teamMembers;
        })
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.author:')).toBeInTheDocument();
      expect(await screen.findByText('An owner')).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.new.editAsCollaboratorLabel')).toBeInTheDocument();
    });

    describe('when author is removed', () => {
      it('displays generic user avatar and removed user text', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftReport: true,
          }),
        });
        const shiftReport = shiftReportsFactory({
          teamMemberId: 2,
          availableActions: shiftReportsAvailableActions({
            edit: true,
            editRootFields: false,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
          getApiProjectsProjectIdPeopleMockHandler(() => {
            const teamMembers = [
              teamMemberFactory({
                id: 1,
                role: 'admin',
                user: userBasicDetailsFactory({ name: 'An admin' }),
              }),
            ];
            return teamMembers;
          })
        );
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
        });
        const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

        render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

        await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
        expect(await screen.findByText('shiftReport.new.author:')).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.new.editAsCollaboratorLabel')).toBeInTheDocument();

        expect((await screen.findAllByRole('presentation', { name: 'Generic user' }))[0]).toBeInTheDocument();
        expect(await screen.findByText('shiftReport.list.table.removedUser')).toBeInTheDocument();
      });
    });
  });

  describe('when a user is on mobile', () => {
    afterEach(() => {
      window.matchMedia = createMatchMedia();
    });

    it('shows form list items as closed and opens them on click', async () => {
      window.matchMedia = createMatchMedia(320);

      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      await screen.findByText('shiftReport.form.people');

      const peopleRow = within(screen.getByTestId('contract_forces.0'));
      const openRow = await peopleRow.findByLabelText('shiftReport.form.openRowLinks');

      await user.click(openRow);

      expect(await peopleRow.findByLabelText('shiftReport.form.closeRowLinks')).toBeInTheDocument();
    });
  });

  describe('when a user is on desktop', () => {
    it('shows form list items as open and closes them on click', async () => {
      window.matchMedia = createMatchMedia(1024);

      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      await screen.findByText('shiftReport.form.people');

      const peopleRow = within(screen.getByTestId('contract_forces.0'));
      const openRow = await peopleRow.findByLabelText('shiftReport.form.closeRowLinks');

      await user.click(openRow);

      expect(await peopleRow.findByLabelText('shiftReport.form.openRowLinks')).toBeInTheDocument();
    });
  });

  it('renders report quality progress', async () => {
    const project = projectFactory({
      id: 'project-0',
      availableActions: projectAvailableActions({
        createShiftReport: true,
      }),
    });
    const shiftReport = shiftReportsFactory({
      availableActions: shiftReportsAvailableActions({
        edit: true,
        editRootFields: true,
      }),
    });
    const shiftReportQualityIndicators = shiftReportQualityIndicatorsFactory({
      currentScore: {
        percentage: {
          completed: 30,
          total: 100,
        },
      },
    });
    server.use(
      getApiProjectsProjectIdMockHandler(() => project),
      getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport),
      getApiProjectsProjectIdShiftReportsShiftReportIdQualityIndicatorsMockHandler(() => shiftReportQualityIndicators)
    );
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
    });
    const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

    render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

    expect(await screen.findByText('30%', {}, { timeout: 2000 })).toBeInTheDocument();
  });

  xdescribe('when user has previous shift report', () => {
    // biome-ignore lint/suspicious/noDuplicateTestHooks: biome complains here because it doesnt recognize xdescribe
    beforeEach(() => {
      jest.useFakeTimers({
        doNotFake: ['setTimeout'],
      });
      jest.setSystemTime(new Date('2022-10-21'));
    });

    it('can pre-fill from previous report', async () => {
      const combinedShiftReports: ShiftReportFormValues = {
        activities: [
          {
            _id: 'id-0',
            comment: 'comment-0',
            description: 'previous activity 1',
            document_count: 0,
            location_id: 'location-0',
            planned: 'planned',
            shift_activity_id: 'shiftActivity-0',
            quantity: 1,
            units: 'unit',
          },
          {
            _destroy: 'true',
            id: 'id-0',
          },
        ],
        client_document_reference_number: 'clientDocumentReferenceNumber-1',
        collaborators_team_member_ids: [],
        contract_forces: [
          {
            activities: [
              {
                allocation_id: 'id-0',
                quantity: 10,
              },
            ],
            document_count: 0,
            down_times: [],
            comment: 'comment-0',
            hours: 10,
          },
          {
            _destroy: 'true',
            id: 'id-0',
          },
        ],
        contractor_name: 'contractor-1',
        down_times: [
          {
            _id: 'id-0',
            causal_type: 'previours casual type 1',
            document_count: 0,
            issue_description: 'Electrician oozed the dragline',
            issue_id: '',
            time_lost: 1,
          },
          {
            _destroy: 'true',
            id: 'id-0',
          },
        ],
        equipments: [
          {
            activities: [
              {
                allocation_id: 'id-0',
                quantity: 10,
              },
            ],
            down_times: [],
            document_count: 0,
            equipment_id: 'equipment-0',
            hours: 10,
            quantity: 1,
          },
          {
            _destroy: 'true',
            id: 'id-0',
          },
        ],
        internal_document_reference_number: 'internalDocumentReferenceNumber-1',
        materials: [
          {
            activities: [
              {
                allocation_id: 'id-0',
                quantity: 10,
              },
            ],
            down_times: [],
            document_count: 0,
            quantity: 1,
            units: '10',
          },
          {
            _destroy: 'true',
            id: 'id-0',
          },
        ],
        notes: 'note-0',
        project_number: 'project-1',
        report_date: '2022-10-21',
        report_title: 'title',
        safety_health_environments: [
          {
            document_count: 0,
            safety_note: 'previous safety note 1',
          },
          {
            _destroy: 'true',
            id: 'id-0',
          },
        ],
        shift_end: '05:00 pm',
        shift_start: '08:00 am',
        shift_type: 'Day Shift',
        visibility: 'private',
        weather_description: 'Partly clouded',
        weather_temperature: '14º - 20º',
      };
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftReport: true,
        }),
      });
      const shiftReport = shiftReportsFactory({
        reportDate: '2023-01-9',
        availableActions: shiftReportsAvailableActions({
          edit: true,
          editRootFields: true,
        }),
      });
      const previousShiftReport = shiftReportsFactory({
        id: 'previous-shift-report-0',
        projectNumber: 'project-1',
        contractorName: 'contractor-1',
        internalDocumentReferenceNumber: 'internalDocumentReferenceNumber-1',
        clientDocumentReferenceNumber: 'clientDocumentReferenceNumber-1',
        reportDate: '2023-01-10',
        shiftType: 'Day Shift',
        activities: [shiftReportsActivityFactory({ description: 'previous activity 1' })],
        contractForces: [shiftReportsContractForceFactory({ name: 'previous force name 1' })],
        equipments: [shiftReportsEquipmentFactory({ description: 'previous equipment 1' })],
        safetyHealthEnvironments: [
          shiftReportsSafetyHealthEnvironmentFactory({ safetyNote: 'previous safety note 1' }),
        ],
        materials: [shiftReportsMaterialFactory({ description: 'previous material 1' })],
        downTimes: [shiftReportsDownTimeFactory({ causalType: 'previours casual type 1' })],
      });

      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdShiftReportsMockHandler(() => ({
          meta: {
            firstEntryCursor: 'cursor',
            lastEntryCursor: 'cursor',
            hasNextPage: false,
            hasPreviousPage: false,
            total: 1,
          },
          shiftReports: [shiftReportsListItemFactory({ id: previousShiftReport.id })],
        })),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(({ params }) => {
          if (params.shiftReportId === 'previous-shift-report-0') return previousShiftReport;
          return shiftReport;
        }),
        patchApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => previousShiftReport)
      );

      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/shift-report-0/edit'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:shiftReportId/edit' };

      const { user } = render(<EditShiftReport />, { history, route, pageData: { project, shiftReport } });

      // Wait for page to load
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      expect(await screen.findByRole('heading', { name: 'shiftReport.edit.title' })).toBeInTheDocument();

      await waitFor(() =>
        expect(screen.queryByRole('button', { name: 'shiftReport.new.prefillFromPrevious' })).toBeEnabled()
      );

      await user.click(await screen.findByRole('button', { name: 'shiftReport.new.prefillFromPrevious' }));

      // Wait for page to load
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      expect(await screen.findByText('shiftReport.modal.actionMessage')).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.modal.warningMessage')).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.modal.actions.cancel')).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.modal.actions.override')).toBeInTheDocument();

      await user.click(await screen.findByRole('button', { name: 'shiftReport.modal.actions.override' }));

      const createPatchShiftReportRequest = await waitForRequest(
        'PATCH',
        '/api/projects/:projectId/shift_reports/:shiftReportId'
      );
      expect(await createPatchShiftReportRequest.json()).toEqual(combinedShiftReports);

      // Wait for page to load
      expect(await screen.findByRole('heading', { name: 'shiftReport.edit.title' })).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());

      expect(await screen.findByLabelText('shiftReport.form.projectNumber')).toHaveValue('project-0');

      expect(await screen.findByLabelText('shiftReport.form.teamName')).toHaveValue('contractor');
      expect(await screen.findByLabelText('shiftReport.form.docRefNumberInternal')).toHaveValue(
        'internalDocumentReferenceNumber-0'
      );
      expect(await screen.findByLabelText('shiftReport.form.docRefNumberClient')).toHaveValue(
        'clientDocumentReferenceNumber-0'
      );

      expect(await screen.findByRole('heading', { name: 'shiftReport.edit.title' })).toBeInTheDocument();
      expect(await screen.findByText('shiftReport.new.draftSaved')).toBeInTheDocument();
      await waitFor(() => expect(screen.queryByRole('progressbar')).not.toBeInTheDocument());
      expect(await screen.findByRole('button', { name: 'navigation.back' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.projectDetails' })).toBeInTheDocument();
      expect(await screen.findByRole('heading', { name: 'shiftReport.form.documents' })).toBeInTheDocument();
    });
  });
});
