import { useMessageGetter } from '@messageformat/react';
import { Button, IconButton, InputText } from '@shape-construction/arch-ui';
import { TrashIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { LogoPreview } from 'app/components/UI/LogoPreview/LogoPreview';
import { MAX_FILE_SIZE_IN_BYTES, SUPPORTED_IMAGE_MIME_TYPES } from 'app/constants/FileUpload';
import { useFileUploadValidator } from 'app/hooks/useFileUploadValidator';
import { useShiftReportInputsDisabled } from 'app/hooks/useShiftReportInputsDisabled';
import { useUploadShiftReportContractorLogo } from 'app/queries/shiftReports/images/images';
import { useUpdateShiftReport } from 'app/queries/shiftReports/shiftReports';
import React, { useRef } from 'react';
import { useController } from 'react-hook-form';
import { useParams } from 'react-router';
import { FormSectionHeader } from '../components/FormSection';
import { ProjectName } from '../components/ProjectName';
import { makeFormValuesFromReportData, useShiftReportFormContext } from './ShiftReportForm';

type Params = {
  projectId: string;
  shiftReportId: string;
};

export const ShiftReportFormProjectDetails = () => {
  const { projectId, shiftReportId } = useParams<Params>() as Params;
  const messages = useMessageGetter('shiftReport.form');
  const fileUploadErrors = useMessageGetter('errors.fileUpload');
  const inputsDisabled = useShiftReportInputsDisabled();
  const { mutate: uploadShiftReportContractorLogo } = useUploadShiftReportContractorLogo();
  const { register, control, getValues, reset } = useShiftReportFormContext();
  const { mutate: updateShiftReport } = useUpdateShiftReport();
  const { field } = useController({ name: 'contractor_logo.new', control });

  const { validateFile, handleValidationErrors } = useFileUploadValidator({
    maxSizeInBytes: MAX_FILE_SIZE_IN_BYTES,
    errorMessages: {
      fileSizeMin: (filename, min) => fileUploadErrors('fileSizeMin', { filename, min }),
      fileSizeMax: (filename, max) => fileUploadErrors('fileSizeMax', { filename, max }),
      fileTypeInvalid: (filename) => fileUploadErrors('fileTypeInvalid', { filename }),
    },
  });

  const logoInputRef = useRef(null);

  const uploadContractorLogo = (file: File) => {
    uploadShiftReportContractorLogo(
      { file },
      {
        onSuccess: (logoSignedId) => {
          updateShiftReport(
            {
              projectId,
              shiftReportId,
              data: {
                contractor_logo_signed_id: logoSignedId,
              },
            },
            {
              onSuccess: (updatedShiftReport) => reset(makeFormValuesFromReportData(updatedShiftReport)),
            }
          );
        },
      }
    );
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    const result = validateFile(file);
    handleValidationErrors(result);

    if (result.isValid) {
      field.onChange(file);
      uploadContractorLogo(file);
    }
  };

  const handleDeleteContractorLogo = async (event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    field.onChange(null);

    updateShiftReport(
      {
        projectId,
        shiftReportId,
        data: {
          contractor_logo_signed_id: null,
        },
      },
      {
        onSuccess: (updatedShiftReport) => reset(makeFormValuesFromReportData(updatedShiftReport)),
      }
    );

    event.preventDefault();
  };

  const logoUrl = getValues().contractor_logo?.url;
  const hasLogo = !!logoUrl || !!field.value;

  return (
    <fieldset className="gap-y-6">
      <legend>
        <FormSectionHeader>{messages('projectDetails')}</FormSectionHeader>
      </legend>
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <ProjectName />
        <InputText {...register('project_number')} disabled={inputsDisabled} label={messages('projectNumber')} />
        <div className="hidden lg:block" />
        <InputText {...register('contractor_name')} disabled={inputsDisabled} label={messages('teamName')} />

        <div className="block text-sm font-medium text-gray-700">
          {messages('teamLogo')}
          <div className="relative mt-1 flex items-center">
            <LogoPreview url={logoUrl} logo={field.value} className="w-16 h-16 max-w-16 mr-3" />

            <label htmlFor="logo" className="h-16">
              <div className="flex flex-col items-start justify-center">
                <div className="flex gap-2">
                  <Button
                    disabled={inputsDisabled}
                    color="secondary"
                    variant="outlined"
                    size="sm"
                    onClick={(event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
                      // @ts-expect-error ts-migrate(2531) FIXME: Object is possibly 'null'.
                      event.target.parentNode.click();
                    }}
                  >
                    {hasLogo ? messages('change') : messages('upload')}
                  </Button>
                  {hasLogo && (
                    <IconButton
                      disabled={inputsDisabled}
                      color="secondary"
                      variant="text"
                      aria-label={messages('delete')}
                      icon={TrashIcon}
                      size="sm"
                      onClick={handleDeleteContractorLogo}
                    />
                  )}
                </div>
                <p className="text-xs text-gray-500 mt-1.5">JPG or PNG up to 10MB</p>
              </div>
              <input
                disabled={inputsDisabled}
                ref={logoInputRef}
                type="file"
                id="logo"
                onChange={handleImageChange}
                className="h-px w-px opacity-0"
                accept={SUPPORTED_IMAGE_MIME_TYPES.join(',')}
              />
            </label>
          </div>
        </div>

        <div className="hidden lg:block" />
        <InputText
          {...register('internal_document_reference_number')}
          disabled={inputsDisabled}
          label={messages('docRefNumberInternal')}
        />
        <InputText
          {...register('client_document_reference_number')}
          disabled={inputsDisabled}
          label={messages('docRefNumberClient')}
        />
      </div>
    </fieldset>
  );
};
