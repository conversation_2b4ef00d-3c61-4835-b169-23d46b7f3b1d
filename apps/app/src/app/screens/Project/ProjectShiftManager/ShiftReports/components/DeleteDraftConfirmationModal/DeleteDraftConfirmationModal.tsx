import { useMessageGetter } from '@messageformat/react';
import type { ShiftReportBasicDetailsSchema } from '@shape-construction/api/src/types';
import { Button, ConfirmationModal, IconBadge } from '@shape-construction/arch-ui';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useShiftReportsDraftDelete } from 'app/queries/shiftReports/shiftReports';
import React from 'react';
import { useNavigate, useParams } from 'react-router';

type Params = {
  projectId: string;
};

export interface DeleteDraftConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  shiftReportId: ShiftReportBasicDetailsSchema['id'];
}

export const DeleteDraftConfirmationModal = ({ isOpen, onClose, shiftReportId }: DeleteDraftConfirmationModalProps) => {
  const navigate = useNavigate();
  const messages = useMessageGetter('shiftReport.deleteDraftModal');
  const { projectId } = useParams<Params>() as Params;
  const { mutate: deleteDraft, isPending } = useShiftReportsDraftDelete();

  const handleDelete = async () => {
    deleteDraft(
      {
        projectId,
        shiftReportId,
      },
      {
        onSuccess() {
          onClose();
          navigate(`/projects/${projectId}/shift-reports/drafts`, { replace: true });
          showSuccessToast({
            message: messages('feedbackMessage'),
            alignContent: 'start',
          });
        },
      }
    );
  };

  return (
    <ConfirmationModal.Root open={isOpen} onClose={onClose}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="danger">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>{messages('subTitle')}</ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button
          color="secondary"
          variant="outlined"
          size="md"
          aria-label={messages('actions.cancel')}
          onClick={onClose}
          disabled={isPending}
        >
          {messages('actions.cancel')}
        </Button>
        <Button
          color="danger"
          variant="contained"
          size="md"
          aria-label={messages('actions.delete')}
          onClick={handleDelete}
          disabled={isPending}
        >
          {messages('actions.delete')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
