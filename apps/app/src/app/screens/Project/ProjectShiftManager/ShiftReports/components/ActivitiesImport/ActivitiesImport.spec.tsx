import { activityFactory } from '@shape-construction/api/factories/activities';
import { getApiProjectsProjectIdShiftActivitiesMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-activities';
import {
  getApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMockHandler,
  patchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMockHandler,
  postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatchMockHandler,
} from '@shape-construction/api/handlers-factories/projects/weekly-work-plans';
import * as useFiltersParams from 'app/components/Filters/hooks/useFiltersParams';
import { server } from 'tests/mock-server';
import { render, screen } from 'tests/test-utils';
import * as useShiftReportRows from '../../ShiftReportForm/useShiftReportRows';
import { ActivitiesImport } from './ActivitiesImport';

const mockHandleAddRows = jest.fn();
const mockHandleDeleteRows = jest.fn();
const mockUseInitialValues = jest.fn();

jest.mock('../../../../../../components/Activities/AddActivitiesModal/ActivitiesToolbar/useInitialValues', () => ({
  useInitialValues: () => mockUseInitialValues(),
}));

describe('<ActivitiesImport />', () => {
  beforeEach(() => {
    jest.spyOn(useShiftReportRows, 'useShiftReportRows').mockImplementation(() => {
      return {
        handleAddRows: mockHandleAddRows,
        handleDeleteRows: mockHandleDeleteRows,
        fieldRows: [],
        pathname: 'activities',
        handleAddRow: jest.fn(),
        isAddingRow: false,
      };
    });
  });

  it('renders correctly', () => {
    render(<ActivitiesImport />);

    expect(
      screen.getByRole('button', { name: 'shiftReport.form.linkProgress.removeAllActivities' })
    ).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'shiftReport.form.linkProgress.linkActivities' })).toBeInTheDocument();
  });

  describe('when remove all activities button is clicked', () => {
    it('calls handleDeleteRows', async () => {
      const { user } = render(<ActivitiesImport canRemoveActivities />);

      await user.click(screen.getByRole('button', { name: 'shiftReport.form.linkProgress.removeAllActivities' }));

      expect(mockHandleDeleteRows).toHaveBeenCalled();
    });
  });

  describe('when link activities button is clicked', () => {
    it('opens the AddActivitiesModal', async () => {
      mockUseInitialValues.mockReturnValue({ initialValues: {}, isFetchingInitialValues: true });

      const { user } = render(<ActivitiesImport />);

      await user.click(screen.getByRole('button', { name: 'shiftReport.form.linkProgress.linkActivities' }));

      expect(
        await screen.findByText('weeklyPlanner.workPlans.planEditor.addActivitiesModal.title')
      ).toBeInTheDocument();
    });

    describe('when activities are selected', () => {
      it('calls handleAddRows with selected activity ids', async () => {
        const shiftActivities = [activityFactory({ id: 'activity-0' }), activityFactory({ id: 'activity-1' })];
        mockUseInitialValues.mockReturnValue({ initialValues: {}, isFetchingInitialValues: false });
        jest.spyOn(useFiltersParams, 'useFiltersStateParams').mockImplementation(() => ({
          filters: { selected_ids: ['activity-0'] },
          sort: {},
          updateParams: jest.fn(),
        }));
        server.use(
          getApiProjectsProjectIdShiftActivitiesMockHandler(() => ({
            entries: shiftActivities,
            meta: {
              firstEntryCursor: '',
              hasNextPage: false,
              hasPreviousPage: false,
              lastEntryCursor: '',
              total: shiftActivities.length,
            },
          })),
          postApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatchMockHandler(),
          getApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMockHandler(() => ({
            filters: [],
          })),
          patchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMockHandler()
        );

        const { user } = render(<ActivitiesImport />);

        await user.click(screen.getByRole('button', { name: 'shiftReport.form.linkProgress.linkActivities' }));

        expect(
          await screen.findByText('weeklyPlanner.workPlans.planEditor.addActivitiesModal.title')
        ).toBeInTheDocument();

        await user.click(
          await screen.findByRole('button', {
            name: 'weeklyPlanner.workPlans.planEditor.addActivitiesModal.addSelectedCTA',
          })
        );

        expect(mockHandleAddRows).toHaveBeenCalled();
      });
    });
  });
});
