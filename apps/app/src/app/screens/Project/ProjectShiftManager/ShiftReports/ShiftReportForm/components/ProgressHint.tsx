import { useMessageGetter } from '@messageformat/react';
import { Button, ConfirmationModal, IconBadge, IconButton } from '@shape-construction/arch-ui';
import { QuestionMarkCircleIcon as QuestionMarkCircleIconOutline } from '@shape-construction/arch-ui/src/Icons/outline';
import { QuestionMarkCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useModal } from '@shape-construction/hooks';
import React from 'react';
import { renderMarkup } from 'react-render-markup';

export const ProgressHint = () => {
  const messages = useMessageGetter('shiftReport.form.progressHint');
  const { open, openModal, closeModal } = useModal(false);

  return (
    <>
      <IconButton
        variant="text"
        size="xxs"
        color="secondary"
        icon={QuestionMarkCircleIcon}
        onClick={openModal}
        aria-label="progress-hint"
      />
      <ConfirmationModal.Root open={open} onClose={closeModal}>
        <ConfirmationModal.Header>
          <ConfirmationModal.Image>
            <IconBadge type="info">
              <QuestionMarkCircleIconOutline />
            </IconBadge>
          </ConfirmationModal.Image>
          <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
          <ConfirmationModal.SubTitle>{renderMarkup(messages('subtitle'))}</ConfirmationModal.SubTitle>
        </ConfirmationModal.Header>
        <ConfirmationModal.Footer>
          <Button color="primary" variant="contained" size="md" onClick={closeModal}>
            {messages('okCTA')}
          </Button>
        </ConfirmationModal.Footer>
      </ConfirmationModal.Root>
    </>
  );
};
