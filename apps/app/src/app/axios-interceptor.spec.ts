import { type ResponseError, axiosInstance } from 'app/axios';
import type axios from 'axios';
import { type AxiosError, isAxiosError } from 'axios';
import * as authorization from './authorization';
import { getToken, isTokenExpired } from './authorization';
import { type RefreshableAxiosRequestConfig, onRequest, setupInterceptors } from './axios-interceptor';
import {
  onMaintenanceMode,
  onMutateError,
  onNotAcceptedEndUserAgreement,
  refreshAuthToken,
  setupAuthorizationHeader,
} from './axios-interceptor-handlers';
import { endUnAuthorizedSession } from './session';

jest.mock('./session', () => ({
  endUnAuthorizedSession: jest.fn(),
}));

jest.mock('axios', () => {
  const mockAxios: typeof axios = jest.genMockFromModule('axios');
  mockAxios.create = jest.fn(() => mockAxios);
  mockAxios.interceptors = {
    request: {
      eject: jest.fn(),
      use: jest.fn(),
      clear: jest.fn(),
    },
    response: {
      eject: jest.fn(),
      use: jest.fn(),
      clear: jest.fn(),
    },
  };

  return mockAxios;
});

jest.mock('./axios-interceptor-handlers', () => ({
  onMaintenanceMode: jest.fn(),
  onMutateError: jest.fn(),
  onNotAcceptedEndUserAgreement: jest.fn(),
  refreshAuthToken: jest.fn(),
  setupAuthorizationHeader: jest.fn(),
}));

jest.mock('./authorization', () => ({
  ...jest.requireActual('./authorization'),
  getToken: jest.fn(),
  isTokenExpired: jest.fn(),
}));

describe('axios-interceptor', () => {
  beforeEach(() => {
    jest.restoreAllMocks();
  });

  describe('onRequest', () => {
    const mockConfig = {
      headers: {},
      url: '/api/test',
      method: 'get',
    } as RefreshableAxiosRequestConfig;

    beforeEach(() => {
      jest.spyOn(authorization, 'getToken').mockReturnValue('test-token');
      jest.spyOn(authorization, 'setToken').mockImplementation(() => {});
    });

    describe('when token is valid', () => {
      it('returns request with authorization header', async () => {
        (getToken as jest.Mock).mockReturnValue('test-token');
        (isTokenExpired as jest.Mock).mockReturnValue(false);
        (setupAuthorizationHeader as jest.Mock).mockReturnValue({
          ...mockConfig,
          headers: { Authorization: 'test-token' },
        });

        const result = await onRequest(mockConfig);

        expect(setupAuthorizationHeader).toHaveBeenCalledWith(mockConfig);
        expect(result.headers.Authorization).toBe('test-token');
        expect(refreshAuthToken).not.toHaveBeenCalled();
      });
    });

    describe('when token is expired', () => {
      it('refreshes token and updates authorization header', async () => {
        (getToken as jest.Mock).mockReturnValue('test-token');
        (isTokenExpired as jest.Mock).mockReturnValue(true);
        (setupAuthorizationHeader as jest.Mock).mockReturnValue({
          ...mockConfig,
          headers: { Authorization: 'test-token' },
        });
        (refreshAuthToken as unknown as jest.Mock).mockResolvedValue({
          headers: { authorization: 'new-token' },
        });

        const result = await onRequest(mockConfig);

        expect(refreshAuthToken).toHaveBeenCalled();
        expect(result.headers.Authorization).toBe('new-token');
      });
    });

    describe('when token refresh fails (400)', () => {
      it('rejects with the error', async () => {
        (getToken as jest.Mock).mockReturnValue('test-token');
        (isTokenExpired as jest.Mock).mockReturnValue(true);
        (setupAuthorizationHeader as jest.Mock).mockReturnValue({
          ...mockConfig,
          headers: { Authorization: 'test-token' },
        });
        const error = {
          isAxiosError: true,
          config: { method: 'get' },
          response: { status: 400, config: { url: '/api/some/endpoint' } },
        } as AxiosError;
        (refreshAuthToken as unknown as jest.Mock).mockRejectedValue(error);

        const request = onRequest(mockConfig);

        await expect(request).rejects.toBe(error);
        expect(endUnAuthorizedSession).not.toHaveBeenCalled();
      });
    });

    describe('when token refresh fails (401)', () => {
      it('ends unauthorized session and rejects', async () => {
        (getToken as jest.Mock).mockReturnValue('test-token');
        (isTokenExpired as jest.Mock).mockReturnValue(true);
        (isAxiosError as unknown as jest.Mock).mockReturnValue(true);
        (setupAuthorizationHeader as jest.Mock).mockReturnValue({
          ...mockConfig,
          headers: { Authorization: 'test-token' },
        });
        const error = {
          config: { method: 'get' },
          response: { status: 401, config: { url: '/api/some/endpoint' } },
        } as AxiosError;
        (refreshAuthToken as unknown as jest.Mock).mockRejectedValue(error);

        const request = onRequest(mockConfig);

        await expect(request).rejects.toBe(error);
        expect(endUnAuthorizedSession).toHaveBeenCalled();
      });
    });

    describe('when token is not present', () => {
      it('returns request without authorization header', async () => {
        (getToken as jest.Mock).mockReturnValue(null);
        (isTokenExpired as jest.Mock).mockReturnValue(false);
        (setupAuthorizationHeader as jest.Mock).mockReturnValue({
          ...mockConfig,
          headers: {},
        });

        const result = await onRequest(mockConfig);

        expect(setupAuthorizationHeader).toHaveBeenCalledWith(mockConfig);
        expect(result.headers.Authorization).toBeUndefined();
        expect(refreshAuthToken).not.toHaveBeenCalled();
      });
    });
  });

  it('registers the request and response interceptors on mount', async () => {
    setupInterceptors(axiosInstance);

    expect(axiosInstance.interceptors.request.use).toBeCalledTimes(1);
    expect(axiosInstance.interceptors.request.use).toBeCalledWith(expect.any(Function), undefined);
    expect(axiosInstance.interceptors.response.use).toBeCalledTimes(1);
    expect(axiosInstance.interceptors.response.use).toBeCalledWith(expect.any(Function), expect.any(Function));
  });

  describe('onResponseError', () => {
    describe('when status code is 401', () => {
      it(`calls the 'onUnauthorized and retries request with correct headers'`, async () => {
        jest.spyOn(authorization, 'getToken').mockReturnValueOnce('old-token').mockReturnValueOnce('new-token');
        jest.spyOn(authorization, 'getRefreshToken').mockReturnValue('current-refresh-token');
        const { onResponseError } = await import('./axios-interceptor');
        const error = {
          config: { method: 'get' },
          response: { status: 401, config: { url: '/api/some/endpoint' } },
        } as ResponseError;
        (refreshAuthToken as unknown as jest.Mock).mockResolvedValue({
          headers: { authorization: 'new-token' },
        });

        setupInterceptors(axiosInstance);
        await onResponseError(error);

        expect(refreshAuthToken).toHaveBeenCalledTimes(1);
        expect(axiosInstance).toHaveBeenCalledWith(
          expect.objectContaining({
            headers: expect.objectContaining({
              Authorization: 'new-token',
            }),
          })
        );
        expect((error.config as RefreshableAxiosRequestConfig)._hasRefreshedToken).toBe(true);
      });
    });

    it(`calls the 'onNotAcceptedEndUserAgreement' when status code 403`, async () => {
      const { onResponseError } = await import('./axios-interceptor');
      const error = {
        config: { method: 'post' },
        response: {
          status: 403,
          data: {
            errorCode: 'latest_end_user_agreement_not_accepted',
          },
        },
      } as ResponseError;

      setupInterceptors(axiosInstance);

      await expect(onResponseError(error)).rejects.toEqual(error);
      expect(onNotAcceptedEndUserAgreement).toHaveBeenCalled();
    });

    it(`calls the 'onMaintenanceMode' when status code 503 and proper error code`, async () => {
      const { onResponseError } = await import('./axios-interceptor');
      const error = {
        config: { method: 'post' },
        response: {
          status: 503,
          data: {
            errorCode: 'maintenance',
          },
        },
      } as ResponseError;

      setupInterceptors(axiosInstance);

      await expect(onResponseError(error)).rejects.toEqual(error);
      expect(onMaintenanceMode).toHaveBeenCalled();
    });

    it(`calls the 'onMutateError' when some mutation returns error`, async () => {
      const { onResponseError } = await import('./axios-interceptor');
      const error = {
        config: { method: 'put' },
        request: true,
      } as ResponseError;

      setupInterceptors(axiosInstance);

      await expect(onResponseError(error)).rejects.toEqual(error);
      expect(onMutateError).toHaveBeenCalled();
    });

    it('returns a rejection when there are no handlers', async () => {
      const { onResponseError } = await import('./axios-interceptor');
      const error = {
        config: { method: 'put' },
        response: { status: 500 },
      } as ResponseError;

      setupInterceptors(axiosInstance);

      await expect(onResponseError(error)).rejects.toEqual(error);
    });
  });
});
