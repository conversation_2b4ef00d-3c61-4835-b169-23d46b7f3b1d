@import "tailwindcss";

.str-chat__reaction-selector {
  @apply max-w-fit bg-transparent p-0 shadow-none border-none;
}

.str-chat__message-reactions-list {
  @apply flex! flex-row! space-x-2!;
}

.str-chat__message-actions-list-item {
  color: var(--color-text-neutral-default) !important;
  @apply text-sm! leading-5! font-medium! p-3!;
}

.str-chat__message-reactions-list-item.str-chat__message-reactions-option {
  @apply transition-colors rounded-3xl!;
}

.str-chat__message-reactions-list-item.str-chat__message-reactions-option:hover {
  background-color: var(--color-background-brand-subtle-hovered) !important;
}

.str-chat__reaction-selector .str-chat__message-reaction-emoji {
  @apply text-base py-1 px-1.5 transition-colors;
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", sans-serif;
}

.str-chat__message-reaction-selector .str-chat__message-reactions-options {
  @apply gap-x-0 p-1;
}

.str-chat__message-reactions-list > :not([hidden]) ~ :not([hidden]) {
  @apply ml-0.5!;
}

.str-chat__modal--open .str-chat__modal__inner {
  @apply w-4/5 md:w-[480px];
}

.str-chat__edit-message-form-options {
  @apply gap-2;
}

/* Image */
.str-chat__message-attachment {
  @apply rounded-lg!;
}

/* File attachment */
.str-chat__message-attachment-file--item {
  @apply rounded-lg!;
  @apply border! border-solid! border-gray-200!;
}

/* Unread messages */
.str-chat__unread-messages-separator {
  background-color: rgba(255, 255, 255, 0.25);
}

/* This message was deleted */
.str-chat__message--deleted-inner {
  @apply rounded-lg!;
  background-color: rgba(255, 255, 255, 0.5) !important;
}
