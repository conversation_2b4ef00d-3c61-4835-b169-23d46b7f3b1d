import React from 'react';

import type { LocalMessage } from 'stream-chat';
import { useMessageComposer } from 'stream-chat-react';
import { render, screen, userEvent } from 'tests/test-utils';
import QuotedMessage from './QuotedMessage';

jest.mock('stream-chat-react', () => ({
  ...jest.requireActual('stream-chat-react'),
  useMessageComposer: jest.fn(),
}));

const message: LocalMessage = {
  id: 'message-id-1',
  text: 'Hello world',
  type: 'regular',
  user: {
    id: 'user-1',
    name: '<PERSON>',
    image: 'http://someimgmock.com/img.png',
    language: '',
    role: 'user_with_teams',
  },
  created_at: new Date(),
  updated_at: new Date(),
  deleted_at: null,
  pinned_at: null,
  status: '',
  attachments: [
    {
      type: 'image',
      fallback: 'quote-thumbnail-image',
      image_url: 'http://some.image/image.png',
      original_width: 958,
      original_height: 560,
    },
  ],
};

const messageNoUser: LocalMessage = {
  id: 'message-id-1',
  text: 'Hello world',
  type: 'regular',
  user: {
    id: 'user-1',
    name: undefined,
    image: 'http://someimgmock.com/img.png',
    language: '',
    role: 'user_with_teams',
  },
  created_at: new Date(),
  updated_at: new Date(),
  deleted_at: null,
  pinned_at: null,
  status: '',
  attachments: [
    {
      type: 'image',
      fallback: 'quote-thumbnail-image',
      image_url: 'http://some.image/image.png',
      original_width: 958,
      original_height: 560,
    },
  ],
};

describe('QuotedMessage', () => {
  it('renders correctly with its message and image', async () => {
    const mockUseMessageComposer = jest.fn();
    (useMessageComposer as jest.Mock).mockReturnValueOnce({
      quotedMessage: message,
      setQuotedMessage: mockUseMessageComposer,
    });

    render(<QuotedMessage quotedMessage={message} mode="message" />);

    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Hello world')).toBeInTheDocument();
    expect(screen.getByLabelText('quote-thumbnail-image')).toHaveAttribute('src', 'http://some.image/image.png');
  });

  it('renders Unknown User if no name is provided', async () => {
    const mockSetQuotedMessage = jest.fn();
    (useMessageComposer as jest.Mock).mockReturnValueOnce({
      quotedMessage: messageNoUser,
      setQuotedMessage: mockSetQuotedMessage,
    });

    render(<QuotedMessage quotedMessage={messageNoUser} mode="message" />);

    expect(screen.getByText('Unknown User')).toBeInTheDocument();
    expect(screen.getByText('Hello world')).toBeInTheDocument();
    expect(screen.getByLabelText('quote-thumbnail-image')).toHaveAttribute('src', 'http://some.image/image.png');
  });

  it('renders cancel quote reply when mode is input', async () => {
    const mockSetQuotedMessage = jest.fn();
    (useMessageComposer as jest.Mock).mockReturnValue({
      quotedMessage: message,
      setQuotedMessage: mockSetQuotedMessage,
    });

    render(<QuotedMessage quotedMessage={message} mode="input" />);

    expect(screen.getByLabelText('cancel-quote-reply')).toBeInTheDocument();
  });

  it('handles onPress correctly', async () => {
    const mockSetQuotedMessage = jest.fn();
    (useMessageComposer as jest.Mock).mockReturnValue({
      quotedMessage: message,
      setQuotedMessage: mockSetQuotedMessage,
    });

    render(<QuotedMessage quotedMessage={message} mode="input" />);
    await userEvent.click(screen.getByLabelText('cancel-quote-reply'));

    expect(mockSetQuotedMessage).toHaveBeenCalled();
  });

  it('does not render cancel quote reply when mode is message', async () => {
    const mockSetQuotedMessage = jest.fn();
    (useMessageComposer as jest.Mock).mockReturnValue({
      quotedMessage: message,
      setQuotedMessage: mockSetQuotedMessage,
    });

    render(<QuotedMessage quotedMessage={message} mode="message" />);

    expect(screen.queryByLabelText('cancel-quote-reply')).not.toBeInTheDocument();
  });
});
