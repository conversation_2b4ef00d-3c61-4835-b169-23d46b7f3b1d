import React, { useMemo } from 'react';
import type { LocalMessage, TranslationLanguages } from 'stream-chat';
import {
  type MessageContextValue,
  renderText as defaultRenderText,
  useMessageContext,
  useTranslationContext,
} from 'stream-chat-react';

import { MessageErrorText } from './MessageErrorText';

export type MessageTextProps = {
  /* Replaces the CSS class name placed on the component's inner `div` container */
  customInnerClass?: string;
  /* Adds a CSS class name to the component's outer `div` container */
  customWrapperClass?: string;
  /* The `StreamChat` message object, which provides necessary data to the underlying UI components (overrides the value stored in `MessageContext`) */
  message?: LocalMessage;
  /* Theme string to be added to CSS class names */
  theme?: string;
} & Pick<MessageContextValue, 'renderText'>;

const UnMemoizedMessageTextComponent = (props: MessageTextProps) => {
  const {
    customInnerClass,
    customWrapperClass = '',
    message: propMessage,
    renderText: propsRenderText,
    theme = 'simple',
  } = props;

  const {
    message: contextMessage,
    onMentionsClickMessage,
    onMentionsHoverMessage,
    renderText: contextRenderText,
  } = useMessageContext('MessageText');

  const renderText = propsRenderText ?? contextRenderText ?? defaultRenderText;

  const { userLanguage } = useTranslationContext('MessageText');
  const message = propMessage || contextMessage;

  const messageTextToRender = message.i18n?.[`${userLanguage}_text` as `${TranslationLanguages}_text`] || message.text;

  const messageText = useMemo(
    () => renderText(messageTextToRender, message.mentioned_users),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [message.mentioned_users, messageTextToRender]
  );

  if (!messageTextToRender && !message.quoted_message) return null;

  return (
    <div className={customWrapperClass}>
      <button
        tabIndex={0}
        type="button"
        className={customInnerClass}
        data-testid="message-text-inner-wrapper"
        onClick={onMentionsClickMessage}
        onMouseOver={onMentionsHoverMessage}
        onFocus={onMentionsHoverMessage}
      >
        <MessageErrorText message={message} theme={theme} />

        <div className="whitespace-pre-line text-left text-base leading-6 font-normal text-neutral-bold cursor-text select-text [&_a]:inline-block [&_a]:break-all [&_a]:w-full">
          {messageText}
        </div>
      </button>
    </div>
  );
};

export const MessageText = React.memo(UnMemoizedMessageTextComponent) as typeof UnMemoizedMessageTextComponent;
