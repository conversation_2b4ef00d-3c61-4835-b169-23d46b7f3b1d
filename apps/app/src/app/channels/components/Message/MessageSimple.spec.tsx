import { DATE_TIME_FORMAT_ISO, formatDate } from '@shape-construction/utils/DateTime';
import React from 'react';
import { DialogManagerProvider } from 'stream-chat-react';
import { render, screen } from 'tests/test-utils';
import { MessageSimple } from './MessageSimple';

const mockUseMessageContext = jest.fn();
const mockUseChannelStateContext = jest.fn();
const mockUseDialog = jest.fn();
const mockUseChatContext = jest.fn();
jest.mock('stream-chat-react', () => ({
  ...jest.requireActual('stream-chat-react'),
  useMessageContext: () => mockUseMessageContext(),
  useChannelActionContext: () => jest.fn(),
  useChannelStateContext: () => mockUseChannelStateContext(),
  useChatContext: () => mockUseChatContext(),
  useDialog: () => mockUseDialog(),
  MessageInput: () => (
    <>
      <label htmlFor="message-input">Type your message</label>
      <input id="message-input" type="text" />
    </>
  ),
}));

describe('MessageSimple', () => {
  it('renders a deleted message correctly', () => {
    mockUseMessageContext.mockReturnValue({
      message: { type: 'deleted' },
      isMyMessage: () => false,
    });
    mockUseChannelStateContext.mockReturnValue({
      channel: { type: 'group', state: { messages: [{ id: 'message1', user: { id: 'user1' } }] } },
    });
    mockUseChatContext.mockReturnValue({ client: { user: { id: 'user1' } } });
    mockUseDialog.mockReturnValue({ isOpen: true });

    render(
      <DialogManagerProvider id="dialog">
        <MessageSimple />
      </DialogManagerProvider>
    );

    expect(screen.getByText('This message was deleted...')).toBeInTheDocument();
  });

  it('opens edit modal on edit action', () => {
    mockUseChatContext.mockReturnValue({ client: { user: { id: 'user1' } } });
    mockUseMessageContext.mockReturnValue({
      message: { user: { id: 'user1' } },
      isMyMessage: () => true,
      editing: true,
      clearEditingState: jest.fn(),
    });
    mockUseChannelStateContext.mockReturnValue({
      channel: {
        type: 'messaging',
        state: { messages: [{ id: 'message1', user: { id: 'user1' } }] },
      },
    });
    mockUseDialog.mockReturnValue({ isOpen: true });

    render(
      <DialogManagerProvider id="dialog">
        <MessageSimple />
      </DialogManagerProvider>
    );

    expect(screen.getByLabelText('Type your message')).toBeInTheDocument();
  });

  it('renders a quoted message correctly', () => {
    mockUseMessageContext.mockReturnValue({
      message: {
        user: { id: 'user1' },
        quoted_message: { text: 'This is a quoted message', user: { id: 'user2', name: 'User 2' } },
      },
      isMyMessage: () => false,
      editing: false,
      clearEditingState: jest.fn(),
    });
    mockUseChannelStateContext.mockReturnValue({
      channel: {
        type: 'messaging',
        state: { messages: [{ id: 'message1', user: { id: 'user1' } }] },
      },
    });
    mockUseChatContext.mockReturnValue({ client: { user: { id: 'user1' } } });
    mockUseDialog.mockReturnValue({ isOpen: true });

    render(
      <DialogManagerProvider id="dialog">
        <MessageSimple />
      </DialogManagerProvider>
    );

    expect(screen.getByText('This is a quoted message')).toBeInTheDocument();
  });

  it('renders attachments correctly', () => {
    const attachments = [{ type: 'image' }];
    mockUseMessageContext.mockReturnValue({
      message: { attachments },
      isMyMessage: () => false,
    });
    mockUseChannelStateContext.mockReturnValue({
      channel: {
        type: 'messaging',
        state: { messages: [{ id: 'message1', user: { id: 'user1' } }] },
      },
    });
    mockUseChatContext.mockReturnValue({ client: { user: { id: 'user1' } } });
    mockUseDialog.mockReturnValue({ isOpen: true });

    render(
      <DialogManagerProvider id="dialog">
        <MessageSimple />
      </DialogManagerProvider>
    );

    const attachmentImage = screen.getByTestId('image-test');
    expect(attachmentImage).toBeInTheDocument();
  });

  it('renders message.text correctly', () => {
    mockUseMessageContext.mockReturnValue({
      message: { text: 'Hello, this is a test message' },
      isMyMessage: () => false,
      editing: false,
      clearEditingState: jest.fn(),
    });
    mockUseChannelStateContext.mockReturnValue({
      channel: {
        type: 'messaging',
        state: { messages: [{ id: 'message1', user: { id: 'user1' } }] },
      },
    });
    mockUseChatContext.mockReturnValue({ client: { user: { id: 'user1' } } });
    mockUseDialog.mockReturnValue({ isOpen: true });

    render(
      <DialogManagerProvider id="dialog">
        <MessageSimple />
      </DialogManagerProvider>
    );

    expect(screen.getByText('Hello, this is a test message')).toBeInTheDocument();
  });

  it('renders the message time correctly', () => {
    const testDate = new Date('2024-12-26T12:00:00Z');
    mockUseChatContext.mockReturnValue({ client: { user: { id: 'user1' } } });
    mockUseMessageContext.mockReturnValue({
      message: { created_at: formatDate(testDate, 'Europe/London', DATE_TIME_FORMAT_ISO) },
      isMyMessage: () => false,
    });
    mockUseChannelStateContext.mockReturnValue({
      channel: {
        type: 'messaging',
        state: { messages: [{ id: 'message1', user: { id: 'user1' } }] },
      },
    });
    mockUseDialog.mockReturnValue({ isOpen: true });

    render(
      <DialogManagerProvider id="dialog">
        <MessageSimple />
      </DialogManagerProvider>
    );

    expect(screen.getByText('12:00 pm')).toBeInTheDocument();
  });
});
