import React from 'react';
import {
  EditMessageForm as DefaultEditMessageForm,
  MessageInput,
  Modal,
  useComponentContext,
  useMessageContext,
} from 'stream-chat-react';

export const EditMessageInputModal = () => {
  const { editing, clearEditingState } = useMessageContext();
  const { EditMessageInput = DefaultEditMessageForm } = useComponentContext('MessageSimple');

  if (!editing) return null;

  return (
    <Modal
      arial-label="edit message modal"
      className="str-chat__edit-message-modal "
      onClose={clearEditingState}
      open={editing}
    >
      <MessageInput clearEditingState={clearEditingState} hideSendButton Input={EditMessageInput} />
    </Modal>
  );
};
