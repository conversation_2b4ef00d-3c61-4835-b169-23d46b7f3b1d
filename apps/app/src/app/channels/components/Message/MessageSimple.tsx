import { Avatar as DefaultAvatar, cn } from '@shape-construction/arch-ui';
import { EllipsisHorizontalIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { CHANNELS_TIME_FORMAT, parseDate } from '@shape-construction/utils/DateTime';
import QuotedMessage from 'app/channels/components/Message/QuotedMessage';
import React, { useRef, useState } from 'react';
import {
  DialogAnchor,
  ReactionSelector,
  messageHasReactions,
  useChannelStateContext,
  useChatContext,
  useComponentContext,
  useDialog,
  useMessageContext,
} from 'stream-chat-react';
import { MessageActions as DefaultMessageActions } from '../MessageActions/MessageActions';
import { ReactionsList as DefaultReactionsList } from '../Reactions/ReactionList';
import { Attachment } from './Attachment';
import { EditMessageInputModal } from './EditMessageInputModal';
import { MessageDeleted as DefaultMessageDeleted } from './MessageDeleted';
import { MessagePdfViewer } from './MessagePdfPreview';
import './MessageSimple.css';
import { GROUP_CHANNEL_TYPES } from '../Channel/constants/channelTypes';
import { MessageText } from './MessageText';
import { statusIconMap } from './statusIconMap';

export const MessageSimple = () => {
  const { channel } = useChannelStateContext();
  const { isMyMessage, message } = useMessageContext();
  const { readBy = [] } = useMessageContext('MessageActions');

  const [triggerVisible, setTriggerVisible] = useState(false);

  const channelMessages = channel.state.messages;
  const isGroupChannel = GROUP_CHANNEL_TYPES.includes(channel.type);
  const channelMessagesLength = channelMessages?.length || 0;

  const hasReactions = messageHasReactions(message);

  const index = channelMessages.findIndex((msg) => msg.id === message.id);
  const nextMessage = index !== channelMessagesLength - 1 ? channelMessages[index + 1] : undefined;
  const lastMessageInGroup = index === channelMessagesLength - 1 || nextMessage?.user?.id !== message.user?.id;

  const showAvatar = lastMessageInGroup && isGroupChannel && !isMyMessage();

  const buttonRef = useRef<React.ElementRef<'button'>>(null);
  const dialogId = `${message.id}-actions-dialog`;
  const dialog = useDialog({ id: dialogId });
  const {
    Avatar = DefaultAvatar,
    MessageActions = DefaultMessageActions,
    MessageDeleted = DefaultMessageDeleted,
    ReactionsList = DefaultReactionsList,
  } = useComponentContext('MessageSimple');

  const { client } = useChatContext('MessageSimple');
  const containerRef = useRef<HTMLDivElement | null>(null);
  const [firstUser] = readBy;
  const receivedAndRead = readBy.length > 1 || (firstUser && firstUser.id !== client.user?.id);

  if (message.deleted_at || message.type === 'deleted') return <MessageDeleted message={message} />;

  return (
    <>
      <EditMessageInputModal />
      <button
        type="button"
        className="flex flex-col w-full items-center text-left cursor-default"
        onMouseEnter={() => setTriggerVisible(true)}
        onMouseLeave={() => {
          if (!dialog.isOpen) setTriggerVisible(false);
        }}
      >
        <div
          ref={containerRef}
          className={cn('w-full flex items-end gap-1 relative mb-1', {
            'flex-row-reverse pl-4': isMyMessage(),
            'justify-start flex-row pr-4': !isMyMessage(),
            'mb-4': lastMessageInGroup,
            'mb-7': hasReactions,
          })}
        >
          {showAvatar && (
            <Avatar imgURL={message.user?.image as string} text={message.user?.name as string} size="lg" />
          )}

          <div
            className={cn('str-chat__message-bubble rounded-t-2xl p-1 flex flex-col relative', {
              'lg:max-w-[836px]': isGroupChannel && !isMyMessage(),
              'lg:max-w-[880px]': !isGroupChannel || isMyMessage(),
              'bg-neutral-white rounded-br-2xl': !isMyMessage(),
              'bg-brand-subtle-hovered rounded-bl-2xl': isMyMessage(),
              'ml-11': !isMyMessage() && !lastMessageInGroup && isGroupChannel,
              'lg:min-w-[476px] px-0 py-1': message.attachments?.length,
            })}
          >
            {!isMyMessage() && <span className="text-sm leading-5 font-semibold mt-1 mx-2">{message?.user?.name}</span>}
            {message.quoted_message && (
              <div
                className={cn('px-0.5 pt-0.5 pb-0.5 min-w-full flex-1 lg:min-w-[476px]', {
                  'lg:max-w-[476px]': message.attachments?.length,
                })}
              >
                <QuotedMessage quotedMessage={message.quoted_message} isMyMessage={isMyMessage()} mode="message" />
              </div>
            )}
            {message.attachments?.length && message.attachments?.length > 0 ? (
              <>
                <Attachment attachments={message.attachments} />
                <MessagePdfViewer />
              </>
            ) : null}
            {message.text && (
              <div
                className={cn(
                  'px-2',
                  { 'pt-1': message.attachments?.length || message.quoted_message },
                  { 'pt-1.5': !message.attachments?.length && !message.quoted_message && isMyMessage() },
                  { 'max-w-[476px]': message.attachments?.length }
                )}
              >
                <MessageText />
              </div>
            )}
            <div className="flex flex-row align-center justify-end mx-2">
              {message.created_at && (
                <span className="text-xs font-light text-gray-600 align-center flex flex-row items-center select-text cursor-text">
                  {parseDate(message.created_at).format(CHANNELS_TIME_FORMAT)}
                  {isMyMessage() &&
                    (receivedAndRead
                      ? statusIconMap.receivedAndRead
                      : (statusIconMap[(message?.status ?? 'unknown') as keyof typeof statusIconMap] ??
                        statusIconMap.unknown))}
                </span>
              )}
            </div>
            <div
              className={cn('absolute bottom-[-24px] flex flex-col item-center', {
                'left-2': !isMyMessage(),
                'right-2': isMyMessage(),
              })}
            >
              <ReactionsList />
            </div>
          </div>

          <div className={cn('px-2 self-center flex w-8 relative')}>
            <DialogAnchor
              id={dialogId}
              referenceElement={buttonRef.current}
              placement={isMyMessage() ? 'left' : 'right'}
            >
              <div className="flex flex-col gap-2">
                <div className="self-end">
                  <ReactionSelector />
                </div>
                <div className="bg-neutral-subtle rounded-xl border overflow-hidden">
                  <MessageActions />
                </div>
              </div>
            </DialogAnchor>
            <button
              ref={buttonRef}
              aria-expanded={dialog.isOpen}
              aria-label="Open message actions"
              onClick={() => dialog.toggle(true)}
              type="button"
            >
              <EllipsisHorizontalIcon
                className={cn('transition-opacity', {
                  'opacity-100': triggerVisible,
                  'opacity-0': !triggerVisible,
                })}
              />
            </button>
          </div>
        </div>
      </button>
    </>
  );
};
