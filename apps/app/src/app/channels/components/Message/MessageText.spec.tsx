import React from 'react';
import type { LocalMessage } from 'stream-chat';
import { render, screen } from 'tests/test-utils';
import { MessageText } from './MessageText';

describe('MessageText', () => {
  const baseMessage: LocalMessage = {
    created_at: new Date(),
    id: '1',
    deleted_at: null,
    pinned_at: null,
    status: '',
    type: 'regular',
    updated_at: new Date(),
    text: 'Test message',
  };

  it('renders correctly', async () => {
    render(<MessageText message={baseMessage} />);
    expect(await screen.findByText('Test message')).toBeInTheDocument();
  });
});
