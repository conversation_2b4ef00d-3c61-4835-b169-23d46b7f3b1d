import React from 'react';
import type { LocalMessage } from 'stream-chat';
import { render, screen } from 'tests/test-utils';
import { MessageErrorText } from './MessageErrorText';

jest.mock('stream-chat-react', () => ({
  ...jest.requireActual('stream-chat-react'),
  useTranslationContext: () => ({
    t: (key: string) => key,
  }),
  isMessageBounced: jest.fn(),
}));

describe('MessageErrorText', () => {
  const theme = 'test-theme';

  it('displays error message for non-bounced error type messages', () => {
    const message = { type: 'error' } as LocalMessage;

    render(<MessageErrorText message={message} theme={theme} />);

    expect(screen.getByText('Error · Unsent')).toBeInTheDocument();
  });

  it('displays unauthorized message for failed messages with 403 status code', () => {
    const message = { status: 'failed', error: { status: 403 } } as LocalMessage;

    render(<MessageErrorText message={message} theme={theme} />);

    expect(screen.getByText('Message Failed · Unauthorized')).toBeInTheDocument();
  });

  it('displays generic failed message for failed messages without 403 status code', () => {
    const message = { status: 'failed', error: { status: 401 } } as LocalMessage;

    render(<MessageErrorText message={message} theme={theme} />);

    expect(screen.getByText('Message Failed · Click to try again')).toBeInTheDocument();
  });

  it('renders nothing for messages without error or failed status', () => {
    const message = { type: 'regular' } as LocalMessage;

    const { container } = render(<MessageErrorText message={message} theme={theme} />);

    expect(container).toBeEmptyDOMElement();
  });
});
