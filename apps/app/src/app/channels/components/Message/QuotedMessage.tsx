import { IconButton, cn } from '@shape-construction/arch-ui';
import { CameraIcon, XCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import capitalize from 'lodash.capitalize';
import get from 'lodash.get';
import React, { useMemo } from 'react';
import type { LocalMessage } from 'stream-chat';
import { useChannelActionContext, useMessageComposer } from 'stream-chat-react';

const XWithClasses = () => <XCircleIcon className="text-icon-brand self-center w-7 aspect-square" />;

const QuotedMessage = ({
  quotedMessage,
  isMyMessage,
  mode,
}: {
  quotedMessage: LocalMessage;
  isMyMessage?: boolean;
  mode?: 'input' | 'message';
}) => {
  const {
    messageText,
    authorName = 'Unknown User',
    imageURI,
    isImage,
  } = useMemo(() => {
    if (!quotedMessage) return {};

    return {
      messageText: quotedMessage?.text || capitalize(get(quotedMessage?.attachments, [0, 'type'])),
      authorName: quotedMessage?.user?.name,
      imageURI:
        get(quotedMessage?.attachments, [0, 'image_url']) ||
        get(quotedMessage?.attachments, [0, 'asset_url']) ||
        get(quotedMessage?.attachments, [0, 'thumb_url']),
      isImage: get(quotedMessage?.attachments, [0, 'type']) === 'image',
    };
  }, [quotedMessage]);

  const { setQuotedMessage } = useMessageComposer();
  const { jumpToMessage } = useChannelActionContext('QuotedMessage');

  const renderImage = () =>
    mode === 'input' ? (
      <img
        aria-label="quote-thumbnail-image"
        className="w-12 h-12 ml-2 shrink-0 rounded-md overflow-hidden object-cover self-center"
        src={imageURI}
      />
    ) : (
      <div className="w-16 shrink-0 relative">
        <img
          className="absolute inset-0 w-full h-full object-cover"
          aria-label="quote-thumbnail-image"
          src={imageURI}
        />
      </div>
    );

  const renderBody = () => (
    <div
      className={cn('flex flex-row overflow-hidden flex-1 mx-1 bg-transparent h-min cursor-pointer', {
        'rounded-lg': mode === 'message',
        'bg-neutral-subtle': !isMyMessage,
        'bg-selected': isMyMessage,
      })}
      data-testid="quoted-message"
      onClickCapture={(e) => {
        e.stopPropagation();
        e.preventDefault();
        jumpToMessage(quotedMessage.id);
      }}
    >
      <div className="grow flex flex-row">
        <div className={cn('bg-gray-600 w-1 shrink-0', { 'mr-2': mode === 'input' })} />
        <div
          className={cn('grow flex-1 shrink-0 flex flex-col items-start ', {
            'p-3': mode === 'input',
            'm-1': mode === 'message',
          })}
        >
          <span className="text-xs leading-4 font-semibold text-gray-600 mt-1 line-clamp-1">{authorName}</span>
          <span className="text-xs leading-4 font-normal text-clip text-gray-500 my-2 flex flex-row gap-1">
            {isImage && (
              <div className="h-4 w-4 shrink-0 flex flex-row items-center">
                <CameraIcon className="w-full text-gray-500 flex flex-row items-end" />
              </div>
            )}
            <span className={cn('line-clamp-1', { 'line-clamp-3': mode === 'message' })}>{messageText}</span>
          </span>
        </div>
      </div>
      {isImage && renderImage()}
    </div>
  );

  return mode === 'input' ? (
    <div className="flex flex-row flex-1 bg-neutral-subtle w-full items-center mb-2">
      {renderBody()}
      <IconButton
        aria-label="cancel-quote-reply"
        icon={XWithClasses}
        color="primary"
        size="xs"
        variant="text"
        onClick={() => setQuotedMessage(null)}
      />
    </div>
  ) : (
    renderBody()
  );
};

export default QuotedMessage;
