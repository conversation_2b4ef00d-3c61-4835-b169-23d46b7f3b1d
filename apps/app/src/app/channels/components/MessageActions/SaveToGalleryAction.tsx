import { useMessageGetter } from '@messageformat/react';
import { usePostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments } from '@shape-construction/api/src/hooks';
import type { ErrorSchema } from '@shape-construction/api/src/types';
import { CloudArrowUpIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { Spinner } from '@shape-construction/arch-ui/src/Spinner/Spinner';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { memo, useCallback } from 'react';
import { useParams } from 'react-router';
import { useChatContext, useDialog, useMessageContext } from 'stream-chat-react';
import { DropdownActionButton } from './DropdownActionButton';

type Params = {
  projectId: string;
};

export const SaveToGalleryAction = memo(() => {
  const { projectId: currentProjectId } = useParams<Params>() as Params;

  const messageChannelsMessagesGetter = useMessageGetter('channels.messages');
  const {
    message: { id: messageId },
  } = useMessageContext();
  const { channel } = useChatContext();
  const saveAttachmentsToGallery = usePostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments();

  const dialogId = `${messageId}-actions-dialog`;
  const dialog = useDialog({ id: dialogId });

  const projectId = channel?.data?.shape_project_id ?? currentProjectId;

  const handleSave = useCallback(() => {
    saveAttachmentsToGallery.mutate(
      { messageId, projectId },
      {
        onError: (e) => {
          showErrorToast({ message: (e.response?.data as ErrorSchema).errorDescription });
        },
        onSuccess: () => {
          showSuccessToast({ message: messageChannelsMessagesGetter('saveToGallery.success') });
          if (dialog.isOpen) dialog.close();
        },
      }
    );
  }, [messageId, projectId]);

  return (
    <DropdownActionButton onClick={handleSave} disabled={saveAttachmentsToGallery.isPending}>
      {messageChannelsMessagesGetter('actions.saveToGallery')}
      {saveAttachmentsToGallery.isPending ? <Spinner size="sm" /> : <CloudArrowUpIcon className="w-5 h-5" />}
    </DropdownActionButton>
  );
});
