import { postApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMockHandler } from '@shape-construction/api/handlers-factories/projects';
import * as toasts from '@shape-construction/arch-ui/src/Toast/toasts';
import { channelContextFactory, chatContextValueFactory } from 'app/channels/get-stream/factories/chat-context';
import { createMemoryHistory } from 'history';
import {
  type ChatContextValue,
  ChatProvider,
  DialogManagerProvider,
  MessageContext,
  type MessageContextValue,
} from 'stream-chat-react';
import { server, waitForRequest } from 'tests/mock-server';
import { render, screen, waitFor } from 'tests/test-utils';
import { SaveToGalleryAction } from './SaveToGalleryAction';

const mockMessageContext = { message: { id: 'message-1' } } as MessageContextValue;

const renderComponent = (context: ChatContextValue) => {
  const currentProjectId = 'project-0';
  const history = createMemoryHistory({
    initialEntries: [`/projects/${currentProjectId}/channels/messaging:channel-1`],
  });
  const route = { path: '/projects/:projectId/channels/:channelId' };

  return render(
    <ChatProvider value={context}>
      <MessageContext.Provider value={mockMessageContext}>
        <DialogManagerProvider id={mockMessageContext.message.id}>
          <SaveToGalleryAction />
        </DialogManagerProvider>
      </MessageContext.Provider>
    </ChatProvider>,
    { route, history }
  );
};

describe('<SaveToGalleryAction />', () => {
  const spyOnToast = jest.spyOn(toasts, 'showSuccessToast');

  it('renders the SaveToGalleryAction action', async () => {
    const chatContextValue = chatContextValueFactory({
      channel: channelContextFactory({ type: 'team', id: 'channel-1' }),
    });
    const { user } = renderComponent(chatContextValue);

    expect(screen.getByText('channels.messages.actions.saveToGallery')).toBeInTheDocument();
  });

  describe('when channel data has project id', () => {
    it('sends the correct arguments to the saveAttachmentsToGallery mutation', async () => {
      const saveAttachmentsToGalleryRequest = waitForRequest(
        'POST',
        '/api/projects/:projectId/channels/messages/:messageId/save_attachments'
      );
      server.use(postApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMockHandler());
      const channelProjectId = 'project-1';
      const channelTeamId = 'team-1';
      const chatContextValue = chatContextValueFactory({
        channel: channelContextFactory({
          type: 'team',
          id: 'channel-1',
          data: { shape_project_id: channelProjectId, shape_team_id: channelTeamId },
        }),
      });
      const { user } = renderComponent(chatContextValue);

      await user.click(screen.getByText('channels.messages.actions.saveToGallery'));
      const saveAttachmentsToGalleryResponse = await saveAttachmentsToGalleryRequest;

      expect(new URL(saveAttachmentsToGalleryResponse.url).pathname.includes(channelProjectId)).toBe(true);
      expect(new URL(saveAttachmentsToGalleryResponse.url).pathname.includes('message-1')).toBe(true);
    });
  });

  describe('when channel data has no project id', () => {
    it('sends the current projectId from url param to the saveAttachmentsToGallery mutation', async () => {
      const saveAttachmentsToGalleryRequest = waitForRequest(
        'POST',
        '/api/projects/:projectId/channels/messages/:messageId/save_attachments'
      );
      server.use(postApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMockHandler());
      const chatContextValue = chatContextValueFactory({
        channel: channelContextFactory({ type: 'team', id: 'channel-1' }),
      });
      const { user } = renderComponent(chatContextValue);

      await user.click(screen.getByText('channels.messages.actions.saveToGallery'));
      const saveAttachmentsToGalleryResponse = await saveAttachmentsToGalleryRequest;

      expect(new URL(saveAttachmentsToGalleryResponse.url).pathname.includes('project-0')).toBe(true);
      expect(new URL(saveAttachmentsToGalleryResponse.url).pathname.includes('message-1')).toBe(true);
    });
  });

  describe('when save_attachments is successfully called', () => {
    beforeEach(() => {
      server.use(postApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMockHandler());
      const { user } = render(
        <MessageContext.Provider value={mockMessageContext}>
          <DialogManagerProvider id={mockMessageContext.message.id}>
            <SaveToGalleryAction />
          </DialogManagerProvider>
        </MessageContext.Provider>
      );

      user.click(screen.getByText('channels.messages.actions.saveToGallery'));
    });

    it('shows a success toast', async () => {
      await waitFor(() =>
        expect(spyOnToast).toHaveBeenCalledWith({ message: 'channels.messages.saveToGallery.success' })
      );
    });

    it('should close dialog', async () => {
      await waitFor(() => {
        expect(screen.queryByRole('dialog')).not.toBeInTheDocument();
      });
    });
  });
});
