import React from 'react';
import type { LocalMessage } from 'stream-chat';
import { DialogManagerProvider, type MessageContextValue, MessageProvider } from 'stream-chat-react';
import { useBaseMessageActionSetFilter } from 'stream-chat-react/experimental';
import { render, screen } from 'tests/test-utils';
import { MessageActions } from './MessageActions';
import { defaultMessageActionSet } from './defaults';

jest.mock('stream-chat-react', () => ({
  ...jest.requireActual('stream-chat-react'),
  useMessageComposer: () => ({ setQuotedMessage: jest.fn() }),
}));

jest.mock('stream-chat-react/experimental', () => ({
  ...jest.requireActual('stream-chat-react/experimental'),
  useBaseMessageActionSetFilter: jest.fn(),
}));

describe('<MessageActions />', () => {
  it('renders all used actions', async () => {
    (useBaseMessageActionSetFilter as jest.Mock).mockReturnValue(defaultMessageActionSet);
    const messageContextValue = { message: {} } as MessageContextValue;

    render(
      <MessageProvider value={messageContextValue}>
        <DialogManagerProvider id="dialog">
          <MessageActions />
        </DialogManagerProvider>
      </MessageProvider>
    );

    expect(screen.getByRole('listbox')).toBeInTheDocument();
    expect(screen.getByRole('option', { name: 'channels.messages.actions.reply' })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: 'channels.messages.actions.copy' })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: 'channels.messages.actions.edit' })).toBeInTheDocument();
    expect(screen.getByRole('option', { name: 'channels.messages.actions.delete' })).toBeInTheDocument();
  });

  describe('when there are no actions', () => {
    it('does not render the list of actions', () => {
      (useBaseMessageActionSetFilter as jest.Mock).mockReturnValue([]);
      const messageContextValue = { message: {} } as MessageContextValue;

      render(
        <MessageProvider value={messageContextValue}>
          <DialogManagerProvider id="dialog">
            <MessageActions />
          </DialogManagerProvider>
        </MessageProvider>
      );

      expect(screen.queryByRole('listbox')).not.toBeInTheDocument();
    });
  });

  describe('when there are attachments', () => {
    it('should render Save to gallery action', async () => {
      (useBaseMessageActionSetFilter as jest.Mock).mockReturnValue(defaultMessageActionSet);
      const messageContextValue = {
        message: {
          attachments: [{ asset_url: 'https://example.com/attachment.pdf' }],
          created_at: new Date(),
          deleted_at: null,
          id: 'message-1',
          pinned_at: null,
          updated_at: new Date(),
          status: '',
          type: 'regular',
        } as LocalMessage,
      } as MessageContextValue;

      render(
        <MessageProvider value={messageContextValue}>
          <DialogManagerProvider id="dialog">
            <MessageActions />
          </DialogManagerProvider>
        </MessageProvider>
      );

      expect(screen.getByRole('option', { name: 'channels.messages.actions.saveToGallery' })).toBeInTheDocument();
    });
  });
  describe('when there are no attachments', () => {
    it('does not render Save to gallery action', () => {
      (useBaseMessageActionSetFilter as jest.Mock).mockReturnValue(defaultMessageActionSet);
      const messageContextValue = { message: {} } as MessageContextValue;

      render(
        <MessageProvider value={messageContextValue}>
          <DialogManagerProvider id="dialog">
            <MessageActions />
          </DialogManagerProvider>
        </MessageProvider>
      );

      expect(screen.queryByRole('option', { name: 'channels.messages.actions.saveToGallery' })).not.toBeInTheDocument();
    });
  });
});
