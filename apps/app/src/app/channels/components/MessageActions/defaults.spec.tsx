import * as toasts from '@shape-construction/arch-ui/src/Toast/toasts';
import {
  DialogManagerProvider,
  type MessageContextValue,
  MessageProvider,
  useDialog,
  useMessageComposer,
  useMessageContext,
} from 'stream-chat-react';
import { render, screen } from 'tests/test-utils';
import { defaultMessageActionSet } from './defaults';

jest.mock('stream-chat-react', () => ({
  ...jest.requireActual('stream-chat-react'),
  useMessageContext: jest.fn(),
  useMessageComposer: jest.fn(),
  useDialog: jest.fn(),
}));

const mockUseMessageComposer = {
  setQuotedMessage: jest.fn(),
};

const renderComponent = (Component: React.ComponentType) => {
  return render(
    <DialogManagerProvider id="dialog-1">
      <Component />
    </DialogManagerProvider>
  );
};

describe('defaultMessageActionSet', () => {
  const mockMessageContext = {
    message: { text: 'test message', pinned: false },
    handlePin: jest.fn(),
    handleEdit: jest.fn(),
    handleDelete: jest.fn(),
  };
  const mockDialog = { isOpen: true, close: jest.fn(), remove: jest.fn() };

  beforeEach(() => {
    (useMessageContext as jest.Mock).mockReturnValue(mockMessageContext);
    (useMessageComposer as jest.Mock).mockReturnValue(mockUseMessageComposer);
    (useDialog as jest.Mock).mockReturnValue(mockDialog);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should set reply message and focus textarea on Quote action', async () => {
    const Reply = defaultMessageActionSet[0].Component;
    const { user } = renderComponent(Reply);

    await user.click(screen.getByRole('option', { name: 'channels.messages.actions.reply' }));

    expect(mockUseMessageComposer.setQuotedMessage).toHaveBeenCalledWith(mockMessageContext.message);
    expect(mockDialog.close).toHaveBeenCalled();
  });

  // With this new testing-library/user-event version, this stopped working
  // Until we found another way to test this, lets leave this test commented
  xit('copies message text to clipboard and show toast on Copy action', async () => {
    const spyOnToast = jest.spyOn(toasts, 'showInfoToast');
    const messageContextValue = { message: { text: 'test message' } } as MessageContextValue;
    const Copy = defaultMessageActionSet[2].Component;
    const { user } = render(
      <MessageProvider value={messageContextValue}>
        <DialogManagerProvider>
          <Copy />
        </DialogManagerProvider>
      </MessageProvider>
    );

    await user.click(screen.getByRole('option', { name: 'channels.messages.actions.copy' }));

    // Read from the stub clipboard
    const clipboardText = await navigator.clipboard.readText();
    expect(clipboardText).toBe('test message');
    expect(spyOnToast).toHaveBeenCalledWith({ message: 'channels.messages.copyMessage' });
  });

  it('should call handleEdit on Edit action', async () => {
    const Edit = defaultMessageActionSet[3].Component;
    const { user } = renderComponent(Edit);

    await user.click(screen.getByRole('option', { name: 'channels.messages.actions.edit' }));

    expect(mockMessageContext.handleEdit).toHaveBeenCalled();
    expect(mockDialog.close).toHaveBeenCalled();
  });

  it('should call handleDelete on Delete action', async () => {
    const Delete = defaultMessageActionSet[4].Component;
    const { user } = renderComponent(Delete);

    await user.click(screen.getByRole('option', { name: 'channels.messages.actions.delete' }));

    expect(mockMessageContext.handleDelete).toHaveBeenCalled();
    expect(mockDialog.remove).toHaveBeenCalled();
  });
});
