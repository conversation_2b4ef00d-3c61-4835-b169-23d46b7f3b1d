import { useMessageGetter } from '@messageformat/react';
import {
  ArrowUturnLeftIcon,
  PencilSquareIcon,
  Square2StackIcon,
  TrashIcon,
} from '@shape-construction/arch-ui/src/Icons/outline';
import { showInfoToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useDialog, useMessageComposer, useMessageContext } from 'stream-chat-react';
import type { MessageActionSetItem } from 'stream-chat-react/experimental';
import { DropdownActionButton } from './DropdownActionButton';
import { SaveToGalleryAction } from './SaveToGalleryAction';

const DefaultMessageActionComponents = {
  dropdown: {
    Copy: () => {
      const { message } = useMessageContext();
      const messageGetter = useMessageGetter('channels.messages');

      const dialogId = `${message.id}-actions-dialog`;
      const dialog = useDialog({ id: dialogId });

      return (
        <DropdownActionButton
          onClick={() => {
            navigator.clipboard.writeText(message.text ?? '');
            showInfoToast({ message: messageGetter('copyMessage') });
            if (dialog.isOpen) dialog.close();
          }}
        >
          {messageGetter('actions.copy')}
          <Square2StackIcon className="w-5 h-5" />
        </DropdownActionButton>
      );
    },
    Quote: () => {
      const { setQuotedMessage } = useMessageComposer();
      const { message } = useMessageContext();
      const messageGetter = useMessageGetter('channels.messages.actions');

      const dialogId = `${message.id}-actions-dialog`;
      const dialog = useDialog({ id: dialogId });

      const handleQuote = () => {
        setQuotedMessage(message);

        const elements = message.parent_id
          ? document.querySelectorAll('.str-chat__thread .str-chat__textarea__textarea')
          : document.getElementsByClassName('str-chat__textarea__textarea');
        const textarea = elements.item(0);

        if (textarea instanceof HTMLTextAreaElement) textarea.focus();
        if (dialog.isOpen) dialog.close();
      };

      return (
        <DropdownActionButton onClick={handleQuote}>
          {messageGetter('reply')}
          <ArrowUturnLeftIcon className="w-5 h-5" />
        </DropdownActionButton>
      );
    },
    Edit: () => {
      const { message } = useMessageContext();
      const { handleEdit } = useMessageContext();
      const messageGetter = useMessageGetter('channels.messages.actions');

      const dialogId = `${message.id}-actions-dialog`;
      const dialog = useDialog({ id: dialogId });

      const onEdit = (event: React.BaseSyntheticEvent) => {
        handleEdit(event);
        if (dialog.isOpen) dialog.close();
      };

      return (
        <DropdownActionButton onClick={onEdit}>
          {messageGetter('edit')}
          <PencilSquareIcon className="w-5 h-5" />
        </DropdownActionButton>
      );
    },
    Delete: () => {
      const { message } = useMessageContext();
      const { handleDelete } = useMessageContext();
      const messageGetter = useMessageGetter('channels.messages.actions');

      const dialogId = `${message.id}-actions-dialog`;
      const dialog = useDialog({ id: dialogId });

      const onDelete = (event: React.BaseSyntheticEvent) => {
        handleDelete(event);
        dialog.remove();
      };

      return (
        <DropdownActionButton className="text-icon-danger-bold" onClick={onDelete}>
          {messageGetter('delete')}
          <TrashIcon className="w-5 h-5" />
        </DropdownActionButton>
      );
    },
    SaveToGallery: SaveToGalleryAction,
  },
};

export const defaultMessageActionSet: MessageActionSetItem[] = [
  {
    Component: DefaultMessageActionComponents.dropdown.Quote,
    placement: 'dropdown',
    type: 'quote',
  },
  {
    Component: DefaultMessageActionComponents.dropdown.SaveToGallery,
    placement: 'dropdown',
    type: 'saveToGallery',
  },
  {
    Component: DefaultMessageActionComponents.dropdown.Copy,
    placement: 'dropdown',
    type: 'copy',
  },
  { Component: DefaultMessageActionComponents.dropdown.Edit, placement: 'dropdown', type: 'edit' },
  {
    Component: DefaultMessageActionComponents.dropdown.Delete,
    placement: 'dropdown',
    type: 'delete',
  },
] as const;
