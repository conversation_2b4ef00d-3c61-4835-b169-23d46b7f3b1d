import type { UserSchema } from '@shape-construction/api/src';
import type { StreamChat, UserResponse } from 'stream-chat';

export function intersect<T>(lists?: T[][]): T[] {
  if (!lists?.length) return [];
  if (lists.length === 1) return lists[0];

  const [first, ...rest] = lists;
  const result = first.filter((item) => rest.every((list) => list.includes(item)));

  return [...new Set(result)];
}

export const createPersonalChannel = async (
  client: StreamChat,
  shapeChannelsUser: NonNullable<UserSchema['channels']>
) => {
  const userId = shapeChannelsUser.streamChatUserId;
  const team = shapeChannelsUser.streamChatTeam;
  const conversation = client.channel('personal', userId, { team, members: [userId] });
  await conversation.watch();
  await conversation.pin();

  return conversation;
};

export const createMessagingChannel = async (client: StreamChat, streamUser: UserResponse, contact: UserResponse) => {
  const [team] = intersect([streamUser, contact]?.map(({ teams = [] }) => teams));
  const conversation = client.channel('messaging', null, { team, members: [contact.id, streamUser.id] });
  await conversation.watch();

  return conversation;
};
