import type { StreamChat } from 'stream-chat';

import { userFactory as streamUserFactory } from '@shape-construction/api/channels/factories/user';
import { userChannelsFactory } from '@shape-construction/api/factories/users';

import { createMessagingChannel, createPersonalChannel, intersect } from './createChannel';

const createStreamClient = (user = streamUserFactory()) => {
  const channel = jest.fn().mockReturnValue({
    watch: jest.fn().mockResolvedValue(undefined),
    pin: jest.fn().mockResolvedValue(undefined),
  });
  const client = { channel, user } as unknown as jest.Mocked<StreamChat>;

  return { client, channel };
};

describe('createChannel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('intersect', () => {
    describe('when no input is provided', () => {
      it('returns an empty array', () => {
        expect(intersect()).toEqual([]);
      });
    });

    describe('when empty array is provided', () => {
      it('returns an empty array', () => {
        expect(intersect([])).toEqual([]);
      });
    });

    describe('when only one array is provided', () => {
      it('returns the array itself', () => {
        const input = [1, 2, 3];
        expect(intersect([input])).toEqual(input);
      });
    });

    describe('when two arrays are provided', () => {
      it('returns the intersection of the two arrays', () => {
        const arr1 = [1, 2, 3, 4];
        const arr2 = [3, 4, 5, 6];
        expect(intersect([arr1, arr2])).toEqual([3, 4]);
      });
    });

    describe('when multiple arrays are provided', () => {
      it('returns the intersection of the arrays', () => {
        const arr1 = [1, 2, 3, 4, 5];
        const arr2 = [2, 3, 4, 5, 6];
        const arr3 = [3, 4, 5, 6, 7];
        expect(intersect([arr1, arr2, arr3])).toEqual([3, 4, 5]);
      });
    });

    describe('when arrays of strings are provided', () => {
      it('returns the intersection of the arrays', () => {
        const arr1 = ['a', 'b', 'c'];
        const arr2 = ['b', 'c', 'd'];
        const arr3 = ['c', 'd', 'e'];
        expect(intersect([arr1, arr2, arr3])).toEqual(['c']);
      });
    });

    describe('when there is no intersection', () => {
      it('returns an empty array', () => {
        const arr1 = [1, 2, 3];
        const arr2 = [4, 5, 6];
        expect(intersect([arr1, arr2])).toEqual([]);
      });
    });

    describe('when arrays with duplicate values are provided', () => {
      it('returns the intersection of the arrays', () => {
        const arr1 = [1, 1, 2, 2, 3];
        const arr2 = [2, 2, 3, 3, 4];
        expect(intersect([arr1, arr2])).toEqual([2, 3]);
      });
    });
  });

  describe('createPersonalChannel', () => {
    it('creates a personal channel successfully', async () => {
      const channelsUser = userChannelsFactory({ streamChatUserId: 'user1', streamChatTeam: 'team1' });
      const { client, channel } = createStreamClient();

      await createPersonalChannel(client, channelsUser!);

      expect(channel).toHaveBeenCalledWith('personal', 'user1', { team: 'team1', members: ['user1'] });
      const channelInstance = channel();
      expect(channelInstance.watch).toHaveBeenCalled();
      expect(channelInstance.pin).toHaveBeenCalled();
    });
  });

  describe('createMessagingChannel', () => {
    it('creates a messaging channel successfully', async () => {
      const user = streamUserFactory({ id: 'user1', teams: ['team1', 'team2'] });
      const contact = streamUserFactory({ id: 'user2', teams: ['team1', 'team3'] });
      const { client, channel } = createStreamClient(user);

      await createMessagingChannel(client, user, contact);

      expect(channel).toHaveBeenCalledWith('messaging', null, {
        team: 'team1',
        members: ['user2', 'user1'],
      });
      const channelInstance = channel();
      expect(channelInstance.watch).toHaveBeenCalled();
    });
  });
});
