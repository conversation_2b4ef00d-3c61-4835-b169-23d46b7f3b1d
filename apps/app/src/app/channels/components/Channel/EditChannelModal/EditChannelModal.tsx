import { useMessageGetter } from '@messageformat/react';
import type { ComponentProps } from 'react';
import { useChannelStateContext } from 'stream-chat-react';

import { usePatchApiProjectsProjectIdGroupsGroupId } from '@shape-construction/api/src';
import { Avatar, Button, FileUpload, IconButton, InputText, Modal } from '@shape-construction/arch-ui';
import { TrashIcon, UsersIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { DirectUpload } from 'app/lib/direct-upload/direct-upload';
import { Controller, useForm } from 'react-hook-form';

export type EditChannelFormValues = {
  avatar?: string | File | null;
  name?: string;
};

export const EditChannelModal = (props: ComponentProps<typeof Modal.Root>) => {
  const message = useMessageGetter('channels.details.edit');
  const { channel } = useChannelStateContext();
  const editChannelMutation = usePatchApiProjectsProjectIdGroupsGroupId();
  const { register, control, formState, handleSubmit, watch, setValue } = useForm<EditChannelFormValues>({
    values: {
      avatar: channel?.data?.image,
      name: channel?.data?.name,
    },
  });
  const hasPhoto = Boolean(watch('avatar'));
  const onEditGroup = handleSubmit(async (values) => {
    const avatar = values.avatar instanceof File ? await DirectUpload(values.avatar, 'image') : values.avatar;

    try {
      return await editChannelMutation.mutateAsync(
        {
          projectId: channel?.data?.shape_project_id as string,
          groupId: channel?.data?.shape_group_id as string,
          data: {
            name: values.name,
            avatar,
          },
        },
        {
          onSuccess: () => {
            showSuccessToast({ message: message('success') });
            props.onClose();
          },
        }
      );
    } catch {
      showErrorToast({ message: message('error') });
    }
  });

  return (
    <Modal.Root {...props}>
      <Modal.Header>
        <Modal.Title>{message('title')}</Modal.Title>
      </Modal.Header>
      <Modal.Content className="py-4 flex flex-col gap-4 items-center">
        <Controller
          name="avatar"
          control={control}
          render={({ field }) => (
            <div className="flex flex-col justify-center items-center">
              <FileUpload.Root id="group-image-upload" accept="image/*" onChange={(files) => field.onChange(files[0])}>
                <FileUpload.Trigger>
                  <div className="relative cursor-pointer">
                    <Controller
                      name="avatar"
                      control={control}
                      render={({ field }) => {
                        const avatar = field.value;
                        const isFile = avatar instanceof File;
                        const imageUrl = isFile ? URL.createObjectURL(avatar) : avatar;

                        return (
                          <Avatar
                            text="group avatar"
                            imgURL={imageUrl}
                            icon={<UsersIcon className="h-10 w-10 text-icon-neutral-inverse" />}
                            size="3xl"
                          />
                        );
                      }}
                    />
                    <FileUpload.Label className="absolute inset-0 flex h-full w-full items-center justify-center rounded-full bg-black/60 text-xs font-medium text-white opacity-0 transition-opacity hover:opacity-100 active:opacity-100">
                      {hasPhoto ? message('editPhoto') : message('addPhoto')}
                    </FileUpload.Label>
                  </div>
                </FileUpload.Trigger>
              </FileUpload.Root>
              {hasPhoto && (
                <IconButton
                  size="xs"
                  aria-label={message('actions.removePhoto')}
                  icon={TrashIcon}
                  color="secondary"
                  variant="text"
                  onClick={() => setValue('avatar', null, { shouldDirty: true })}
                />
              )}
            </div>
          )}
        />

        <InputText fullWidth maxLength={50} placeholder={message('titlePlaceholder')} {...register('name')} />
      </Modal.Content>
      <Modal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={() => props.onClose()}>
          {message('actions.cancel')}
        </Button>
        <Button
          disabled={formState.isSubmitting || !formState.isDirty}
          color="primary"
          variant="contained"
          size="md"
          type="submit"
          onClick={onEditGroup}
        >
          {message('actions.save')}
        </Button>
      </Modal.Footer>
    </Modal.Root>
  );
};
