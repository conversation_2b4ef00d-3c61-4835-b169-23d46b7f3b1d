import { useMessageGetter } from '@messageformat/react';
import type { ComponentProps } from 'react';
import { useChannelStateContext } from 'stream-chat-react';

import { useDeleteApiProjectsProjectIdGroupsGroupId } from '@shape-construction/api/src';
import { Button, ConfirmationModal, IconBadge } from '@shape-construction/arch-ui';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { showErrorToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { DIRECT_MESSAGE_CHANNEL_TYPES } from '../constants/channelTypes';

export const DeleteChannelConfirmationModal = (props: ComponentProps<typeof ConfirmationModal.Root>) => {
  const message = useMessageGetter('channels.details.deleteConfirmation');
  const { channel } = useChannelStateContext();
  const deleteGroupMutation = useDeleteApiProjectsProjectIdGroupsGroupId();

  const onDeleteGroup = async () => {
    try {
      if (DIRECT_MESSAGE_CHANNEL_TYPES.includes(channel.type)) {
        await channel.delete();
      } else if (channel.type === 'group') {
        await deleteGroupMutation.mutateAsync({
          projectId: channel.data?.shape_project_id as string,
          groupId: channel.data?.shape_group_id as string,
        });
      }
    } catch (error) {
      showErrorToast({ message: message('error') });
    } finally {
      props.onClose();
    }
  };

  return (
    <ConfirmationModal.Root {...props}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="danger">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{message('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>{message('subTitle')}</ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button color="secondary" variant="outlined" size="md" onClick={() => props.onClose()}>
          {message('cancelCTA')}
        </Button>
        <Button color="danger" variant="contained" size="md" onClick={onDeleteGroup}>
          {message('deleteCTA')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
