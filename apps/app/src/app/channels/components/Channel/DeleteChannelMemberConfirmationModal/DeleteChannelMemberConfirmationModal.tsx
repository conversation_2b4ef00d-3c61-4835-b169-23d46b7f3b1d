import { useMessageGetter } from '@messageformat/react';
import { usePatchApiProjectsProjectIdGroupsGroupIdMembers } from '@shape-construction/api/src';
import { Button, ConfirmationModal } from '@shape-construction/arch-ui';
import { showErrorToast, showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useQueryClient } from '@tanstack/react-query';
import { useCurrentUser } from 'app/queries/users/users';
import type { ComponentProps } from 'react';
import type { ChannelMemberResponse } from 'stream-chat';
import { useChannelStateContext } from 'stream-chat-react';
import { useChannelMembersQueryOptions } from '../hooks/useChannelMembers';

export const DeleteChannelMemberConfirmationModal = ({
  member,
  ...props
}: ComponentProps<typeof ConfirmationModal.Root> & { member: ChannelMemberResponse }) => {
  const queryClient = useQueryClient();
  const user = useCurrentUser();
  const { channel } = useChannelStateContext();
  const channelMembersQueryOptions = useChannelMembersQueryOptions(channel);
  const {
    mutate: removeMemberMutate,
    isPending: isRemoveMemberPending,
    isSuccess: isRemoveMemberSuccess,
  } = usePatchApiProjectsProjectIdGroupsGroupIdMembers();
  const message = useMessageGetter('channels.details.removeMembers');
  const onRemoveMember = () => {
    removeMemberMutate(
      {
        projectId: channel.data?.shape_project_id as string,
        groupId: channel.data?.shape_group_id as string,
        data: {
          remove_user_ids: [member.user?.shape_user_id as string],
        },
      },
      {
        onSuccess: () => {
          showSuccessToast({
            message: message('notifications.removeMemberSuccess', {
              username: user.name,
              member: member.user?.name,
            }),
          });
        },
        onSettled: async () => {
          await queryClient.invalidateQueries(channelMembersQueryOptions);
          props.onClose();
        },
        onError: () => {
          showErrorToast({ message: message('notifications.removeMemberError') });
        },
      }
    );
  };

  return (
    <ConfirmationModal.Root {...props}>
      <ConfirmationModal.Header>
        <ConfirmationModal.Title>{message('removeMemberConfirmation.title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>
          {message('removeMemberConfirmation.description', { member: member.user?.name })}
        </ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button color="secondary" variant="text" size="md" onClick={() => props.onClose()}>
          {message('removeMemberConfirmation.actions.cancel')}
        </Button>
        <Button
          color="primary"
          variant="text"
          size="md"
          onClick={onRemoveMember}
          disabled={isRemoveMemberPending || isRemoveMemberSuccess}
        >
          {message('removeMemberConfirmation.actions.delete')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
