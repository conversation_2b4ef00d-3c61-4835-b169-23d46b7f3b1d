import { channelMemberFactory } from '@shape-construction/api/channels/factories/channel';
import { userFactory } from '@shape-construction/api/channels/factories/user';
import { patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler } from '@shape-construction/api/handlers-factories/projects/groups';

import { server } from 'tests/mock-server';
import { findAsyncToaster, render, screen, userEvent } from 'tests/test-utils';

import { DeleteChannelMemberConfirmationModal } from './DeleteChannelMemberConfirmationModal';

const mockMember = channelMemberFactory({
  user_id: 'billy-1',
  is_moderator: false,
  user: userFactory({
    shape_user_id: 'billy-shape-1',
    id: 'billy-1',
    name: '<PERSON>',
    email: '<EMAIL>',
    image: 'https://example.com/avatar.jpg',
  }),
});

const mockOnClose = jest.fn();

describe('<DeleteChannelMemberConfirmationModal />', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders confirmation modal correctly', () => {
    render(<DeleteChannelMemberConfirmationModal open member={mockMember} onClose={jest.fn()} />);

    expect(screen.getByText('channels.details.removeMembers.removeMemberConfirmation.title')).toBeInTheDocument();
    expect(screen.getByText('channels.details.removeMembers.removeMemberConfirmation.description')).toBeInTheDocument();
    expect(
      screen.getByText('channels.details.removeMembers.removeMemberConfirmation.actions.cancel')
    ).toBeInTheDocument();
    expect(
      screen.getByText('channels.details.removeMembers.removeMemberConfirmation.actions.delete')
    ).toBeInTheDocument();
  });

  it('shows success toast on confirm', async () => {
    render(<DeleteChannelMemberConfirmationModal open member={mockMember} onClose={mockOnClose} />, {
      renderToast: true,
    });

    await userEvent.click(
      screen.getByRole('button', {
        name: 'channels.details.removeMembers.removeMemberConfirmation.actions.delete',
      })
    );

    expect(
      await findAsyncToaster('channels.details.removeMembers.notifications.removeMemberSuccess')
    ).toBeInTheDocument();
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('shows error toast on mutation failure', async () => {
    server.use(patchApiProjectsProjectIdGroupsGroupIdMembersMockHandler(() => undefined, { status: 500 }));

    render(<DeleteChannelMemberConfirmationModal open member={mockMember} onClose={mockOnClose} />, {
      renderToast: true,
    });

    await userEvent.click(
      screen.getByRole('button', {
        name: 'channels.details.removeMembers.removeMemberConfirmation.actions.delete',
      })
    );

    expect(
      await findAsyncToaster('channels.details.removeMembers.notifications.removeMemberError')
    ).toBeInTheDocument();
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('disables buttons if mutation isPending and isSuccess', async () => {
    render(<DeleteChannelMemberConfirmationModal open member={mockMember} onClose={mockOnClose} />);

    await userEvent.click(
      screen.getByRole('button', {
        name: 'channels.details.removeMembers.removeMemberConfirmation.actions.delete',
      })
    );

    expect(
      screen.getByRole('button', {
        name: 'channels.details.removeMembers.removeMemberConfirmation.actions.delete',
      })
    ).toBeDisabled();
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('closes modal when cancel is clicked', async () => {
    render(<DeleteChannelMemberConfirmationModal open member={mockMember} onClose={mockOnClose} />);

    await userEvent.click(screen.getByText('channels.details.removeMembers.removeMemberConfirmation.actions.cancel'));

    expect(mockOnClose).toHaveBeenCalled();
  });
});
