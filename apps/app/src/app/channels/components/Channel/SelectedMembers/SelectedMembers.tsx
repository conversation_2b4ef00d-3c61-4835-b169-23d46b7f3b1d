import { composeRefs } from '@shape-construction/arch-ui';
import React, { type ComponentPropsWithoutRef, type ElementRef, forwardRef, useRef, useEffect } from 'react';
import type { UserResponse } from 'stream-chat';

import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import { UserIcon, XMarkIcon } from '@shape-construction/arch-ui/src/Icons/solid';

export interface SelectedMembersProps extends ComponentPropsWithoutRef<'ul'> {
  members: UserResponse[];
  onRemoveContact: (id: UserResponse['id'], index: number) => void;
}

export const SelectedMembers = forwardRef<ElementRef<'ul'>, SelectedMembersProps>(
  ({ members, onRemoveContact, ...props }, ref) => {
    const innerRef = useRef<ElementRef<'ul'>>(null);

    useEffect(() => {
      innerRef.current?.scrollTo({ left: innerRef.current.scrollWidth, behavior: 'smooth' });
    }, [members]);

    return (
      <ul
        ref={composeRefs(innerRef, ref)}
        aria-label="selected users"
        className="flex flex-row overflow-x-auto pb-1.5 px-2"
        {...props}
      >
        {members?.map((member, index) => (
          <button
            type="button"
            key={member.id}
            className="shrink-0 w-16 relative flex flex-col items-center gap-1.5 mr-4"
            onClick={() => onRemoveContact(member.id, index)}
          >
            <Avatar
              text=""
              imgURL={member.image as string}
              icon={<UserIcon className="text-icon-neutral-inverse h-full w-full" />}
              size="3xl"
            />
            <span className="w-full shrink-0 text-xs leading-none font-normal text-neutral-bold truncate">
              {member.name}
            </span>
            <div className="absolute -top-1 -right-1 rounded-full bg-neutral-white">
              <XMarkIcon className="h-7 w-7 p-1" />
            </div>
          </button>
        ))}
      </ul>
    );
  }
);
