import { userFactory } from '@shape-construction/api/factories/users';
import { channelContextFactory } from 'app/channels/get-stream/factories/chat-context';
import type { ChannelState } from 'stream-chat';
import { renderHook } from 'tests/test-utils';
import { useCustomChannelTitle } from './useCustomChannelTitle';

describe('useCustomChannelTitle', () => {
  describe('when channel type is "personal"', () => {
    it('returns channel name', () => {
      const user = userFactory({ name: 'User 1' });
      const channel = channelContextFactory({
        type: 'personal',
        data: { name: 'Test Channel' },
      });

      const { result } = renderHook(() => useCustomChannelTitle(channel), { user });

      expect(result.current).toBe('User 1 currentUserLabel');
    });
  });

  describe('when channel type is "messaging"', () => {
    it('returns channel name', () => {
      const channel = channelContextFactory({
        type: 'messaging',
        data: { name: 'Test Channel' },
      });

      const { result } = renderHook(() => useCustomChannelTitle(channel));

      expect(result.current).toBe('Test Channel');
    });
  });

  describe('when channel type is "group"', () => {
    describe('when channel name is provided', () => {
      it('returns channel name', () => {
        const channel = channelContextFactory({ type: 'group', data: { name: 'Test Channel' } });

        const { result } = renderHook(() => useCustomChannelTitle(channel));

        expect(result.current).toBe('Test Channel');
      });
    });

    describe('when there is only one member', () => {
      it('returns the name of the other member', () => {
        const channel = channelContextFactory({
          type: 'group',
          data: { name: 'Test Channel' },
          context: {
            state: {
              members: {
                user2: { user_id: 'user2', user: { name: 'User 2' } },
              },
            } as unknown as ChannelState,
          },
        });

        const { result } = renderHook(() => useCustomChannelTitle(channel));

        expect(result.current).toBe('User 2');
      });
    });

    describe('when there are multiple members with names', () => {
      it('returns the names of other members', () => {
        const channel = channelContextFactory({
          type: 'group',
          data: { name: 'Test Channel' },
          context: {
            state: {
              members: {
                user2: { user_id: 'user2', user: { name: 'User 2' } },
                user3: { user_id: 'user3', user: { name: 'User 3' } },
              },
            } as unknown as ChannelState,
          },
        });
        const { result } = renderHook(() => useCustomChannelTitle(channel));

        expect(result.current).toBe('User 2, User 3');
      });

      it('returns names and count of excess members over 4', () => {
        const channel = channelContextFactory({
          type: 'group',
          data: { name: 'Test Channel' },
          context: {
            state: {
              members: {
                user2: { user_id: 'user2', user: { name: 'User 2' } },
                user3: { user_id: 'user3', user: { name: 'User 3' } },
                user4: { user_id: 'user4', user: { name: 'User 4' } },
                user5: { user_id: 'user5', user: { name: 'User 5' } },
                user6: { user_id: 'user6', user: { name: 'User 6' } },
                user7: { user_id: 'user7', user: { name: 'User 7' } },
              },
            } as unknown as ChannelState,
          },
        });

        const { result } = renderHook(() => useCustomChannelTitle(channel));

        expect(result.current).toBe('User 2, User 3, User 4, User 5, +2');
      });
    });
  });

  describe('when channel type is "team"', () => {
    it('returns channel name', () => {
      const channel = channelContextFactory({
        type: 'team',
        data: { name: 'Test Channel' },
      });

      const { result } = renderHook(() => useCustomChannelTitle(channel));

      expect(result.current).toBe('Test Channel');
    });
  });
});
