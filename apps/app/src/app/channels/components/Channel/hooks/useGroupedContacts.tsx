import { useInfiniteQuery } from '@tanstack/react-query';
import { userInfinityQueryOptions } from 'app/channels/queries/users';
import { useCurrentUser } from 'app/queries/users/users';
import groupBy from 'lodash.groupby';
import { useMemo } from 'react';
import type { UserFilters, UserOptions, UserSort } from 'stream-chat';
import { useChatContext } from 'stream-chat-react';

export const useGroupedContacts = (
  filters: UserFilters,
  search: string,
  sort: UserSort = {},
  options: UserOptions = {}
) => {
  const { client } = useChatContext();
  const user = useCurrentUser();
  const myStreamUserId = user.channels.streamChatUserId;
  const {
    data: flattenedUsers,
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    enabled: Boolean(myStreamUserId),
    ...userInfinityQueryOptions(
      client,
      {
        banned: false,
        ...(search && { name: { $autocomplete: search } }),
        ...filters,
      },
      { name: 1, ...sort },
      { limit: 30, ...options }
    ),
    select: (data) => {
      return data.pages.flatMap((page) => page?.users || []).filter((user) => user.id !== myStreamUserId);
    },
  });

  const groupedContacts = useMemo(() => {
    return groupBy(flattenedUsers, ({ name }) => name?.at(0)?.toUpperCase());
  }, [flattenedUsers]);

  return {
    data: groupedContacts,
    flattenData: flattenedUsers || [],
    isLoading,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  };
};
