import type { Channel } from 'stream-chat';

import { useMessageGetter } from '@messageformat/react';
import type { ChannelType } from '@shape-construction/api/channels/types/channel';
import { useUsersMe } from 'app/queries/users/users';
import { useMemo } from 'react';
import { useChannelPreviewInfo } from 'stream-chat-react';

const getGroupChannelName = (channel: Channel, clientUserId?: string) => {
  const otherMembers = Object.values(channel.state.members).filter((member) => member.user_id !== clientUserId);
  if (otherMembers.length === 1 && otherMembers[0].user) {
    return otherMembers[0].user.name || otherMembers[0].user.id;
  }

  const memberNames = otherMembers.map((member) => member.user?.name || member.user?.id).filter(Boolean);
  if (memberNames.length > 4) {
    const displayedNames = memberNames.slice(0, 4);
    const excessCount = memberNames.length - 4;
    return `${displayedNames.join(', ')}, +${excessCount}`;
  }

  return memberNames.join(', ');
};

export const useCustomChannelTitle = (channel: Channel) => {
  const getMessage = useMessageGetter('');
  const { data: user } = useUsersMe();
  const channelType = channel.type as ChannelType;

  const { displayTitle: defaultChannelTitle } = useChannelPreviewInfo({ channel });
  const customChannelTitle = useMemo(() => {
    const message = {
      personal: `${user?.name} ${getMessage('currentUserLabel')}`,
      group: getGroupChannelName(channel),
    } as Record<ChannelType, string>;

    return message[channelType] || defaultChannelTitle;
  }, [user?.name, channel, defaultChannelTitle, channelType, getMessage]);

  return customChannelTitle;
};
