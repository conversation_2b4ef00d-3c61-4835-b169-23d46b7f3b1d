import { useCallback, useMemo } from 'react';

import { type ReactionsListProps, useComponentContext, useMessageContext } from 'stream-chat-react';

import type { ReactionSummary, ReactionsComparator } from '../types';

type SharedReactionListProps =
  | 'own_reactions'
  | 'reaction_counts'
  | 'reaction_groups'
  | 'reactionOptions'
  | 'reactions';

type UseProcessReactionsParams = Pick<ReactionsListProps, SharedReactionListProps> & {
  sortReactions?: ReactionsComparator;
};

export const defaultReactionsSort: ReactionsComparator = (a, b) => {
  if (a.firstReactionAt && b.firstReactionAt) {
    return +a.firstReactionAt - +b.firstReactionAt;
  }

  return a.reactionType.localeCompare(b.reactionType, 'en');
};

export const useProcessReactions = (params: UseProcessReactionsParams) => {
  const {
    own_reactions: propOwnReactions,
    reaction_groups: propReactionGroups,
    reactionOptions: propReactionOptions,
    reactions: propReactions,
    sortReactions: propSortReactions,
  } = params;
  const { message, sortReactions: contextSortReactions } = useMessageContext('useProcessReactions');
  const { reactionOptions: contextReactionOptions } = useComponentContext('useProcessReactions');
  const reactionOptions = propReactionOptions ?? contextReactionOptions;
  const sortReactions = propSortReactions ?? contextSortReactions ?? defaultReactionsSort;
  const latestReactions = propReactions || message.latest_reactions;
  const ownReactions = propOwnReactions || message?.own_reactions;
  const reactionGroups = propReactionGroups || message?.reaction_groups;

  const isOwnReaction = useCallback(
    (reactionType: string) => ownReactions?.some((reaction) => reaction.type === reactionType) ?? false,
    [ownReactions]
  );

  const getEmojiByReactionType = useCallback(
    (reactionType: string) => reactionOptions?.find(({ type }) => type === reactionType)?.Component ?? null,
    [reactionOptions]
  );

  const isSupportedReaction = useCallback(
    (reactionType: string) => reactionOptions?.some((reactionOption) => reactionOption.type === reactionType),
    [reactionOptions]
  );

  const getLatestReactedUserNames = useCallback(
    (reactionType?: string) =>
      latestReactions?.flatMap((reaction) => {
        if (reactionType && reactionType === reaction.type) {
          const username = reaction.user?.name || reaction.user?.id;
          return username ? [username] : [];
        }
        return [];
      }) ?? [],
    [latestReactions]
  );

  const existingReactions: ReactionSummary[] = useMemo(() => {
    if (!reactionGroups) {
      return [];
    }

    const unsortedReactions = Object.entries(reactionGroups).flatMap(
      ([reactionType, { count, first_reaction_at: firstReactionAt, last_reaction_at: lastReactionAt }]) => {
        if (count === 0 || !isSupportedReaction(reactionType)) {
          return [];
        }

        const latestReactedUserNames = getLatestReactedUserNames(reactionType);

        return [
          {
            EmojiComponent: getEmojiByReactionType(reactionType),
            firstReactionAt: firstReactionAt ? new Date(firstReactionAt) : null,
            isOwnReaction: isOwnReaction(reactionType),
            lastReactionAt: lastReactionAt ? new Date(lastReactionAt) : null,
            latestReactedUserNames,
            reactionCount: count,
            reactionType,
            unlistedReactedUserCount: count - latestReactedUserNames.length,
          },
        ];
      }
    );

    return unsortedReactions.sort(sortReactions);
  }, [
    getEmojiByReactionType,
    getLatestReactedUserNames,
    isOwnReaction,
    isSupportedReaction,
    reactionGroups,
    sortReactions,
  ]);

  const hasReactions = existingReactions.length > 0;

  const totalReactionCount = useMemo(
    () => existingReactions.reduce((total, { reactionCount }) => total + reactionCount, 0),
    [existingReactions]
  );

  return {
    existingReactions,
    hasReactions,
    totalReactionCount,
  };
};
