import React from 'react';

import { render, screen } from 'tests/test-utils';
import { ChannelHeader } from './ChannelHeader';

const mockUseChannelStateContext = jest.fn();
const mockUseCustomChannelTitle = jest.fn();
const mockUseTypingUsers = jest.fn();

jest.mock('./useTypingUsers', () => ({
  useTypingUsers: () => mockUseTypingUsers(),
}));

jest.mock('../Channel/hooks/useCustomChannelTitle', () => ({
  useCustomChannelTitle: () => mockUseCustomChannelTitle(),
}));

jest.mock('stream-chat-react', () => ({
  ...jest.requireActual('stream-chat-react'),
  useChannelStateContext: () => mockUseChannelStateContext(),
}));

describe('ChannelHeader', () => {
  it('renders the channel name and subtitle', () => {
    mockUseChannelStateContext.mockReturnValue({
      channel: {
        state: {
          members: new Map([
            ['user-1', {}],
            ['user-2', {}],
          ]),
        },
      },
    });
    mockUseCustomChannelTitle.mockReturnValue('Channel 1');
    mockUseTypingUsers.mockReturnValue([]);

    render(<ChannelHeader />);

    expect(screen.getByText('Channel 1')).toBeInTheDocument();
    expect(screen.getByText('channels.channel.header.subtitle')).toBeInTheDocument();
  });

  it('displays typing message when users are typing', () => {
    mockUseChannelStateContext.mockReturnValue({ channel: { state: { members: new Map() } } });
    mockUseCustomChannelTitle.mockReturnValue('Channel 1');
    mockUseTypingUsers.mockReturnValue(['user1', 'user2']);

    render(<ChannelHeader />);

    expect(screen.getByText('channels.channel.header.events.typing')).toBeInTheDocument();
  });
});
