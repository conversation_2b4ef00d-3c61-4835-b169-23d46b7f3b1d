import { useMessage } from '@messageformat/react';
import React from 'react';
import { useChannelStateContext } from 'stream-chat-react';
import { useCustomChannelTitle } from '../Channel/hooks/useCustomChannelTitle';
import { PreviewAvatar } from '../ChannelsPreview/PreviewAvatar';
import { useTypingUsers } from './useTypingUsers';

export const ChannelHeader: React.FC = () => {
  const { channel } = useChannelStateContext();
  const displayTitle = useCustomChannelTitle(channel);
  const subtitle = useMessage('channels.channel.header.subtitle');

  const typingUsers = useTypingUsers();

  const users = typingUsers && typingUsers.length > 3 ? typingUsers.slice(0, 3) : typingUsers;

  const message = useMessage('channels.channel.header.events.typing', {
    count: typingUsers?.length,
    users,
  });

  const typingMessage = users && users?.length > 0 ? message : null;

  return (
    <div className="flex flex-row items-center p-2 gap-2">
      <PreviewAvatar channel={channel} />
      <div className="flex flex-col grow">
        <div className="text-base leading-6 font-bold text-neutral-bold line-clamp-1">{displayTitle}</div>
        <span className="text-xs leading-none font-normal text-neutral-subtle">{typingMessage ?? subtitle}</span>
      </div>
    </div>
  );
};
