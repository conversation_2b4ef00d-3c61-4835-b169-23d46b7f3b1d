import React from 'react';

import { useMessageGetter } from '@messageformat/react';
import { cn } from '@shape-construction/arch-ui';
import { BellSlashIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { Link, useParams } from 'react-router';
import type { ChannelPreviewUIComponentProps } from 'stream-chat-react';

import { useCustomChannelTitle } from '../Channel/hooks/useCustomChannelTitle';
import { PreviewAvatar } from './PreviewAvatar';
import { PreviewTimeStamp } from './PreviewTimestamp';
import { UnreadCount } from './UnreadCount';

export type Params = {
  projectId?: string;
  channelId?: string;
};

export const Preview: React.FC<ChannelPreviewUIComponentProps> = ({
  channel,
  latestMessagePreview,
  setActiveChannel,
  unread,
}) => {
  const { projectId, channelId } = useParams() as Params;
  const { muted } = channel.muteStatus() || { muted: false };
  const messages = useMessageGetter('channels.channelsList');

  const displayTitle = useCustomChannelTitle(channel);
  const isChannelActive = channelId === channel.cid;

  return (
    <div className="mx-2">
      <Link
        key={channel.cid}
        to={`/projects/${projectId}/channels/${channel.cid}`}
        onClick={() => {
          if (setActiveChannel) setActiveChannel(channel);
        }}
        aria-selected={isChannelActive}
        className={cn(
          'w-full flex flex-row items-center py-3 px-4 gap-2 rounded-lg',
          'hover:bg-neutral-subtle-hovered active:bg-neutral-subtle-pressed aria-selected:bg-neutral-subtle'
        )}
      >
        <PreviewAvatar channel={channel} />
        <div className="flex-1 flex flex-col items-start">
          <div className="w-full flex flex-row">
            <div className="flex-1 text-sm leading-5 font-semibold text-neutral-bold line-clamp-1">{displayTitle}</div>
            <div className="flex flex-row items-center shrink-0 ml-1 gap-1">
              {channel?.type === 'team' && (
                <div className="flex flex-row shrink-0 rounded-xl space-x-1.5 px-1.5 min-w-5 min-h-5 items-center justify-center bg-accent-indigo-subtle">
                  <div className="text-accent-indigo text-xs leading-4 font-medium">{messages('autoTag')}</div>
                </div>
              )}
              <PreviewTimeStamp date={channel?.state?.last_message_at} />
            </div>
          </div>
          <div className="w-full flex-1 flex flex-row items-center">
            <div className="text-xs leading-4 font-normal text-neutral-subtle flex-1 line-clamp-2 [&_p]:break-all!">
              {latestMessagePreview}
            </div>
            <div className="flex flex-row items-center shrink-0 ml-1 gap-1">
              <UnreadCount unread={unread} />
              {muted && <BellSlashIcon className="text-icon-neutral" width={16} height={16} />}
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
};
