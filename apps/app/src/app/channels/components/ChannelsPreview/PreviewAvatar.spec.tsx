import type { ChannelState } from 'stream-chat';

import { userFactory as streamUserFactory } from '@shape-construction/api/channels/factories/user';
import { userFactory as shapeUserFactory } from '@shape-construction/api/factories/users';

import { channelContextFactory, chatContextValueFactory } from 'app/channels/get-stream/factories/chat-context';
import { render, screen } from 'tests/test-utils';

import { GROUP_CHANNEL_TYPES } from '../Channel/constants/channelTypes';
import { PreviewAvatar } from './PreviewAvatar';

describe('PreviewAvatar', () => {
  beforeAll(() => {
    jest.clearAllMocks();
  });

  describe('when channel type is personal', () => {
    it('renders personal avatar', () => {
      const user = shapeUserFactory({ id: 'user-me', avatarUrl: 'https://example.com/avatar.png' });
      const channel = channelContextFactory({ type: 'personal', context: { data: { member_count: 1 } } });
      const chatContextValue = chatContextValueFactory({ channel });

      render(<PreviewAvatar channel={channel} />, { chatContextValue, user });

      expect(screen.getByAltText('channels.previewAvatar.personal')).toBeInTheDocument();
    });
  });

  describe('when channel type is messaging', () => {
    it('renders user avatar', () => {
      const channel = channelContextFactory({
        type: 'messaging',
        context: {
          data: { member_count: 2 },
          state: {
            members: { member1: { user: streamUserFactory({ image: 'https://example.com/avatar.png' }) } },
          } as unknown as ChannelState,
        },
      });
      const chatContextValue = chatContextValueFactory({ channel });

      render(<PreviewAvatar channel={channel} />, { chatContextValue });

      expect(screen.getByAltText('channels.previewAvatar.messaging')).toBeInTheDocument();
    });
  });

  describe.each(GROUP_CHANNEL_TYPES)('when channel type is %s', (type) => {
    it('renders channel avatar', () => {
      const channel = channelContextFactory({ type, context: { data: { image: 'https://example.com/avatar.png' } } });
      const chatContextValue = chatContextValueFactory({ channel });

      render(<PreviewAvatar channel={channel} />, { chatContextValue });

      expect(screen.getByAltText('channels.previewAvatar.channel')).toBeInTheDocument();
    });
  });
});
