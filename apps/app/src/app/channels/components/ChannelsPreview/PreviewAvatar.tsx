import React, { memo } from 'react';
import type { Channel } from 'stream-chat';

import type { ChannelType } from '@shape-construction/api/channels/types/channel';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import { UserIcon, UsersIcon } from '@shape-construction/arch-ui/src/Icons/solid';

import { useMessageGetter } from '@messageformat/react';
import { useCurrentUser } from 'app/queries/users/users';

export type PreviewAvatarProps = Partial<React.ComponentProps<typeof Avatar>> & {
  channel: Channel;
};

export const PreviewAvatar: React.FC<PreviewAvatarProps> = memo(({ channel, ...props }) => {
  const user = useCurrentUser();
  const messageGetter = useMessageGetter('channels.previewAvatar');
  const channelType = channel.type as ChannelType;

  if (channelType === 'personal') {
    return (
      <Avatar
        imgURL={user.avatarUrl}
        size="lg"
        icon={<UserIcon className="text-icon-neutral-inverse" />}
        {...props}
        text={messageGetter('personal')}
      />
    );
  }

  if (channelType === 'messaging') {
    const members = Object.values(channel.state.members);
    const otherMember = members.find((member) => member.user?.id !== user.channels?.streamChatUserId);
    const memberImage = otherMember?.user?.image as string | undefined;

    return (
      <Avatar
        imgURL={memberImage}
        icon={<UserIcon className="text-icon-neutral-inverse" />}
        size="lg"
        {...props}
        text={messageGetter('messaging')}
      />
    );
  }

  return (
    <Avatar
      imgURL={channel.data?.image as string | undefined}
      icon={<UsersIcon className="text-icon-neutral-inverse" />}
      size="lg"
      {...props}
      text={messageGetter('channel')}
    />
  );
});
