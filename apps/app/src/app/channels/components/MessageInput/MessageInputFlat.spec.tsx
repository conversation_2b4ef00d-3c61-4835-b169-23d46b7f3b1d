import { channelFactory } from '@shape-construction/api/channels/factories/channel';
import { channelContextFactory, chatContextValueFactory } from 'app/channels/get-stream/factories/chat-context';
import { createMemoryHistory } from 'history';
import type { ComponentProps } from 'react';
import { useDropzone } from 'react-dropzone';
import type { ChannelState } from 'stream-chat';
import {
  ChannelStateContext,
  type ChannelStateContextValue,
  ComponentContext,
  type ComponentContextValue,
  MessageInputContext,
  type MessageInputContextValue,
  useAttachmentManagerState,
  useChannelActionContext,
  useChannelStateContext,
  useChatContext,
  useComponentContext,
  useMessageComposer,
  useMessageComposerHasSendableData,
  useMessageInputContext,
  useTranslationContext,
} from 'stream-chat-react';
import { render, screen, userEvent } from 'tests/test-utils';
import { MessageInputFlat } from './MessageInputFlat';

jest.mock('stream-chat-react', () => ({
  ...jest.requireActual('stream-chat-react'),
  useAttachmentManagerState: jest.fn(),
  useChannelActionContext: jest.fn(),
  useChannelStateContext: jest.fn(),
  useChatContext: jest.fn(),
  useComponentContext: jest.fn(),
  useMessageComposer: jest.fn(),
  useMessageComposerHasSendableData: jest.fn(),
  useMessageGetter: jest.fn().mockReturnValue(() => 'Test message'),
  useMessageInputContext: jest.fn(),
  useTranslationContext: jest.fn(),
  QuotedMessagePreviewHeader: () => <div>QuotedMessagePreviewHeader</div>,
  WithDragAndDropUpload: (props: ComponentProps<'div'>) => <div {...props} />,
}));

jest.mock('react-dropzone', () => ({
  useDropzone: jest.fn(() => ({
    getRootProps: jest.fn(),
    getInputProps: jest.fn(),
    isDragActive: false,
    isDragReject: false,
    acceptedFiles: [],
  })),
}));

describe('<MessageInputFlat />', () => {
  const mockMessageInputContext = {
    asyncMessagesMultiSendEnabled: false,
    cooldownRemaining: 0,
    handleSubmit: jest.fn(),
    hideSendButton: false,
    recordingController: {
      recordingState: false,
      recorder: { start: jest.fn() },
      permissionState: 'granted',
    },
    setCooldownRemaining: jest.fn(),
  };

  const mockComponentContext = {
    AudioRecorder: () => <div>AudioRecorder</div>,
    AttachmentPreviewList: () => <div>AttachmentPreviewList</div>,
    CooldownTimer: () => <div>CooldownTimer</div>,
    LinkPreviewList: () => <div>LinkPreviewList</div>,
    RecordingPermissionDeniedNotification: () => <div>RecordingPermissionDeniedNotification</div>,
    StartRecordingAudioButton: () => <div>StartRecordingAudioButton</div>,
    EmojiPicker: () => <div>EmojiPicker</div>,
    TextareaComposer: (props: ComponentProps<'input'>) => <input {...props} />,
    QuotedMessagePreview: () => <div>QuotedMessagePreview</div>,
  };

  const mockChannelStateContext = {
    acceptedFiles: [],
    channel: channelFactory({
      type: 'team',
      id: 'channel-1',
      cid: 'channels.details.channelType.team:channel-1',
      name: 'Project X',
    }),
    multipleUploads: true,
    quotedMessage: {
      id: 'messageId',
      text: 'Some text',
      html: '<p>Some text</p>',
      type: 'regular',
      user: {
        id: 'userId',
        name: 'User Name',
        language: 'en',
        role: 'user',
        teams: [],
        blocked_user_ids: [],
      },
      status: 'received',
    },
    suppressAutoscroll: false,
  };

  const mockAttachmentManagerState = {
    attachments: [],
    isUploadEnabled: true,
    successfulUploadsCount: 1,
    failedUploadsCount: 0,
    availableUploadSlots: 1,
  };

  const mockUseMessageComposer = {
    setQuotedMessage: jest.fn(),
    textComposer: {
      text: 'default ',
    },
    state: {
      subscribe: jest.fn(),
      subscribeWithSelector: jest.fn(),
      getLatestValue: () => ({
        quotedMessage: 'quote message',
      }),
    },
    attachmentManager: {
      acceptedFiles: [],
      uploadFiles: jest.fn(),
      maxNumberOfFilesPerMessage: 5,
    },
  };

  const mockChatContext = {
    channel: {
      on: jest.fn(),
      off: jest.fn(),
    },
  };

  beforeEach(() => {
    jest.resetAllMocks();

    (useChatContext as jest.Mock).mockReturnValue(mockChatContext);
    (useChannelStateContext as jest.Mock).mockReturnValue(mockChannelStateContext);
    (useMessageComposer as jest.Mock).mockReturnValue(mockUseMessageComposer);
    (useMessageInputContext as jest.Mock).mockReturnValue(mockMessageInputContext);
    (useComponentContext as jest.Mock).mockReturnValue(mockComponentContext);
    (useTranslationContext as jest.Mock).mockReturnValue({ t: (key: string) => key });
    (useAttachmentManagerState as jest.Mock).mockReturnValue(mockAttachmentManagerState);
    (useChannelActionContext as jest.Mock).mockReturnValue({ jumpToMessage: jest.fn() });
  });

  const setup = (overrides = {}) => {
    (useDropzone as jest.Mock).mockReturnValue({
      getRootProps: jest.fn(),
      isDragActive: false,
      isDragReject: false,
    });
    const channel = channelFactory({
      type: 'team',
      id: 'channel-1',
      cid: 'channels.details.channelType.team:channel-1',
      name: 'Project X',
    });
    const route = { path: '/projects/:projectId/channels/:channelId' };
    const history = createMemoryHistory({
      initialEntries: ['/projects/project-0/channels/channel-0'],
    });
    const utils = render(
      <ChannelStateContext.Provider value={mockChannelStateContext as unknown as ChannelStateContextValue}>
        <MessageInputContext.Provider value={mockMessageInputContext as unknown as MessageInputContextValue}>
          <ComponentContext.Provider value={mockComponentContext as unknown as ComponentContextValue}>
            <MessageInputFlat {...overrides} />
          </ComponentContext.Provider>
        </MessageInputContext.Provider>
      </ChannelStateContext.Provider>,
      {
        route,
        history,
        chatContextValue: chatContextValueFactory({
          channel: channelContextFactory({
            type: channel.type,
            id: channel.id,
            data: channel,
            context: { state: { members: {} } as ChannelState },
          }),
        }),
      }
    );

    return { ...utils };
  };

  it('renders correctly', () => {
    setup();

    expect(screen.getByLabelText('aria/File upload')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('channels.channel.messageInput.placeholder')).toBeInTheDocument();
    expect(screen.getByLabelText('send-message')).toBeInTheDocument();
  });

  describe('when there is no sendable data', () => {
    it('disables send button when there is no sendable data', () => {
      (useMessageComposerHasSendableData as jest.Mock).mockReturnValue(false);

      setup();

      expect(screen.getByLabelText('send-message')).toBeDisabled();
    });
  });

  describe('when there is sendable data', () => {
    it('enables send button when there is sendable data', () => {
      (useMessageComposerHasSendableData as jest.Mock).mockReturnValue(true);

      setup();

      expect(screen.getByLabelText('send-message')).toBeEnabled();
    });

    it('sends a message', async () => {
      (useMessageComposerHasSendableData as jest.Mock).mockReturnValue(true);
      setup();

      await userEvent.click(screen.getByLabelText('send-message'));

      expect(mockMessageInputContext.handleSubmit).toHaveBeenCalled();
    });
  });

  it('displays quoted message', () => {
    setup();

    expect(screen.getByText('QuotedMessagePreview')).toBeInTheDocument();
  });

  it('shows drag reject UI when dragged files are not accepted', () => {
    (useDropzone as jest.Mock).mockReturnValue({
      getRootProps: jest.fn(),
      isDragActive: true,
      isDragReject: true,
    });

    render(<MessageInputFlat />);

    expect(screen.getByText('Some of the files will not be accepted')).toBeInTheDocument();
  });
});
