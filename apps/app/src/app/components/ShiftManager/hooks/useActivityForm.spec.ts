import { activityFactory } from '@shape-construction/api/factories/activities';
import { shiftReportsAvailableActions, shiftReportsFactory } from '@shape-construction/api/factories/shiftReports';
import {
  getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler,
  postApiProjectsProjectIdShiftReportsMockHandler,
} from '@shape-construction/api/handlers-factories/projects/shift-reports';
import type { PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema } from '@shape-construction/api/src';
import { server } from 'tests/mock-server';
import { renderHook, waitFor } from 'tests/test-utils';
import { getInitialActivityValues, makeFormValuesFromActivityData, useActivityForm } from './useActivityForm';

const initialValues: PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema = {
  actual_end_date: undefined,
  actual_start_date: undefined,
  critical: false,
  description: '',
  location_id: undefined,
  organisation_resource_id: undefined,
  owner_id: undefined,
  planned_end_date: undefined,
  planned_start_date: undefined,
  expected_finish_date: undefined,
  status: 'not_started',
};

describe('useActivityForm', () => {
  describe('makeFormValuesFromActivityData', () => {
    it('produces the form payload from shift activity data', () => {
      const shiftActivity = activityFactory({
        actualEndDate: '2025-07-18T15:30',
        actualStartDate: '2025-07-17T14:30',
        critical: true,
        description: 'Repair faulty electrical outlets on the first floor',
        plannedEndDate: '2025-07-21T16:00',
        plannedStartDate: '2025-07-16T06:30',
        expectedFinishDate: '2025-02-22T13:00',
        status: 'completed',
      });
      const expected: PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema = {
        actual_end_date: '2025-07-18T15:30',
        actual_start_date: '2025-07-17T14:30',
        critical: true,
        description: 'Repair faulty electrical outlets on the first floor',
        planned_end_date: '2025-07-21T16:00',
        planned_start_date: '2025-07-16T06:30',
        expected_finish_date: '2025-02-22T13:00',
        status: 'completed',
      };
      const actual = makeFormValuesFromActivityData(shiftActivity);

      expect(actual).toMatchObject(expected);
    });
  });

  describe('getInitialActivityValues', () => {
    it('produces the initial values', () => {
      const actual = getInitialActivityValues();

      expect(actual).toMatchObject(initialValues);
    });
  });

  describe('useActivityForm hook', () => {
    it('produces the initial values', async () => {
      const shiftReport = shiftReportsFactory({
        availableActions: shiftReportsAvailableActions({
          edit: true,
        }),
      });
      server.use(
        postApiProjectsProjectIdShiftReportsMockHandler(() => shiftReport),
        getApiProjectsProjectIdShiftReportsShiftReportIdMockHandler(() => shiftReport)
      );

      const { result } = renderHook(() => useActivityForm());

      await waitFor(() => expect(result.current.initialValues).toMatchObject(initialValues));
    });
  });
});
