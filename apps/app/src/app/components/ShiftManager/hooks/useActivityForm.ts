import { yupResolver } from '@hookform/resolvers/yup';
import {
  type PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema,
  type ProjectSchema,
  type ShiftActivitySchema,
  shiftActivityStatusEnum,
} from '@shape-construction/api/src/types';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { useShiftActivity } from 'app/queries/activities/activities';
import { useEffect, useMemo } from 'react';
import { type UseFormReturn, useForm } from 'react-hook-form';
import { useParams } from 'react-router';
import * as Yup from 'yup';

type Params = {
  projectId: ProjectSchema['id'];
  shiftActivityId: ShiftActivitySchema['id'];
};

type FormValues = {
  description: PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema['description'];
  status: PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema['status'];
  critical: PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema['critical'];
};

const activityFormSchema: Yup.SchemaOf<FormValues> = Yup.object().shape({
  description: Yup.string().required('Required field'),
  percentage_completed: Yup.number()
    .typeError('This field should be a valid number')
    .nullable()
    .min(0, 'This field should be greater than 0')
    .max(100, 'This field should be less than 100')
    .transform((value, originalValue) => (String(originalValue).trim() === '' ? null : value)),
  status: Yup.mixed().oneOf([undefined, ...Object.keys(shiftActivityStatusEnum)]),
  critical: Yup.boolean(),
});

export const getInitialActivityValues = (): PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema => {
  return {
    actual_end_date: undefined,
    actual_start_date: undefined,
    critical: false,
    description: '',
    location_id: undefined,
    organisation_resource_id: undefined,
    owner_id: undefined,
    planned_end_date: undefined,
    planned_start_date: undefined,
    expected_finish_date: undefined,
    status: shiftActivityStatusEnum.not_started,
  };
};

export const makeFormValuesFromActivityData = (
  shiftActivity: ShiftActivitySchema
): PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema => {
  const {
    actualEndDate,
    actualStartDate,
    critical,
    description,
    locationId,
    taskIdentifier,
    organisationResourceId,
    ownerId,
    plannedEndDate,
    plannedStartDate,
    expectedFinishDate,
    status,
  } = shiftActivity;

  return {
    actual_end_date: actualEndDate && parseDateWithFormat(actualEndDate, 'YYYY-MM-DDTHH:mm'),
    actual_start_date: actualStartDate && parseDateWithFormat(actualStartDate, 'YYYY-MM-DDTHH:mm'),
    critical,
    description,
    location_id: locationId,
    task_identifier: taskIdentifier,
    organisation_resource_id: organisationResourceId,
    owner_id: ownerId,
    planned_end_date: plannedEndDate && parseDateWithFormat(plannedEndDate, 'YYYY-MM-DDTHH:mm'),
    planned_start_date: plannedStartDate && parseDateWithFormat(plannedStartDate, 'YYYY-MM-DDTHH:mm'),
    expected_finish_date: expectedFinishDate && parseDateWithFormat(expectedFinishDate, 'YYYY-MM-DDTHH:mm'),
    status: status || shiftActivityStatusEnum.not_started,
  };
};

export const useActivityForm = (): {
  form: UseFormReturn<PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema>;
  initialValues: PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema;
  organisationName: ShiftActivitySchema['organisation'];
  isLoadingData: boolean;
} => {
  const { projectId, shiftActivityId } = useParams<Params>() as Params;
  const { data: shiftActivity, isLoading, isFetching } = useShiftActivity(projectId, shiftActivityId!);
  const isLoadingData = isLoading || isFetching;

  const initialValues = useMemo(() => {
    if (!shiftActivity || isLoadingData) return getInitialActivityValues();

    return makeFormValuesFromActivityData(shiftActivity);
  }, [shiftActivity, isLoadingData]);

  const form = useForm<PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema>({
    defaultValues: initialValues,
    shouldUnregister: true,
    resolver: yupResolver(activityFormSchema),
  });

  useEffect(() => {
    form.reset(initialValues);
  }, [initialValues, form]);

  return {
    form,
    initialValues,
    organisationName: shiftActivity?.organisation || null,
    isLoadingData,
  };
};
