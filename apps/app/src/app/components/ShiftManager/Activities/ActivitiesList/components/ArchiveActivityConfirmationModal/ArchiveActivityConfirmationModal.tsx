import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import { Button, ConfirmationModal, IconBadge } from '@shape-construction/arch-ui';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useShiftActivityArchive } from 'app/queries/activities/activities';
import React from 'react';
import { useParams } from 'react-router';

type Params = {
  projectId: string;
};

export interface ArchiveActivityConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  shiftActivityId: ShiftActivitySchema['id'];
}

export const ArchiveActivityConfirmationModal = ({
  isOpen,
  onClose,
  shiftActivityId,
}: ArchiveActivityConfirmationModalProps) => {
  const messages = useMessageGetter('activities.archiveModal');

  const { projectId } = useParams<Params>() as Params;
  const { mutate: archiveShiftActivity, isPending } = useShiftActivityArchive();

  const handleArchive = () => {
    archiveShiftActivity(
      {
        projectId,
        shiftActivityId,
      },
      {
        onSuccess: () => {
          showSuccessToast({ message: messages('success') });
        },
        onSettled: () => {
          onClose();
        },
      }
    );
  };

  return (
    <ConfirmationModal.Root open={isOpen} onClose={onClose} data-cy="archive-activity-confirmation-modal">
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="danger">
            <ExclamationTriangleIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>{messages('subtitle')}</ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button
          color="secondary"
          variant="outlined"
          size="md"
          aria-label={messages('actions.cancel')}
          onClick={onClose}
          disabled={isPending}
        >
          {messages('actions.cancel')}
        </Button>
        <Button
          color="danger"
          variant="contained"
          size="md"
          aria-label={messages('actions.archive')}
          onClick={handleArchive}
          disabled={isPending}
        >
          {messages('actions.archive')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
