import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import React from 'react';

export interface ActivityCardRefProps {
  referenceNumber: ShiftActivitySchema['referenceNumber'];
}

export const ActivityCardRef: React.FC<ActivityCardRefProps> = ({ referenceNumber }) => {
  const { value: isStreamliningProgressLogs } = useFeatureFlag('streamlining-progress-logs');
  const messages = useMessageGetter('shiftManager.activities.list');

  return (
    <span className="whitespace-nowrap text-xs leading-4 font-medium text-gray-500">
      {isStreamliningProgressLogs && messages('taskId')} {referenceNumber}
    </span>
  );
};
