import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';
import React from 'react';

export interface ActivityCardProgressProps {
  progress: ShiftActivitySchema['percentageCompleted'];
}

export const ActivityCardProgress: React.FC<ActivityCardProgressProps> = ({ progress }) => {
  const { value: isStreamliningProgressLogs } = useFeatureFlag('streamlining-progress-logs');
  const messages = useMessageGetter('shiftManager.activities.list');

  if (progress === undefined || progress === null) return null;

  return (
    <span className="truncate text-xs font-medium leading-4 text-gray-500">
      {isStreamliningProgressLogs
        ? messages('percentageCompletedStreamlining', {
            progress,
            today: parseDateWithFormat(new Date(), 'DD-MMM-YYYY'),
          })
        : messages('percentageCompleted', { progress })}
    </span>
  );
};
