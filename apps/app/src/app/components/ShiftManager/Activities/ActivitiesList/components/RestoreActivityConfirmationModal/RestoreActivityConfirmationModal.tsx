import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import { Button, ConfirmationModal, IconBadge } from '@shape-construction/arch-ui';
import { InboxOutIcon } from '@shape-construction/arch-ui/src/Icons/custom';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { useShiftActivityRestore } from 'app/queries/activities/activities';
import React from 'react';
import { useParams } from 'react-router';

type Params = {
  projectId: string;
};

export interface RestoreActivityConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  shiftActivityId: ShiftActivitySchema['id'];
}

export const RestoreActivityConfirmationModal = ({
  isOpen,
  onClose,
  shiftActivityId,
}: RestoreActivityConfirmationModalProps) => {
  const messages = useMessageGetter('activities.restoreModal');

  const { projectId } = useParams<Params>() as Params;
  const { mutate: restoreShiftActivity, isPending } = useShiftActivityRestore();

  const handleRestore = () => {
    restoreShiftActivity(
      { projectId, shiftActivityId },
      {
        onSuccess: () => {
          showSuccessToast({ message: messages('success') });
        },
        onSettled: () => {
          onClose();
        },
      }
    );
  };

  return (
    <ConfirmationModal.Root open={isOpen} onClose={onClose} data-cy="restore-activity-confirmation-modal">
      <ConfirmationModal.Header>
        <ConfirmationModal.Image>
          <IconBadge type="info">
            <InboxOutIcon />
          </IconBadge>
        </ConfirmationModal.Image>
        <ConfirmationModal.Title>{messages('title')}</ConfirmationModal.Title>
        <ConfirmationModal.SubTitle>{messages('subtitle')}</ConfirmationModal.SubTitle>
      </ConfirmationModal.Header>
      <ConfirmationModal.Footer>
        <Button
          color="secondary"
          variant="outlined"
          size="md"
          aria-label={messages('actions.cancel')}
          onClick={onClose}
          disabled={isPending}
        >
          {messages('actions.cancel')}
        </Button>
        <Button
          color="primary"
          variant="contained"
          size="md"
          aria-label={messages('actions.restore')}
          onClick={handleRestore}
          disabled={isPending}
        >
          {messages('actions.restore')}
        </Button>
      </ConfirmationModal.Footer>
    </ConfirmationModal.Root>
  );
};
