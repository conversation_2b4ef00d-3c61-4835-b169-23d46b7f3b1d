import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import { parseDateWithFormat } from '@shape-construction/utils/DateTime';

import React from 'react';

export interface ExpectedFinishProps {
  expectedFinish: ShiftActivitySchema['expectedFinishDate'];
}

export const ExpectedFinish: React.FC<ExpectedFinishProps> = ({ expectedFinish }) => {
  const messages = useMessageGetter('shiftManager.activities.list');

  if (!expectedFinish) return null;

  return (
    <span className="truncate text-xs font-medium leading-4 text-gray-500">
      {messages('expectedFinish', { expectedFinish: parseDateWithFormat(expectedFinish, 'DD-MMM-YYYY') })}
    </span>
  );
};
