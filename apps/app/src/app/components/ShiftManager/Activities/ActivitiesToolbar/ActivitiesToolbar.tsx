import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { SearchField } from '@shape-construction/arch-ui';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { useQuery } from '@tanstack/react-query';
import { SingleSelect } from 'app/components/Filters/SingleSelect/SingleSelect';
import { addQueryParams } from 'app/lib/utils/query-string';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { ActivitiesImportPanel } from 'app/screens/Project/ProjectShiftManager/Activities/ImportActivities/ActivitiesImportPanel';
import React, { useEffect, useRef } from 'react';
import { useLocation, useSearchParams } from 'react-router';
import { ExportActivitiesButton } from './components/ExportActivitiesButton';

export interface ActivitiesToolbarProps {
  projectId: ProjectSchema['id'];
}

export const ActivitiesToolbar: React.FC<ActivitiesToolbarProps> = ({ projectId }) => {
  const messages = useMessageGetter('activities');
  const ref = useRef(null);
  const location = useLocation();
  const [searchParams, setSearchParams] = useSearchParams();
  const isLargeScreen = useMediaQuery(breakpoints.up('lg'));
  const { data: project } = useQuery(getProjectQueryOptions(projectId));
  const canCreateShiftActivity = Boolean(project?.availableActions?.createShiftActivity);

  useEffect(() => {
    if (ref.current) {
      (ref.current as HTMLInputElement).value = '';
    }
  }, [location.pathname]);

  const updateSearchParams = (params: { search?: string; ready?: string }) =>
    setSearchParams((state) => new URLSearchParams(addQueryParams(state.toString(), params)));

  const selectActivitiesPanel = (
    <SingleSelect
      triggerLabel={messages('filters.readiness.label')}
      filterByLabel={messages('filters.readiness.title')}
      onChange={(value) => updateSearchParams({ ready: value })}
      value={searchParams.get('ready') || ''}
      selectOptions={[
        { name: messages('filters.readiness.options.all'), value: '' },
        { name: messages('filters.readiness.options.ready'), value: 'true' },
        { name: messages('filters.readiness.options.notReady'), value: 'false' },
      ]}
    />
  );
  const searchFieldPanel = (
    <SearchField
      defaultValue={searchParams.get('search') || ''}
      onChange={(event) => updateSearchParams({ search: event.target.value })}
      placeholder={messages('searchPlaceholder')}
      className="border border-gray-300"
      value={undefined}
      ref={ref}
    />
  );
  const exportActivitiesButton = <ExportActivitiesButton />;
  const importActivitiesPanel = canCreateShiftActivity ? <ActivitiesImportPanel /> : null;

  return (
    <div className="border-y border-neutral-subtlest bg-surface-navigation px-4 md:px-8 py-1">
      {isLargeScreen ? (
        <div className="grid gap-2 grid-cols-[auto_1fr_320px_auto]">
          <div>{selectActivitiesPanel}</div>
          <div />
          <div>{searchFieldPanel}</div>
          <div className="flex gap-2">
            {exportActivitiesButton}
            {importActivitiesPanel}
          </div>
        </div>
      ) : (
        <div className="grid gap-2 grid-cols-[auto_1fr_auto_auto] grid-rows-2">
          <div className="col-start-1">{selectActivitiesPanel}</div>
          <div className="col-start-3">{exportActivitiesButton}</div>
          {canCreateShiftActivity && <div className="col-start-4">{importActivitiesPanel}</div>}
          <div className="col-span-4 row-start-2">{searchFieldPanel}</div>
        </div>
      )}
    </div>
  );
};
