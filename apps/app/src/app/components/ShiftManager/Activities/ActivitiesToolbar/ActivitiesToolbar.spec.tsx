import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, waitFor } from 'tests/test-utils';
import { ActivitiesToolbar } from './ActivitiesToolbar';

describe('ActivitiesToolbar', () => {
  it('renders toolbar with export button', async () => {
    render(<ActivitiesToolbar projectId="project-0" />);

    expect(
      await screen.findByRole('button', {
        name: 'activities.export.exportCTA',
      })
    ).toBeInTheDocument();
  });

  describe('when the user has permission to create activity', () => {
    it('renders toolbar with import button', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createShiftActivity: true,
        }),
      });
      server.use(getApiProjectsProjectIdMockHandler(() => project));

      render(<ActivitiesToolbar projectId="project-0" />);

      expect(
        await screen.findByRole('button', {
          name: 'activities.import.trigger.title',
        })
      ).toBeInTheDocument();
    });

    describe('when the user does not have permission to create activity', () => {
      it('does not render toolbar with import button', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createShiftActivity: false,
          }),
        });
        server.use(getApiProjectsProjectIdMockHandler(() => project));

        render(<ActivitiesToolbar projectId="project-0" />);

        expect(
          screen.queryByRole('button', {
            name: 'activities.import.trigger.title',
          })
        ).not.toBeInTheDocument();
      });
    });

    describe('readiness filter', () => {
      describe('when changing the readiness filter', () => {
        it('updates the url with the filter', async () => {
          const history = createMemoryHistory({
            initialEntries: ['/projects/project-0/shift-reports/activities'],
          });
          const route = { path: '/projects/:projectId/shift-reports/:tab' };
          const { user } = render(<ActivitiesToolbar projectId="project-0" />, {
            history,
            route,
          });

          await user.click(await screen.findByRole('button', { name: /activities.filters.readiness.label/i }));
          await user.selectOptions(await screen.findByRole('listbox'), [
            await screen.findByRole('option', { name: 'activities.filters.readiness.options.ready' }),
          ]);

          await waitFor(() => expect(history.location.search).toEqual('?ready=true'));
        });
      });

      describe('when the url has a readiness filter', () => {
        it('renders the filter as selected', async () => {
          const history = createMemoryHistory({
            initialEntries: ['/projects/project-0/shift-reports/activities?ready=false'],
          });
          const route = { path: '/projects/:projectId/shift-reports/:tab' };
          const { user } = render(<ActivitiesToolbar projectId="project-0" />, { history, route });

          await user.click(await screen.findByRole('button', { name: /activities.filters.readiness.label/i }));
          await user.selectOptions(await screen.findByRole('listbox'), [
            await screen.findByRole('option', { name: 'activities.filters.readiness.options.ready' }),
          ]);

          expect(
            await screen.findByRole('button', { name: /activities.filters.readiness.options.notReady/ })
          ).toBeInTheDocument();
        });
      });
    });
  });

  describe('Search field', () => {
    it('renders search field', async () => {
      render(<ActivitiesToolbar projectId="project-0" />);

      expect(screen.getByPlaceholderText('activities.searchPlaceholder')).toBeInTheDocument();
    });

    it('updates search params when user types in search field', async () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports/activities'],
      });
      const route = { path: '/projects/:projectId/shift-reports/:tab' };
      const { user } = render(<ActivitiesToolbar projectId="project-0" />, {
        history,
        route,
      });

      await user.type(screen.getByPlaceholderText('activities.searchPlaceholder'), 'test');

      await waitFor(() =>
        expect(history.location).toEqual(
          expect.objectContaining({
            pathname: '/projects/project-0/shift-reports/activities',
            search: '?search=test',
          })
        )
      );
    });
  });
});
