import { MessageProvider, useMessageGetter } from '@messageformat/react';
import type {
  DocumentSchema,
  PatchApiProjectsProjectIdDocumentsDocumentIdMutationRequestSchema,
  ProjectSchema,
  ShiftActivityProgressLogSchema,
  ShiftActivitySchema,
  WeeklyWorkPlanSchema,
} from '@shape-construction/api/src/types';
import { FeedEventBase, Tooltip } from '@shape-construction/arch-ui';
import { PencilIcon, PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { formatDateAndTime, now, parseDate, parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { useInfiniteQuery } from '@tanstack/react-query';
import galleryMessages from 'app/components/Gallery/messages.en.yaml';
import { ProgressLogGalleryList } from 'app/pages/projects/[projectId]/weekly-planner/plan/components/progress/form/ProgressLogDocuments/ProgressLogGalleryList';
import { useProgressLogUploadWorkflow } from 'app/pages/projects/[projectId]/weekly-planner/plan/hooks/useProgressLogUploadWorkflow';
import {
  getProgressLogDocumentsInfiniteQueryOptions,
  useDeleteProgressLogDocument,
} from 'app/queries/progressLogs/progressLogDocuments';
import { useUpdateProjectDocument } from 'app/queries/projectDocuments/projectDocuments';
import { useProjectPerson } from 'app/queries/projects/people';
import { useProject } from 'app/queries/projects/projects';
import { useWeeklyWorkPlan } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import classNames from 'clsx';
import React, { useMemo } from 'react';
import { Link, useParams } from 'react-router';

const FeedIcon = () => (
  <div className="w-8 h-8 min-w-[32px] rounded-full bg-gray-100 flex items-center justify-center">
    <PlusCircleIcon className="w-5 h-5 text-gray-500" />
  </div>
);

type Params = {
  projectId: ProjectSchema['id'];
  shiftActivityId: ShiftActivitySchema['id'];
};

type ActivityHistoryItemProps = {
  progressLogId: ShiftActivityProgressLogSchema['id'];
  date: string;
  createdById: number;
  comment?: string | null;
  lastUpdatedAt?: string | null;
  lastUpdatedById?: number | null;
  progressTo?: number | null;
  weeklyWorkPlanId?: WeeklyWorkPlanSchema['id'];
  onEdit: () => void;
};

export const ActivityHistoryItem: React.FC<ActivityHistoryItemProps> = ({
  progressLogId,
  date,
  progressTo,
  createdById,
  comment,
  lastUpdatedAt,
  lastUpdatedById,
  weeklyWorkPlanId,
  onEdit,
}) => {
  const messages = useMessageGetter('activities.activityHistory');
  const { projectId, shiftActivityId } = useParams<Params>() as Params;
  const { data: project } = useProject(projectId);
  const { data: weeklyWorkPlan } = useWeeklyWorkPlan(projectId, weeklyWorkPlanId!);
  const { data: createdByMember } = useProjectPerson(projectId, createdById!);
  const { data: lastModifiedByMember } = useProjectPerson(projectId, lastUpdatedById!);
  const {
    data: progressLogDocumentsData,
    isPending,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery(getProgressLogDocumentsInfiniteQueryOptions(projectId, shiftActivityId, progressLogId));
  const { uploadNewDocuments } = useProgressLogUploadWorkflow(projectId, shiftActivityId, progressLogId);
  const { mutate: deleteProgressLogDocument } = useDeleteProgressLogDocument();
  const { mutate: updateProjectDocument, isPending: isDocumentUpdating } = useUpdateProjectDocument();

  const documents = useMemo(
    () => [
      ...(progressLogDocumentsData?.pages.flatMap((page) =>
        page.entries.map((entry) => ({
          ...entry.document,
          availableActions: {
            ...entry.document.availableActions,
            ...entry.documentReference.availableActions,
          },
        }))
      ) || []),
    ],
    [progressLogDocumentsData]
  );

  const handleDeleteDocument = (documentId: DocumentSchema['id']) => {
    const documentsData = [...(progressLogDocumentsData?.pages.flatMap(({ entries }) => entries) || [])];
    const documentDataToDelete = documentsData.find((documentDate) => documentDate.document.id === documentId);
    documentDataToDelete &&
      deleteProgressLogDocument({
        projectId,
        shiftActivityId,
        progressLogId,
        documentReferenceId: documentDataToDelete.documentReference.id,
      });
  };

  const handleUpdateDocument = (
    documentId: DocumentSchema['id'],
    values: PatchApiProjectsProjectIdDocumentsDocumentIdMutationRequestSchema,
    callback: () => void
  ) => {
    updateProjectDocument({ projectId, documentId, data: values }, { onSettled: callback });
  };

  const formattedDate = parseDate(date).isSame(parseDate(now()), 'year')
    ? parseDateWithFormat(date, 'DD MMM')
    : parseDateWithFormat(date, 'DD MMM YYYY');

  const hasOnlyProgressValue = !(comment || weeklyWorkPlan || lastUpdatedAt || documents.length);

  return (
    <section>
      <div className="bg-gray-100 text-center p-2">
        <span className="text-xs font-bold text-gray-500">{formattedDate}</span>
      </div>
      <FeedEventBase
        avatar={<FeedIcon />}
        className={classNames('px-4 py-3', {
          'items-center': hasOnlyProgressValue,
        })}
      >
        <FeedEventBase.Header className={classNames({ 'items-start!': !hasOnlyProgressValue })}>
          <div>
            <div className="text-sm leading-5 font-medium text-gray-900 space-x-1">
              <span className="font-medium">{createdByMember?.user?.name}</span>
              <span className="font-normal text-gray-700">{messages('updatedProgressTo')}</span>
              <span className="font-semibold">{messages('progress', { progress: progressTo })}</span>
              {comment && (
                <>
                  <span className="font-normal text-gray-700">{messages('withComment')}</span>
                  <span className="font-semibold">{`"${comment}"`}</span>
                </>
              )}
              {weeklyWorkPlan && (
                <>
                  <span className="font-normal text-gray-500">{messages('from')}</span>
                  <Link
                    target="_blank"
                    to={`/projects/${projectId}/weekly-planner/plans/${weeklyWorkPlan.id}`}
                    className="underline text-blue-600"
                  >
                    {weeklyWorkPlan.title}
                  </Link>
                </>
              )}
            </div>
            {lastUpdatedAt && (
              <div className="text-xs leading-4 text-gray-500 mt-3">
                {messages('modified', {
                  date: formatDateAndTime(lastUpdatedAt, project?.timezone!),
                  name: lastModifiedByMember?.user?.name,
                })}
              </div>
            )}
          </div>
          <Tooltip.Root>
            <Tooltip.Trigger asChild>
              <FeedEventBase.Header.Action aria-label="edit-progress-log" icon={PencilIcon} onClick={onEdit} />
            </Tooltip.Trigger>
            <Tooltip.Content side="bottom" className="z-popover">
              {messages('edit')}
            </Tooltip.Content>
          </Tooltip.Root>
        </FeedEventBase.Header>
        {!!documents?.length && (
          <FeedEventBase.Content className="mt-3">
            <MessageProvider messages={galleryMessages}>
              <ProgressLogGalleryList
                projectId={projectId}
                documents={documents}
                isLoading={isPending}
                hasNextPage={hasNextPage}
                isFetchingNextPage={isFetchingNextPage}
                isDocumentUpdating={isDocumentUpdating}
                fetchNextPage={fetchNextPage}
                handleDeleteDocument={handleDeleteDocument}
                handleUpdateDocument={handleUpdateDocument}
                uploadDocuments={uploadNewDocuments}
              />
            </MessageProvider>
          </FeedEventBase.Content>
        )}
      </FeedEventBase>
    </section>
  );
};
