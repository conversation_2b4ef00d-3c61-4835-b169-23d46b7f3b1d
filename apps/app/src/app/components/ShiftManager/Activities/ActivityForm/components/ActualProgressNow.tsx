import { useMessageGetter } from '@messageformat/react';
import type {
  ProjectSchema,
  ShiftActivitySchema,
  WeeklyWorkPlanActivitySchema,
} from '@shape-construction/api/src/types';
import { Button, PopoverMenu, Tooltip } from '@shape-construction/arch-ui';
import { InformationCircleIcon, PencilSquareIcon, PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { now, parseDateWithFormat } from '@shape-construction/utils/DateTime';
import { useProgressDrawerState } from 'app/pages/projects/[projectId]/weekly-planner/plan/hooks/useProgressDrawerState';
import { useShiftActivity } from 'app/queries/activities/activities';
import { useShiftActivityProgressLogs } from 'app/queries/progressLogs/progressLogs';
import React from 'react';
import { useParams } from 'react-router';

type Params = {
  projectId: ProjectSchema['id'];
  shiftActivityId: ShiftActivitySchema['id'];
};

export const ActualProgressNow = () => {
  const messages = useMessageGetter('activities.form.fields.progressNow');
  const { projectId, shiftActivityId } = useParams<Params>();
  const { openDrawer } = useProgressDrawerState();
  const { data: shiftActivity } = useShiftActivity(projectId!, shiftActivityId!);
  const { data: progressLogsData } = useShiftActivityProgressLogs(projectId!, shiftActivityId!);

  if (shiftActivity) {
    const latestProgressLog = progressLogsData?.entries?.[0];
    const newProgressPlanActivity = { shiftActivity };
    const editProgressPlanActivity = {
      shiftActivity,
      progressLog: latestProgressLog,
    };

    const progress = shiftActivity.percentageCompleted ?? 0;
    const hasProgress = !!progress;
    const hasExistingProgressLog = !!latestProgressLog;
    const isLatestProgressLogToday = now().format('YYYY-MM-DD') === latestProgressLog?.date;
    const isNewProgressLogEnabled = !isLatestProgressLogToday && progress !== 100;

    return (
      <div className="p-2 space-y-1">
        <div className="flex items-center gap-1">
          <p className="text-sm leading-5 font-medium">{messages('label')}</p>
          {latestProgressLog?.trackedIn?.type === 'weekly_work_plan' && (
            <PopoverMenu.Root>
              <PopoverMenu.Trigger>
                <InformationCircleIcon className="h-4 w-4 text-indigo-500" />
              </PopoverMenu.Trigger>
              <PopoverMenu.Content>
                <div className="md:w-72 space-y-2">
                  <p className="text-base leading-6 font-medium">{messages('connectedInfo.title')}</p>
                  <p>{messages('connectedInfo.description')}</p>
                </div>
              </PopoverMenu.Content>
            </PopoverMenu.Root>
          )}
        </div>
        {hasProgress ? (
          <p className="text-sm leading-5 font-medium text-gray-700">
            {progress}% as of {latestProgressLog ? parseDateWithFormat(latestProgressLog?.date, 'DD-MMM-YYYY') : ''}{' '}
            {isLatestProgressLogToday && <span className="font-normal text-gray-500">(today)</span>}
          </p>
        ) : (
          <p className="text-sm leading-5 font-medium text-gray-400">{messages('noProgress')}</p>
        )}

        <div className="space-x-1">
          {hasExistingProgressLog && (
            <Button
              leadingIcon={PencilSquareIcon}
              color="secondary"
              variant="outlined"
              size="xs"
              onClick={() => {
                openDrawer(editProgressPlanActivity as any);
              }}
            >
              {messages('editProgressLogCTA')}
            </Button>
          )}
          <Tooltip.Root>
            <Tooltip.Trigger asChild>
              <Button
                leadingIcon={PlusIcon}
                color="secondary"
                variant="outlined"
                size="xs"
                disabled={!isNewProgressLogEnabled}
                onClick={() => {
                  openDrawer(newProgressPlanActivity as WeeklyWorkPlanActivitySchema);
                }}
              >
                {messages('newProgressLogCTA')}
              </Button>
            </Tooltip.Trigger>
            <Tooltip.Content align="center" side="bottom" className="z-popover" hidden={isNewProgressLogEnabled}>
              {messages('connectedInfo.disabledNewProgressTooltip')}
            </Tooltip.Content>
          </Tooltip.Root>
        </div>
      </div>
    );
  }

  return (
    <div className="p-2 space-y-1 mt-2!">
      <p className="text-sm leading-5 font-medium">{messages('label')}</p>
      <p className="text-sm leading-5 font-medium text-gray-400">{messages('placeholder')}</p>
    </div>
  );
};
