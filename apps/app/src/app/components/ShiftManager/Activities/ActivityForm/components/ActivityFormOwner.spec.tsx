import React from 'react';
import { render, screen } from 'tests/test-utils';
import { ActivityFormOwner } from './ActivityFormOwner';

describe('<ActivityFormOwner />', () => {
  it('renders correctly', async () => {
    render(<ActivityFormOwner />);

    expect(await screen.findByText('activities.form.fields.owner.label')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /activities.form.fields.owner.triggerLabel/ })).toBeInTheDocument();
  });
});
