import { locationFactory } from '@shape-construction/api/factories/locations';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { resourceFactory } from '@shape-construction/api/factories/resources';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { teamFactory } from '@shape-construction/api/factories/teams';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdLocationsMockHandler } from '@shape-construction/api/handlers-factories/projects/locations';
import { getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler } from '@shape-construction/api/handlers-factories/projects/shift-reports';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { TIMEZONE_DEFAULT } from 'app/constants/DateTime';
import { createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen } from 'tests/test-utils';
import { ActivityForm } from './ActivityForm';

describe('<ActivityForm />', () => {
  it('renders all form fields with correct labels', async () => {
    render(<ActivityForm timeZone={TIMEZONE_DEFAULT} onSubmit={jest.fn()} />);

    expect(await screen.findByRole('textbox', { name: /activities.form.fields.description/ })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'activities.form.fields.location.placeholder' })).toBeInTheDocument();
    expect(screen.getByText('activities.form.fields.organisation.label')).toBeInTheDocument();
    expect(screen.getByText('activities.form.fields.owner.label')).toBeInTheDocument();
    expect(screen.getByRole('radio', { name: 'activities.form.fields.status.options.completed' })).toBeInTheDocument();
    expect(screen.getByText('activities.form.fields.progressNow.label')).toBeInTheDocument();
    expect(screen.getByText('dateTime.timezoneInfo')).toBeInTheDocument();
    expect(screen.getByLabelText('activities.form.fields.planned_start_date')).toBeInTheDocument();
    expect(screen.getByLabelText('activities.form.fields.planned_end_date')).toBeInTheDocument();
    expect(screen.getByLabelText('activities.form.fields.expected_finish_date')).toBeInTheDocument();
    expect(screen.getByLabelText('activities.form.fields.actual_start_date')).toBeInTheDocument();
    expect(screen.getByLabelText('activities.form.fields.actual_end_date')).toBeInTheDocument();
    expect(screen.getByRole('switch')).toHaveAttribute('id', 'critical-toggle');
  });

  describe('when creating a new activity', () => {
    it('renders empty inputs', async () => {
      const hookFormValues = {
        actual_end_date: null,
        actual_start_date: null,
        description: '',
        location_id: null,
        organisation_resource_id: null,
        owner_id: null,
        percentage_completed: null,
        planned_end_date: null,
        planned_start_date: null,
        expected_finish_date: null,
        reference_number: null,
        status: 'not_started',
      };

      render(<ActivityForm timeZone={TIMEZONE_DEFAULT} onSubmit={jest.fn()} />, {
        hookFormValues,
      });

      expect(await screen.findByRole('textbox', { name: /activities.form.fields.description/ })).toHaveValue('');
      expect(screen.getByRole('switch')).not.toBeChecked();
      expect(screen.getByRole('button', { name: 'activities.form.fields.location.placeholder' })).toHaveValue('');
      expect(screen.getByPlaceholderText('activities.form.fields.organisation.placeholder')).toHaveValue('');
      expect(screen.getByRole('button', { name: /activities.form.fields.owner.triggerLabel/ })).toBeInTheDocument();
      expect(screen.getByRole('radio', { name: 'activities.form.fields.status.options.not_started' })).toBeChecked();
      expect(
        screen.getByRole('radio', { name: 'activities.form.fields.status.options.in_progress' })
      ).not.toBeChecked();
      expect(screen.getByRole('radio', { name: 'activities.form.fields.status.options.completed' })).not.toBeChecked();
      expect(screen.getByText('activities.form.fields.progressNow.label')).toBeInTheDocument();
      expect(screen.getByLabelText('activities.form.fields.planned_start_date')).toHaveValue('');
      expect(screen.getByLabelText('activities.form.fields.planned_end_date')).toHaveValue('');
      expect(screen.getByLabelText('activities.form.fields.expected_finish_date')).toHaveValue('');
      expect(screen.getByLabelText('activities.form.fields.actual_start_date')).toHaveValue('');
      expect(screen.getByLabelText('activities.form.fields.actual_end_date')).toHaveValue('');
    });
  });

  describe('when editing an activity', () => {
    it('renders pre-filled inputs', async () => {
      const project = projectFactory({ id: 'project-0', currentTeamId: 'team-0' });
      const resources = [resourceFactory({ id: 'resource-1', name: 'org-1', kind: 'organisation' })];
      const teamMembers = [
        teamMemberFactory({
          id: 1,
          projectId: project.id,
          team: teamFactory({ id: project.currentTeamId }),
          user: userBasicDetailsFactory({ name: 'John Doe' }),
        }),
        teamMemberFactory({
          id: 2,
          projectId: project.id,
          team: teamFactory({ id: project.currentTeamId }),
          user: userBasicDetailsFactory({ name: 'John Travolta' }),
        }),
      ];
      server.use(
        getApiProjectsProjectIdLocationsMockHandler(() => [
          locationFactory({
            id: 'location-0',
            parentLocationId: null,
            shortCode: '123',
            projectId: 'project-0',
            name: 'Location A',
          }),
        ]),
        getApiProjectsProjectIdMockHandler(() => projectFactory({ id: 'project-0', rootLocationId: 'abc-456' })),
        getApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMockHandler(() => ({ entries: resources })),
        getApiProjectsProjectIdPeopleMockHandler(() => teamMembers)
      );
      const hookFormValues = {
        actual_end_date: '2023-12-23T02:37',
        actual_start_date: '2023-12-09T02:37',
        critical: true,
        description: 'description-0',
        location_id: 'location-0',
        organisation_resource_id: 'resource-1',
        owner_id: 1,
        percentage_completed: 73.5,
        planned_end_date: '2023-12-31T02:37',
        planned_start_date: '2023-12-08T02:37',
        expected_finish_date: '2023-12-31T02:37',
        reference_number: 'A0001',
        status: 'in_progress',
      };
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/activities/activity-0/edit'],
      });
      const route = { path: '/projects/:projectId/activities/:activityId/edit' };

      render(<ActivityForm timeZone={TIMEZONE_DEFAULT} onSubmit={jest.fn()} />, {
        hookFormValues,
        history,
        route,
      });

      expect(await screen.findByRole('textbox', { name: /activities.form.fields.description/ })).toHaveValue(
        'description-0'
      );
      expect(await screen.findByRole('button', { name: 'Location A' })).toBeInTheDocument();
      expect(await screen.findByRole('button', { name: /John Doe/ })).toBeInTheDocument();
      expect(
        await screen.findByRole('radio', {
          name: 'activities.form.fields.status.options.not_started',
        })
      ).not.toBeChecked();
      expect(screen.getByRole('radio', { name: 'activities.form.fields.status.options.in_progress' })).toBeChecked();
      expect(screen.getByRole('radio', { name: 'activities.form.fields.status.options.completed' })).not.toBeChecked();
      expect(screen.getByText('activities.form.fields.progressNow.label')).toBeInTheDocument();
      expect(screen.getByLabelText('activities.form.fields.planned_start_date')).toHaveValue('2023-12-08T02:37');
      expect(screen.getByLabelText('activities.form.fields.planned_end_date')).toHaveValue('2023-12-31T02:37');
      expect(screen.getByLabelText('activities.form.fields.expected_finish_date')).toHaveValue('2023-12-31T02:37');
      expect(screen.getByLabelText('activities.form.fields.actual_start_date')).toHaveValue('2023-12-09T02:37');
      expect(screen.getByLabelText('activities.form.fields.actual_end_date')).toHaveValue('2023-12-23T02:37');
    });
  });
});
