import { SkeletonText } from '@shape-construction/arch-ui/src/Skeleton';

export const LoadingSkeleton: React.FC = () => (
  <div className="flex flex-col p-6 space-y-6" role="alert" aria-label="loading form" aria-busy="true">
    {Array.from(Array(4).keys()).map((id) => (
      <div className="flex flex-col gap-2" key={id}>
        <SkeletonText size="xxs" animation="pulse" />
        <SkeletonText size="sm" animation="pulse" />
      </div>
    ))}
  </div>
);
