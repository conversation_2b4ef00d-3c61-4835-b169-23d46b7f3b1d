import { useMessageGetter } from '@messageformat/react';
import { type ProjectSchema, shiftActivityStatusEnum } from '@shape-construction/api/src/types';
import { InputDateTime, InputRadioButtonGroup, InputText, InputToggle } from '@shape-construction/arch-ui';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import type { InputRadioButtonGroupOption } from '@shape-construction/arch-ui/src/InputRadioButtonGroup/InputRadioButtonGroup';
import { AlertTimeZone } from 'app/components/UI/AlertTimeZone/AlertTimeZone';
import React from 'react';
import { useFormContext } from 'react-hook-form';
import { ActivityFormLocation } from './components/ActivityFormLocation';
import { ActivityFormOrganisation } from './components/ActivityFormOrganisation';
import { ActivityFormOwner } from './components/ActivityFormOwner';
import { ActualProgressNow } from './components/ActualProgressNow';

const statuses = Object.keys(shiftActivityStatusEnum);

const getStatusOptions = (messages: ReturnType<typeof useMessageGetter>): InputRadioButtonGroupOption[] =>
  statuses.map((status) => ({
    label: messages(`fields.status.options.${status}`),
    value: status,
  }));

interface ActivityFormProps {
  timeZone?: ProjectSchema['timezone'];
  onSubmit: () => void;
}

export const ActivityForm: React.FC<ActivityFormProps> = ({ timeZone, onSubmit }) => {
  const messages = useMessageGetter('activities.form');
  const { register, formState, setValue, watch } = useFormContext();
  const { errors } = formState;

  return (
    <form onSubmit={onSubmit} className="p-6 space-y-6">
      <InputText
        fullWidth
        {...register('description')}
        label={messages('fields.description')}
        error={errors.description?.message?.toString()}
        required
      />

      <div>
        <InputText
          fullWidth
          maxLength={50}
          {...register('task_identifier')}
          label={messages('fields.task_identifier.label')}
          error={errors.task_identifier?.message?.toString()}
        />
        <span className="text-xs leading-4 text-neutral-500">{messages('fields.task_identifier.helpText')}</span>
      </div>

      <ActivityFormLocation />

      <ActivityFormOrganisation />

      <ActivityFormOwner />

      <label htmlFor="status" className="block text-sm font-medium text-gray-700">
        {messages('fields.status.label')}

        <InputRadioButtonGroup
          type="simple"
          {...register('status')}
          options={getStatusOptions(messages)}
          error={errors.status?.message?.toString()}
          setFieldValue={setValue}
          value={watch('status')}
        />
      </label>

      <div className="flex gap-x-2">
        <InputToggle
          {...register('critical')}
          checked={watch('critical')}
          onChange={(checked) => setValue('critical', checked)}
          id="critical-toggle"
        />

        <label className="flex p-1 gap-x-2" htmlFor="critical-toggle">
          <ExclamationTriangleIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
          {messages('fields.critical.label')}
        </label>
      </div>

      <ActualProgressNow />

      <div className="space-y-6">
        <InputDateTime
          fullWidth
          {...register('planned_start_date')}
          label={messages('fields.planned_start_date')}
          error={errors.planned_start_date?.message?.toString()}
        />

        <InputDateTime
          fullWidth
          {...register('planned_end_date')}
          label={messages('fields.planned_end_date')}
          error={errors.planned_end_date?.message?.toString()}
        />

        <InputDateTime
          fullWidth
          {...register('expected_finish_date')}
          label={messages('fields.expected_finish_date')}
          error={errors.expected_finish_date?.message?.toString()}
        />
      </div>

      <div className="space-y-6">
        <InputDateTime
          fullWidth
          {...register('actual_start_date')}
          label={messages('fields.actual_start_date')}
          error={errors.actual_start_date?.message?.toString()}
        />

        <InputDateTime
          fullWidth
          {...register('actual_end_date')}
          label={messages('fields.actual_end_date')}
          error={errors.actual_end_date?.message?.toString()}
        />
      </div>

      {timeZone && <AlertTimeZone timeZone={timeZone} />}
    </form>
  );
};
