import { useMessageGetter } from '@messageformat/react';
import { PersonSelect } from 'app/components/Filters/PersonSelect/PersonSelect';
import React from 'react';
import { Controller, useFormContext } from 'react-hook-form';

export const ActivityFormOwner: React.FC = () => {
  const messages = useMessageGetter('activities.form.fields.owner');
  const currentUserMessage = useMessageGetter('currentUserLabel');
  const { control } = useFormContext();

  return (
    <label htmlFor="owner_id" className="block text-sm font-medium text-gray-700">
      {messages('label')}
      <div className="flex items-center p-1 my-1 -ml-2.5">
        <div className="text-sm font-medium text-gray-900 w-full">
          <Controller
            control={control}
            name="owner_id"
            render={({ field }) => (
              <PersonSelect
                value={field.value}
                onChange={field.onChange}
                triggerLabel={messages('triggerLabel')}
                filterByLabel={messages('filterByLabel')}
                currentUserLabel={currentUserMessage('')}
                searchPlaceholder={messages('searchPlaceholder')}
              />
            )}
          />
        </div>
      </div>
    </label>
  );
};
