import { useMessageGetter } from '@messageformat/react';
import type { ShiftActivitySchema } from '@shape-construction/api/src/types';
import { Button } from '@shape-construction/arch-ui';
import { UnlinkIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useMediaQuery } from '@shape-construction/hooks';
import React from 'react';
import { ActivityCardArchived } from '../ActivitiesList/components/ActivityCard/ActivityCardArchived/ActivityCardArchived';
import { ActivityCardDescription } from '../ActivitiesList/components/ActivityCard/ActivityCardDescription/ActivityCardDescription';
import { ActivityCardLocation } from '../ActivitiesList/components/ActivityCard/ActivityCardLocation/ActivityCardLocation';
import { ActivityCardProgress } from '../ActivitiesList/components/ActivityCard/ActivityCardProgress/ActivityCardProgress';
import { ActivityCardRef } from '../ActivitiesList/components/ActivityCard/ActivityCardRef/ActivityCardRef';
import { ExpectedFinish } from '../ActivitiesList/components/ActivityCard/ExpectedFinish/ExpectedFinish';

export interface ActivityCardProps {
  shiftActivity: ShiftActivitySchema;
  unlinkActivity: () => void;
}
export const LinkedActivity: React.FC<ActivityCardProps> = ({ shiftActivity, unlinkActivity }) => {
  const { value: isStreamliningProgressLogs } = useFeatureFlag('streamlining-progress-logs');
  const messages = useMessageGetter('shiftReport.form');
  const isLargeScreen = useMediaQuery(breakpoints.up('lg'));

  if (isStreamliningProgressLogs) {
    return (
      <div
        className="flex flex-col  w-full truncate p-4 bg-white rounded-md border border-gray-300 lg:flex-row lg:justify-between"
        data-testid="linked-activity-container"
      >
        <div className="flex flex-col lg:flex-row space-y-3 lg:space-x-3 lg:space-y-0 lg:flex-1 lg:items-center lg:justify-between">
          <div className="flex items-center truncate">
            <div className="w-full truncate">
              <ActivityCardDescription description={shiftActivity.description} />
            </div>
          </div>
          <div className="flex flex-col space-y-3 truncate lg:flex-row lg:space-y-0 lg:space-x-3">
            <ActivityCardRef referenceNumber={shiftActivity.referenceNumber} />
            <ActivityCardProgress progress={shiftActivity.percentageCompleted} />
            <ExpectedFinish expectedFinish={shiftActivity.expectedFinishDate} />
            <ActivityCardArchived isArchived={shiftActivity.archived} />
          </div>
          <div>
            <Button leadingIcon={UnlinkIcon} color="danger" size="sm" variant="outlined" onClick={unlinkActivity}>
              {messages('unlinkActivityCTA')}
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="flex flex-col lg:flex-row w-full truncate p-4 gap-x-3 gap-y-4 bg-white rounded-md border border-gray-300 items-center"
      data-testid="linked-activity-container"
    >
      <div className="w-full flex items-center truncate">
        <div className="w-full truncate">
          <ActivityCardDescription description={shiftActivity.description} />
        </div>
        {!isLargeScreen && (
          <Button color="primary" size="sm" variant="text" onClick={unlinkActivity}>
            {messages('unlinkActivityCTA')}
          </Button>
        )}
      </div>

      <div className="flex flex-row flex-wrap items-center gap-x-4 gap-y-2 w-full truncate">
        <ActivityCardRef referenceNumber={shiftActivity.referenceNumber} />
        <ActivityCardLocation locationId={shiftActivity.locationId} />
        <ActivityCardProgress progress={shiftActivity.percentageCompleted} />
        <ActivityCardArchived isArchived={shiftActivity.archived} />
      </div>

      {isLargeScreen && (
        <Button color="primary" size="sm" variant="text" onClick={unlinkActivity}>
          {messages('unlinkActivityCTA')}
        </Button>
      )}
    </div>
  );
};
