import { projectFactory } from '@shape-construction/api/factories/projects';
import { getApiProjectsMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { type Location, createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { ProjectsMenu } from './ProjectsMenu';

describe('ProjectsMenu', () => {
  beforeEach(() => {
    server.use(
      getApiProjectsMockHandler(() => [
        projectFactory({
          id: 'project-0',
          title: 'project-0',
          currentTeamMemberStatus: 'joined',
        }),
        projectFactory({
          id: 'project-1',
          title: 'project-1',
          currentTeamMemberStatus: 'joined',
        }),
      ])
    );
  });

  it('shows all available projects on opening', async () => {
    render(
      <ProjectsMenu>
        <div>Projects Menu</div>
      </ProjectsMenu>
    );

    await userEvent.click(await screen.findByText('Projects Menu'));

    expect((await screen.findAllByRole('link', { name: 'project item' })).length).toBe(2);
    expect((await screen.findAllByText('project-0'))[0]).toBeInTheDocument();
    expect((await screen.findAllByText('project-1'))[0]).toBeInTheDocument();
  });

  it('redirects to the project issues list when clicking on an item', async () => {
    const history = createMemoryHistory<{ background: Location<any> }>({
      initialEntries: ['/'],
    });

    render(
      <ProjectsMenu>
        <div>Projects Menu</div>
      </ProjectsMenu>,
      { history }
    );

    await userEvent.click(screen.getByText('Projects Menu'));
    await userEvent.click((await screen.findAllByText('project-1'))[0]);

    await waitFor(() => expect(history.location.pathname).toEqual('/projects/project-1/issues'));
  });

  describe('when on issues details page', () => {
    it('redirects to the project issues list', async () => {
      const route = { path: '/projects/:projectId/issues/:issueId' };
      const history = createMemoryHistory<{ background: Location<any> }>({
        initialEntries: ['/projects/project-0/issues/issue-0'],
      });

      render(
        <ProjectsMenu>
          <div>Projects Menu</div>
        </ProjectsMenu>,
        { history, route }
      );

      await userEvent.click(screen.getByText('Projects Menu'));
      await userEvent.click((await screen.findAllByText('project-1'))[0]);

      expect(history.location.pathname).toEqual('/projects/project-1/issues');
    });
  });

  describe('when in issue print preview', () => {
    it('redirects to the project issues list', async () => {
      const route = { path: '/projects/:projectId/issues/:issueId/print' };
      const history = createMemoryHistory<{ background: Location<any> }>({
        initialEntries: ['/projects/project-0/issues/issue-0/print'],
      });

      render(
        <ProjectsMenu>
          <div>Projects Menu</div>
        </ProjectsMenu>,
        { history, route }
      );

      await userEvent.click(screen.getByText('Projects Menu'));
      await userEvent.click((await screen.findAllByText('project-1'))[0]);

      expect(history.location.pathname).toEqual('/projects/project-1/issues');
    });
  });

  it('redirects to the same page on the new project when clicking on an item', async () => {
    const route = { path: '/projects/:projectId/timeline' };
    const history = createMemoryHistory({ initialEntries: ['/projects/project-0/timeline'] });

    render(
      <ProjectsMenu>
        <div>Projects Menu</div>
      </ProjectsMenu>,
      { history, route }
    );

    await userEvent.click(screen.getByText('Projects Menu'));
    await userEvent.click((await screen.findAllByText('project-1'))[0]);

    await waitFor(() => expect(history.location.pathname).toEqual('/projects/project-1/timeline'));
  });

  it('redirects to the teams page if teams detail page is open', async () => {
    const route = { path: '/projects/:projectId/settings/teams/:teamId' };
    const history = createMemoryHistory<{ background: Location<any> }>({
      initialEntries: ['/projects/project-0/settings/teams/team-1'],
    });

    render(
      <ProjectsMenu>
        <div>Projects Menu</div>
      </ProjectsMenu>,
      { history, route }
    );

    await userEvent.click(screen.getByText('Projects Menu'));
    await userEvent.click((await screen.findAllByText('project-1'))[0]);

    await waitFor(() => expect(history.location.pathname).toEqual('/projects/project-1/settings/teams'));
  });

  it('closes the projects menu on clicking on an item', async () => {
    render(
      <ProjectsMenu>
        <div>Projects Menu</div>
      </ProjectsMenu>
    );

    await userEvent.click(screen.getByText('Projects Menu'));
    await userEvent.click((await screen.findAllByText('project-1'))[0]);

    expect(screen.queryByText('project-1')).not.toBeInTheDocument();
  });

  it('does not redirect when user selects the same project', async () => {
    const route = { path: '/projects/:projectId/issues' };
    const history = createMemoryHistory<{ background: Location<any> }>({
      initialEntries: ['/projects/project-0/issues'],
    });

    const spyOnHistory = jest.spyOn(history, 'push');

    render(
      <ProjectsMenu>
        <div>Projects Menu</div>
      </ProjectsMenu>,
      { history, route }
    );

    await userEvent.click(screen.getByText('Projects Menu'));
    await userEvent.click((await screen.findAllByText('project-0'))[0]);

    expect(spyOnHistory).not.toBeCalled();
    expect(history.location.pathname).toEqual('/projects/project-0/issues');
  });

  it('redirects to new project page when user clicks on new project link', async () => {
    const route = { path: '/projects/:projectId/issues' };
    const history = createMemoryHistory<{ background: Location<any> }>({
      initialEntries: ['/projects/project-0/issues'],
    });

    render(
      <ProjectsMenu>
        <div>Projects Menu</div>
      </ProjectsMenu>,
      { history, route }
    );

    await userEvent.click(screen.getByText('Projects Menu'));
    await userEvent.click(screen.getAllByRole('link', { name: 'projectsMenu.newProject' })[0]);

    expect(history.location.pathname).toEqual('/my-projects/new');
  });

  it('redirects to all project page when user clicks on all projects link', async () => {
    const route = { path: '/projects/:projectId/issues' };
    const history = createMemoryHistory<{ background: Location<any> }>({
      initialEntries: ['/projects/project-0/issues'],
    });

    render(
      <ProjectsMenu>
        <div>Projects Menu</div>
      </ProjectsMenu>,
      { history, route }
    );

    await userEvent.click(screen.getByText('Projects Menu'));
    await userEvent.click(screen.getAllByRole('link', { name: 'projectsMenu.allProjects' })[0]);

    expect(history.location.pathname).toEqual('/my-projects');
  });

  describe('when in weekly work plan activities list', () => {
    it('redirects to the project weekly work plans list', async () => {
      const route = { path: '/projects/:projectId/weekly-planner/plans/:planId' };
      const history = createMemoryHistory<{ background: Location<any> }>({
        initialEntries: ['/projects/project-0/weekly-planner/plans/plan-0'],
      });
      render(
        <ProjectsMenu>
          <div>Projects Menu</div>
        </ProjectsMenu>,
        { history, route }
      );

      await userEvent.click(screen.getByText('Projects Menu'));
      await userEvent.click((await screen.findAllByText('project-1'))[0]);

      expect(history.location.pathname).toEqual('/projects/project-1/weekly-planner/plans');
    });
  });

  describe('when in weekly work plan progress tracker tab', () => {
    it('redirects to the project weekly work plans list', async () => {
      const route = { path: '/projects/:projectId/weekly-planner/plans/:planId/progress-logs' };
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/weekly-planner/plans/plan-0/progress-logs'],
      });
      render(
        <ProjectsMenu>
          <div>Projects Menu</div>
        </ProjectsMenu>,
        { history, route }
      );

      await userEvent.click(screen.getByText('Projects Menu'));
      await userEvent.click((await screen.findAllByText('project-1'))[0]);

      expect(history.location.pathname).toEqual('/projects/project-1/weekly-planner/plans');
    });
  });
});
