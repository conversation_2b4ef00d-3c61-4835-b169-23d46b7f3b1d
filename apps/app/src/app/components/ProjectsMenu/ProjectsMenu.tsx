import { useMessageGetter } from '@messageformat/react';
import { Divider, Dropdown, cn } from '@shape-construction/arch-ui';
import { Avatar } from '@shape-construction/arch-ui/src/Avatar';
import { FolderIcon, PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useQuery } from '@tanstack/react-query';
import projectIdReplacer from 'app/lib/utils/projectid-replacer';
import { getJoinedProjectsQueryOptions } from 'app/queries/projects/projects';
import React, { useState, type ReactElement } from 'react';
import { Link, NavLink, useLocation, useMatch } from 'react-router';

export interface ProjectsMenuProps {
  children: ReactElement;
}

const ProjectsMenu: React.FC<ProjectsMenuProps> = ({ children }) => {
  const messages = useMessageGetter('projectsMenu');
  const location = useLocation();
  const { data: joinedProjects } = useQuery(getJoinedProjectsQueryOptions());
  const [menuOpen, setMenuOpen] = useState(false);

  const closeMenu = () => setMenuOpen(false);

  const isProjectRoute = useMatch({ path: '/projects/:projectId', end: false });
  const isProjectIssueRoute = useMatch('/projects/:projectId/issues/:issueId/*');
  const isProjectTeamDetailsRoute = useMatch('/projects/:projectId/settings/teams/:id');
  const isProjectShiftReportDetailsRoute = useMatch('/projects/:projectId/shift-reports/:shiftReportId');
  const isProjectWeeklyPlanRoute = useMatch('/projects/:projectId/weekly-planner/plans/:planId/*');

  const generateProjectURL = (projectId: string) => {
    const defaultURL = `/projects/${projectId}/issues`;

    if (isProjectIssueRoute) return { to: defaultURL };
    if (isProjectTeamDetailsRoute) return { to: `/projects/${projectId}/settings/teams` };
    if (isProjectShiftReportDetailsRoute) return { to: `/projects/${projectId}/shift-reports` };
    if (isProjectWeeklyPlanRoute) return { to: `/projects/${projectId}/weekly-planner/plans` };
    if (isProjectRoute)
      return {
        to: projectIdReplacer(location.pathname, projectId),
        state: location.state,
      };

    return { to: defaultURL };
  };
  return (
    <Dropdown.Root open={menuOpen} onOpenChange={setMenuOpen}>
      <Dropdown.Trigger asChild>{children}</Dropdown.Trigger>
      <Dropdown.Items side="bottom" align="start" alignOffset={6}>
        <div className="max-h-[260px] w-full overflow-y-auto md:max-w-[400px]">
          {joinedProjects?.map((project) => (
            <Dropdown.Item key={project.id} className="p-0 md:p-0" onClick={closeMenu}>
              <NavLink
                aria-label="project item"
                className={cn(
                  'aria-[current=page]:bg-gray-100 px-4 py-2 flex items-center justify-start text-sm font-normal leading-5'
                )}
                {...generateProjectURL(project.id)}
              >
                <Avatar variant="rounded" imgURL={project.logoUrl || ''} text={project.shortName} size="lg" />
                <span className="ml-2 overflow-hidden text-ellipsis whitespace-nowrap">{project.title}</span>
              </NavLink>
            </Dropdown.Item>
          ))}
        </div>
        <Divider orientation="horizontal" />

        <nav className="flex flex-col text-sm font-normal leading-5">
          <Link to="/my-projects" className="py-1.5">
            <Dropdown.Item startAdornment={<FolderIcon className="h-5 w-5 text-gray-400" />}>
              {messages('allProjects')}
            </Dropdown.Item>
          </Link>
          <Link to="/my-projects/new" state={{ background: location }} className="pb-1.5">
            <Dropdown.Item startAdornment={<PlusCircleIcon className="h-5 w-5 text-gray-400" />}>
              {messages('newProject')}
            </Dropdown.Item>
          </Link>
        </nav>
      </Dropdown.Items>
    </Dropdown.Root>
  );
};

export { ProjectsMenu };
