import { useMessageGetter } from '@messageformat/react';
import type {
  PostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationRequestSchema,
  ProjectSchema,
  ResourceKindSchema,
  ResourceSchema,
} from '@shape-construction/api/src/types';
import { Search, cn } from '@shape-construction/arch-ui';
import { PlusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useClickAway } from '@shape-construction/hooks';
import { keepPreviousData, useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { SearchLoading } from 'app/components/Search/SearchLoading';
import { SearchFieldLoadMoreButton } from 'app/components/SearchFieldLoadMoreButton/SearchFieldLoadMoreButton';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { getResourcesInfiniteQueryOptions, useCreateResource } from 'app/queries/resources/resources';
import React, { type MouseEvent, useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router';

type Params = {
  projectId: ProjectSchema['id'];
};

type CreateOptionButtonProps = {
  optionTerm: string;
  createOption: (keyword: string) => void;
};

const CreateOptionButton = ({ optionTerm, createOption }: CreateOptionButtonProps) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor.organisation.resourceSearchField');

  return (
    <Search.Button
      role="button"
      className="w-full truncate border-t border-neutral-subtlest"
      onClick={() => createOption(optionTerm)}
      onMouseDown={(event: MouseEvent) => event.preventDefault()}
    >
      <PlusIcon className="w-3 h-3 mr-2" />
      {messages('createButton', { term: optionTerm })}
    </Search.Button>
  );
};

export type ResourceSearchFieldProps = {
  kind: ResourceKindSchema;
  value?: ResourceSchema['id'] | null;
  onChange: (value: ResourceSchema['id'] | null) => void;
  placeholder?: string;
  inputClassName?: string;
  resourceName: ResourceSchema['name'] | null;
};

export const ResourceSearchField: React.FC<ResourceSearchFieldProps> = ({
  kind,
  value,
  onChange,
  placeholder,
  inputClassName,
  resourceName,
}) => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.planEditor.organisation.resourceSearchField');

  const [selectedValue, setSelectedValue] = useState<ResourceSchema | null>();
  const [searchTerm, setSearchTerm] = useState('');
  const clearSearchTerm = () => setSearchTerm('');
  const isSearchMode = Boolean(searchTerm);

  const defaultDisplayValue = resourceName || '';

  const [optionsOpen, setOptionsOpen] = useState(false);
  const toggleOptions = () => setOptionsOpen(!optionsOpen);
  const openOptions = () => setOptionsOpen(true);
  const closeOptions = () => setOptionsOpen(false);

  const searchInputRef = useRef<HTMLDivElement>(null);
  useClickAway(searchInputRef, () => {
    // Carefull when changing these lines of code.
    // This condition prevents close from happening on every click away
    if (optionsOpen) {
      closeOptions();
    }
  });

  const { projectId } = useParams<Params>() as Params;
  const { data: project } = useQuery(getProjectQueryOptions(projectId!));
  const { mutate: createResource } = useCreateResource();
  const {
    data: searchedResources,
    isLoading,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useInfiniteQuery({
    ...getResourcesInfiniteQueryOptions(projectId, project?.currentTeamId!, kind, { search: searchTerm }),
    placeholderData: keepPreviousData,
    select: (data) => data?.pages.flatMap(({ entries }) => entries).filter((resource) => resource.disabled === false),
  });
  const hasZeroResults = searchedResources?.length === 0;

  useEffect(() => {
    if (searchedResources && selectedValue === undefined) {
      const initialSelectedValue = searchedResources.find((resource) => resource.id === value);
      setSelectedValue(initialSelectedValue);
    }
  }, [searchedResources, selectedValue, value]);

  const createOption = (name: PostApiProjectsProjectIdTeamsTeamIdResourcesKindMutationRequestSchema['name']) => {
    createResource(
      {
        projectId,
        teamId: project?.currentTeamId!,
        kind,
        data: { name },
      },
      {
        onSuccess: (resource) => {
          selectResource(resource);
          closeOptions();
        },
      }
    );
  };

  const resetSearchState = () => clearSearchTerm();

  const handleOnSearch = (search: string) => {
    if (search.length > 1 || search.length === 0) {
      setSearchTerm(search);
      openOptions();
    }
  };

  const handleKeyboardEvent = (event: React.KeyboardEvent<HTMLElement>) => {
    if (event.key === 'Enter' && !optionsOpen) {
      openOptions();
    } else if (event.key === 'Enter' && hasZeroResults && searchTerm) {
      createOption(searchTerm);
    } else if (['Escape', 'Tab'].includes(event.key)) {
      resetSearchState();
      closeOptions();
    }
  };

  const selectResource = (resource: ResourceSchema | null) => {
    setSelectedValue(resource);
    onChange(resource ? resource.id : null);
    resetSearchState();
    closeOptions();
  };

  const renderResults = () => {
    if (isLoading) {
      return <SearchLoading />;
    }

    if (hasZeroResults) {
      return (
        <>
          <Search.Option disabled value={null} className="bg-transparent! pb-3">
            <div className="w-full truncate">
              <span className="text-neutral-subtlest">{messages('noResults')}</span>
            </div>
          </Search.Option>
          {isSearchMode && <CreateOptionButton optionTerm={searchTerm} createOption={createOption} />}
        </>
      );
    }

    if (!searchedResources) return null;

    return (
      <>
        {searchedResources.map((resource) => (
          <Search.Option key={resource.id} value={resource} className="px-3 py-2">
            <div className="w-full truncate">
              <span className="block truncate font-medium leading-5 text-gray-900">{resource.name}</span>
            </div>
          </Search.Option>
        ))}
        {hasNextPage && (
          <SearchFieldLoadMoreButton
            isLoading={isFetchingNextPage}
            onClick={fetchNextPage}
            listName={messages('organisationSuffix')}
          />
        )}
        {isSearchMode && <CreateOptionButton optionTerm={searchTerm} createOption={createOption} />}
      </>
    );
  };

  const searchFieldError = !selectedValue && !optionsOpen && searchTerm.length > 0 ? messages('error') : undefined;

  return (
    <Search.Root ref={searchInputRef} nullable by="id" value={selectedValue ?? null} onChange={selectResource}>
      <div className="-mt-2">
        <Search.Field
          error={searchFieldError}
          className={cn(
            'group w-full truncate hover:text-gray-800 hover:ring-2 hover:ring-gray-400 focus:ring-2 focus:ring-indigo-500 active:ring-2 active:ring-indigo-500',
            'bg-transparent placeholder-gray-400 focus:bg-white hover:bg-white',
            inputClassName
          )}
          variant="plain"
          placeholder={placeholder ?? messages('placeholder')}
          displayValue={(option: ResourceSchema) => searchTerm || option?.name || defaultDisplayValue}
          selectedValue={selectedValue}
          onChange={handleOnSearch}
          withSearchIcon={false}
          onClick={toggleOptions}
          onKeyDown={handleKeyboardEvent}
        />
      </div>
      {optionsOpen && (
        <div>
          <Search.Options static className="absolute z-popover mt-1.5 min-w-[unset] w-80">
            {renderResults()}
          </Search.Options>
        </div>
      )}
    </Search.Root>
  );
};
