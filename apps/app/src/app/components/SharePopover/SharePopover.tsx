import { useMessageGetter } from '@messageformat/react';
import { Button, PopoverMenu } from '@shape-construction/arch-ui';
import { Square2StackIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { useShare } from 'app/hooks/useShare';
import React, { type ReactElement } from 'react';

type SharePopoverProps = {
  shareData: ShareData;
  title: string;
  content: string;
  children: ReactElement;
};

export const SharePopover: React.FC<SharePopoverProps> = ({ shareData, title, content, children }) => {
  const { canNativelyShare, shareLink, copyToClipboard } = useShare(shareData.url);
  const messages = useMessageGetter('sharePopover');

  return (
    <PopoverMenu.Root>
      <PopoverMenu.Trigger asChild>{children}</PopoverMenu.Trigger>
      <PopoverMenu.Content side="bottom" align="center" sideOffset={10} alignOffset={10}>
        <div className="md:max-w-sm p-3">
          <h5 className="mb-2 text-lg font-medium leading-6 text-gray-900">{title}</h5>
          <p>{content}</p>

          <div className="mt-4 flex items-center space-x-3">
            {canNativelyShare && (
              <Button
                color="primary"
                variant="contained"
                size="md"
                onClick={() => shareLink(shareData.title, shareData.text)}
              >
                {messages('shareCTA')}
              </Button>
            )}
            <Button
              color="secondary"
              variant="outlined"
              size="md"
              leadingIcon={Square2StackIcon}
              onClick={() => copyToClipboard()}
            >
              {messages('copyLinkCTA')}
            </Button>
          </div>
        </div>
      </PopoverMenu.Content>
    </PopoverMenu.Root>
  );
};
