import type { IssueEventTypeSchema } from '@shape-construction/api/src';
import {
  ArrowPathIcon,
  ArrowUturnLeftIcon,
  ArrowsRightLeftIcon,
  ChatBubbleBottomCenterTextIcon,
  ChatBubbleOvalLeftEllipsisIcon,
  ChatPrivateIcon,
  CheckCircleIcon,
  DocumentArrowUpIcon,
  DocumentBlockIcon,
  DocumentPlusIcon,
  ExclamationCircleIcon,
  EyeIcon,
  InboxArrowDownIcon,
  MinusCircleIcon,
  PhotoIcon,
  PhotoPlusIcon,
  UserCircleIcon,
  UserGroupIcon,
  UserMinusIcon,
  UserPlusIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';

import { ISSUE_EVENT_TYPES } from 'app/constants/IssueEventTypes';

type EventIconProps = {
  eventType: IssueEventTypeSchema;
};

export const EventIcon: React.FC<EventIconProps> = ({ eventType }) => {
  const props = {
    className: 'h-5 w-5 text-blue-300',
  };

  const renderIcon = () => {
    switch (eventType) {
      case ISSUE_EVENT_TYPES.ACCEPT_ASSIGNMENT:
        return <CheckCircleIcon {...props} />;
      case ISSUE_EVENT_TYPES.ADD_APPROVER:
        return <UserPlusIcon {...props} />;
      case ISSUE_EVENT_TYPES.ADD_TEAM:
        return <UserGroupIcon {...props} />;
      case ISSUE_EVENT_TYPES.APPROVE:
        return <CheckCircleIcon {...props} />;
      case ISSUE_EVENT_TYPES.ARCHIVE:
        return <InboxArrowDownIcon {...props} />;
      case ISSUE_EVENT_TYPES.ASSIGN:
        return <UserCircleIcon {...props} />;
      case ISSUE_EVENT_TYPES.CHANGE_STATUS:
        return <ArrowsRightLeftIcon {...props} />;
      case ISSUE_EVENT_TYPES.COMMENT_ON:
        return <ChatBubbleOvalLeftEllipsisIcon {...props} />;
      case ISSUE_EVENT_TYPES.CREATE:
        return <DocumentPlusIcon {...props} />;
      case ISSUE_EVENT_TYPES.DELETE_DOCUMENT:
        return <DocumentBlockIcon {...props} />;
      case ISSUE_EVENT_TYPES.DELETE_IMAGE:
        return <DocumentBlockIcon {...props} />;
      case ISSUE_EVENT_TYPES.PRIVATE_COMMENT_ON:
        return <ChatPrivateIcon {...props} />;
      case ISSUE_EVENT_TYPES.REJECT_ASSIGNMENT:
        return <ExclamationCircleIcon {...props} />;
      case ISSUE_EVENT_TYPES.REJECT_RESOLUTION:
        return <ExclamationCircleIcon {...props} />;
      case ISSUE_EVENT_TYPES.REMOVE_APPROVER:
        return <UserMinusIcon {...props} />;
      case ISSUE_EVENT_TYPES.REMOVE_TEAM:
        return <UserMinusIcon {...props} />;
      case ISSUE_EVENT_TYPES.UPDATE:
        return <ArrowPathIcon {...props} />;
      case ISSUE_EVENT_TYPES.UPDATE_IMPACT:
        return <MinusCircleIcon {...props} />;
      case ISSUE_EVENT_TYPES.UPDATE_OBSERVER:
        return <EyeIcon {...props} />;
      case ISSUE_EVENT_TYPES.UPLOAD_DOCUMENT:
        return <DocumentArrowUpIcon {...props} />;
      case ISSUE_EVENT_TYPES.UPLOAD_IMAGE:
        return <PhotoPlusIcon {...props} />;
      case ISSUE_EVENT_TYPES.UPDATE_IMAGE:
        return <PhotoIcon {...props} />;
      case ISSUE_EVENT_TYPES.RESTORE:
        return <InboxArrowDownIcon {...props} />;
      case ISSUE_EVENT_TYPES.REOPEN:
        return <ArrowUturnLeftIcon {...props} />;
      case ISSUE_EVENT_TYPES.UPDATE_STATUS_STATEMENT:
        return <ChatBubbleBottomCenterTextIcon {...props} />;
      case ISSUE_EVENT_TYPES.DELETE_STATUS_STATEMENT:
        return <ChatBubbleBottomCenterTextIcon {...props} />;

      default:
        return null;
    }
  };

  return renderIcon();
};
