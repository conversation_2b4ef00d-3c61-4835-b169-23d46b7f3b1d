import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import type { User } from '@shape-construction/arch-ui/src/types/User';
import React from 'react';

type CommentCardProps = {
  index?: number;
  opacity?: number;
  background?: string;
  transition?: string;
  userDetails: User;
  comment: string;
  date?: string;
  loading?: React.ReactNode;
};

export const CommentCard: React.FC<CommentCardProps> = ({
  index,
  opacity,
  background,
  transition,
  userDetails,
  comment,
  date,
  loading,
}) => {
  // derive the HTML identifier from the provided comment card index
  const htmlId = `res-comment-${index}-card`;
  return (
    <div
      data-testid={htmlId}
      data-card-type="comment-card"
      data-card-index={index}
      className="bg-[#f5f5f5] text-left rounded-sm relative pl-14 pr-6 py-5 mb-[15px] last:mb-0"
      style={{
        opacity,
        backgroundColor: background,
        transition,
      }}
    >
      <div className="absolute top-[15px] left-[15px]">
        <UserAvatar user={userDetails} size="md" />
      </div>
      <div>
        <p data-card-attr="comment-author" data-card-index={index} className="mr-2 inline">
          {userDetails.name}
        </p>
        <p className="inline font-thin text-neutral-subtle">{date}</p>
      </div>
      <p data-card-attr="comment-body" data-card-index={index} className="mt-3 pr-3 break-words whitespace-pre-line">
        {comment}
      </p>
      <div className="absolute top-[27.5%] right-[45%] z-50 opacity-100">{loading}</div>
    </div>
  );
};
