import { useMessageGetter } from '@messageformat/react';
import {
  IssueTrackerIcon,
  ProjectGalleryIcon,
  ShiftReportsIcon,
  WeeklyPlannerIcon,
} from '@shape-construction/arch-ui/src/Icons/product-logo';
import BottomNavigation from '@shape-construction/arch-ui/src/Navigation/BottomNavigation';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { QuickNewButton } from 'app/components/QuickNewButton/QuickNewButton';
import { useLayoutContext } from 'app/contexts/layout/layoutContext';
import React, { useMemo } from 'react';
import { matchPath, useLocation, useMatch, useNavigate } from 'react-router';

type BottomNavigationItemType = {
  icon: React.ElementType;
  title: string;
  route: string;
  isActiveOn: string;
};

type AppBottomNavigationItemProps = { item: BottomNavigationItemType };
export const AppBottomNavigationItem = ({ item }: AppBottomNavigationItemProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const matchRoute = matchPath(item.isActiveOn ?? item.route, location.pathname);
  const isActive = !!matchRoute;

  return (
    <BottomNavigation.Item
      aria-current={isActive ? 'page' : undefined}
      active={isActive}
      onClick={() => navigate(item.route)}
    >
      <BottomNavigation.Icon icon={item.icon} className="[&>svg]:size-6" />
      {item.title}
    </BottomNavigation.Item>
  );
};

export const AppBottomNavigation = () => {
  const messages = useMessageGetter('navigation');

  const isProjectRoute = useMatch({ path: '/projects/:projectId', end: false });
  const projectId = isProjectRoute?.params?.projectId;

  const { search } = useLocation();

  const {
    layoutConfig: { showBottomNavigation },
  } = useLayoutContext();

  const isLargeScreen = useMediaQuery(breakpoints.up('md'));
  const isQuickNewButtonVisible = projectId && showBottomNavigation && !isLargeScreen;

  const items: BottomNavigationItemType[] = useMemo(
    () => [
      {
        icon: IssueTrackerIcon,
        title: messages('issues'),
        route: search ? `/projects/${projectId}/filter${search}` : `/projects/${projectId}/issues`,
        isActiveOn: '/projects/:projectId/issues/*',
      },
      {
        icon: WeeklyPlannerIcon,
        title: messages('weeklyPlanner'),
        route: `/projects/${projectId}/weekly-planner/plans`,
        isActiveOn: '/projects/:projectId/weekly-planner/*',
      },
      {
        icon: ShiftReportsIcon,
        title: messages('shiftReports'),
        route: `/projects/${projectId}/shift-reports/drafts`,
        isActiveOn: '/projects/:projectId/shift-reports/*',
      },
      {
        icon: ProjectGalleryIcon,
        title: messages('gallery'),
        route: `/projects/${projectId}/gallery`,
        isActiveOn: '/projects/:projectId/gallery/*',
      },
    ],
    [projectId, search, messages]
  );

  if (!isProjectRoute || !showBottomNavigation) return null;
  return (
    <BottomNavigation.Root aria-label="bottom navigation">
      {items.slice(0, 2).map((item) => (
        <AppBottomNavigationItem key={item.route} item={item} />
      ))}
      {isQuickNewButtonVisible && (
        <BottomNavigation.Item>
          <QuickNewButton projectId={projectId} />
        </BottomNavigation.Item>
      )}
      {items.slice(2).map((item) => (
        <AppBottomNavigationItem key={item.route} item={item} />
      ))}
    </BottomNavigation.Root>
  );
};
