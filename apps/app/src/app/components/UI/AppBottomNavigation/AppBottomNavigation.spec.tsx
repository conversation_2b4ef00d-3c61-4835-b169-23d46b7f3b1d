import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { mediaQueryOptions } from '@shape-construction/arch-ui/src/utils/breakpoints';
import type { LayoutContextProviderProps } from 'app/contexts/layout/layoutContext';
import { createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { createMatchMedia, render, screen, userEvent, within } from 'tests/test-utils';
import { AppBottomNavigation } from './AppBottomNavigation';

function setupTest(context = {}) {
  return render(<AppBottomNavigation />, {
    layoutConfig: { showBottomNavigation: true },
    ...context,
  });
}

describe('<AppBottomNavigation />', () => {
  beforeAll(() => {
    window.matchMedia = createMatchMedia(mediaQueryOptions.sm);
  });

  describe('when inside project route', () => {
    describe('when layout provider has showBottomNavigation enabled', () => {
      it('renders bottom navigation', async () => {
        const route = { path: '/projects/:projectId/issues' };
        const history = createMemoryHistory({ initialEntries: ['/projects/project-one/issues'] });
        setupTest({ history, route });

        const navigation = screen.getByRole('navigation', { name: 'bottom navigation' });
        expect(navigation).toBeInTheDocument();
        expect(within(navigation).getByRole('button', { name: 'navigation.issues' })).toBeInTheDocument();
        expect(within(navigation).getByRole('button', { name: 'navigation.weeklyPlanner' })).toBeInTheDocument();
        expect(within(navigation).getByRole('button', { name: 'navigation.shiftReports' })).toBeInTheDocument();
        expect(within(navigation).getByRole('button', { name: 'navigation.gallery' })).toBeInTheDocument();
        expect(
          await within(navigation).findByRole('button', { name: 'quickNewButton.trigger.title' })
        ).toBeInTheDocument();
      });

      it('shows issues as active when landing on the issues page', () => {
        const route = { path: '/projects/:projectId/issues' };
        const history = createMemoryHistory({ initialEntries: ['/projects/project-one/issues'] });
        setupTest({ history, route });

        expect(screen.getByRole('button', { name: 'navigation.issues', current: 'page' })).toBeInTheDocument();
      });

      it('shows weekly planner as active when landing on the weekly planner page', () => {
        const route = { path: '/projects/:projectId/weekly-planner/plans' };
        const history = createMemoryHistory({ initialEntries: ['/projects/project-one/weekly-planner/plans'] });
        setupTest({ history, route });

        expect(screen.getByRole('button', { name: 'navigation.weeklyPlanner', current: 'page' })).toBeInTheDocument();
      });

      it('shows issues as active when clicking on issues', async () => {
        const route = { path: '/projects/:projectId/timeline' };
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-one/timeline'],
        });
        setupTest({ history, route });

        await userEvent.click(screen.getByRole('button', { name: 'navigation.issues' }));

        expect(history.location.pathname).toBe('/projects/project-one/issues');
      });

      it('shows shift reports as active when landing on the shift reports page', async () => {
        const route = { path: '/projects/:projectId/shift-reports/drafts' };
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-one/shift-reports/drafts'],
        });
        setupTest({ history, route });

        await userEvent.click(screen.getByRole('button', { name: 'navigation.shiftReports' }));

        expect(screen.getByRole('button', { name: 'navigation.shiftReports', current: 'page' })).toBeInTheDocument();
        expect(history.location.pathname).toBe('/projects/project-one/shift-reports/drafts');
      });

      it('shows gallery as active when landing on the gallery page', async () => {
        const route = { path: '/projects/:projectId/gallery' };
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-one/gallery'],
        });
        setupTest({ history, route });

        await userEvent.click(screen.getByRole('button', { name: 'navigation.gallery' }));

        expect(screen.getByRole('button', { name: 'navigation.gallery', current: 'page' })).toBeInTheDocument();
        expect(history.location.pathname).toBe('/projects/project-one/gallery');
      });

      it('shows weekly planner as active when clicking on weekly planner', async () => {
        const route = { path: '/projects/:projectId/issues' };
        const history = createMemoryHistory({ initialEntries: ['/projects/project-one/issues'] });
        setupTest({ history, route });

        await userEvent.click(screen.getByRole('button', { name: 'navigation.weeklyPlanner' }));

        expect(history.location.pathname).toBe('/projects/project-one/weekly-planner/plans');
      });

      it('renders issue with filter url when filters are active', async () => {
        const route = { path: '/projects/:projectId/timeline' };
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-one/timeline?filter_state[]=assigned'],
        });
        setupTest({ history, route });

        await userEvent.click(screen.getByRole('button', { name: 'navigation.issues' }));

        expect(history.location).toEqual(
          expect.objectContaining({
            pathname: '/projects/project-one/filter',
            search: '?filter_state[]=assigned',
          })
        );
      });

      describe('when user clicks a quick new button', () => {
        it('opens the drawer', async () => {
          const project = projectFactory({
            id: 'project-one',
            availableActions: projectAvailableActions({ createIssue: true }),
          });
          const route = { path: '/projects/:projectId/issues' };
          const history = createMemoryHistory({ initialEntries: ['/projects/project-one/issues'] });
          server.use(getApiProjectsProjectIdMockHandler(() => project));
          setupTest({ history, route });

          const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
          expect(trigger).toBeInTheDocument();

          await userEvent.click(trigger);

          expect(await screen.findByText('quickNewButton.issueTracker.heading')).toBeInTheDocument();
        });
      });
    });

    describe('when layout provider has showBottomNavigation not enabled', () => {
      it('does not render bottom navigation', () => {
        const route = { path: '/projects/:projectId/issues' };
        const history = createMemoryHistory({ initialEntries: ['/projects/project-one/issues'] });
        const layoutConfig = {
          showBottomNavigation: false,
        } as LayoutContextProviderProps['layoutConfig'];
        setupTest({ history, route, layoutConfig });

        expect(screen.queryByRole('navigation', { name: 'bottom navigation' })).not.toBeInTheDocument();
      });
    });
  });

  describe('when outside project route', () => {
    it('does not render bottom navigation', () => {
      const route = { path: '/notifications' };
      const history = createMemoryHistory({ initialEntries: ['/notifications'] });
      setupTest({ history, route });

      expect(screen.queryByRole('navigation', { name: 'bottom navigation' })).not.toBeInTheDocument();
    });
  });
});
