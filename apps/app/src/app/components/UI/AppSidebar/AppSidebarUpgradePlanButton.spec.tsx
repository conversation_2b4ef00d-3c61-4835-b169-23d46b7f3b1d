import { projectFactory } from '@shape-construction/api/factories/projects';
import {
  subscriptionPlanAvailableActionsFactory,
  subscriptionPlanFactory,
  subscriptionPlanFeaturesFactory,
} from '@shape-construction/api/factories/subscriptionPlans';
import { teamFactory } from '@shape-construction/api/factories/teams';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import {
  getApiProjectsProjectIdTeamsTeamIdMockHandler,
  getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler,
} from '@shape-construction/api/handlers-factories/projects/teams';
import Sidebar from '@shape-construction/arch-ui/src/Navigation/Sidebar';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { render, screen, waitFor } from 'tests/test-utils';
import AppSidebar from '.';

const renderComponent = (props = {}, context = {}) =>
  render(
    <Sidebar.Provider {...props}>
      <AppSidebar.UpgradePlanButton />
    </Sidebar.Provider>,
    { ...context }
  );

describe('<AppSidebar.UpgradePlanButton />', () => {
  describe('when the user can edit the subscription plan', () => {
    describe('and the active subscription is free', () => {
      it('renders the CTA correctly', async () => {
        const teamsSubscriptionPlan = subscriptionPlanFactory({
          activePlanName: 'Free',
          activePlanSlug: 'free',
          availableActions: subscriptionPlanAvailableActionsFactory({ edit: true }),
          subscribedPlanName: 'Free',
          subscribedPlanSlug: 'free',
          features: subscriptionPlanFeaturesFactory({
            usersPerTeam: { current: 13, maximum: 24 },
          }),
        });
        const team = teamFactory();
        server.use(getApiProjectsProjectIdTeamsTeamIdMockHandler(() => team));
        server.use(getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler(() => teamsSubscriptionPlan));
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0'],
        });
        const route = { path: '/projects/:projectId' };
        renderComponent({}, { history, route });

        expect(
          await screen.findByRole('button', { name: 'appSidebar.upgradePlanButton.free.title' })
        ).toBeInTheDocument();

        expect(screen.getByText('appSidebar.upgradePlanButton.free.title')).toBeInTheDocument();
        expect(screen.getByText('appSidebar.upgradePlanButton.free.description')).toBeInTheDocument();
      });
    });

    describe('and the active subscription is trial', () => {
      it('renders the CTA correctly', async () => {
        const teamsSubscriptionPlan = subscriptionPlanFactory({
          activePlanName: 'Trial',
          activePlanSlug: 'trial',
          availableActions: subscriptionPlanAvailableActionsFactory({ edit: true }),
          subscribedPlanName: 'Free',
          subscribedPlanSlug: 'free',
          features: subscriptionPlanFeaturesFactory({
            usersPerTeam: { current: 13, maximum: 24 },
          }),
        });
        const team = teamFactory();
        server.use(getApiProjectsProjectIdTeamsTeamIdMockHandler(() => team));
        server.use(getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler(() => teamsSubscriptionPlan));
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0'],
        });
        const route = { path: '/projects/:projectId' };
        renderComponent({}, { history, route });

        expect(
          await screen.findByRole('button', { name: 'appSidebar.upgradePlanButton.trial.title' })
        ).toBeInTheDocument();

        expect(screen.getByText('appSidebar.upgradePlanButton.trial.title')).toBeInTheDocument();
        expect(screen.getByText('appSidebar.upgradePlanButton.trial.description')).toBeInTheDocument();
      });
    });

    describe('and click the CTA', () => {
      it('redirects to the respective team subscription page', async () => {
        const teamsSubscriptionPlan = subscriptionPlanFactory({
          activePlanName: 'Free',
          activePlanSlug: 'free',
          availableActions: subscriptionPlanAvailableActionsFactory({ edit: true }),
          subscribedPlanName: 'Free',
          subscribedPlanSlug: 'free',
          features: subscriptionPlanFeaturesFactory({
            usersPerTeam: { current: 13, maximum: 24 },
          }),
        });
        const project = projectFactory({ currentTeamId: 'team-999' });
        server.use(getApiProjectsProjectIdMockHandler(() => project));
        server.use(getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler(() => teamsSubscriptionPlan));
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0'],
        });
        const route = { path: '/projects/:projectId' };
        const { user } = renderComponent({}, { history, route });

        await user.click(await screen.findByRole('button', { name: 'appSidebar.upgradePlanButton.free.title' }));

        expect(history.location.pathname).toEqual('/projects/project-0/settings/teams/team-999/subscription');
      });
    });

    describe('and the active subscription is pro', () => {
      it('does not render the CTA', async () => {
        const teamsSubscriptionPlan = subscriptionPlanFactory({
          activePlanName: 'Pro',
          activePlanSlug: 'pro',
          availableActions: subscriptionPlanAvailableActionsFactory({
            accessBillingPortal: true,
            billingPortal: true,
            edit: true,
          }),
          subscribedPlanName: 'Pro',
          subscribedPlanSlug: 'pro',
          features: subscriptionPlanFeaturesFactory({
            usersPerTeam: { current: 13, maximum: 24 },
          }),
        });
        const team = teamFactory();
        server.use(getApiProjectsProjectIdTeamsTeamIdMockHandler(() => team));
        server.use(getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler(() => teamsSubscriptionPlan));
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0'],
        });
        const route = { path: '/projects/:projectId' };
        renderComponent({}, { history, route });

        await waitFor(() =>
          expect(
            screen.queryByRole('button', { name: 'appSidebar.upgradePlanButton.free.title' })
          ).not.toBeInTheDocument()
        );
      });
    });
  });

  describe('when the user cannot edit the subscription plan', () => {
    it('does not render the CTA', async () => {
      const teamsSubscriptionPlan = subscriptionPlanFactory({
        activePlanName: 'Free',
        activePlanSlug: 'free',
        availableActions: subscriptionPlanAvailableActionsFactory({ edit: false }),
        subscribedPlanName: 'Free',
        subscribedPlanSlug: 'free',
        features: subscriptionPlanFeaturesFactory({
          usersPerTeam: { current: 13, maximum: 24 },
        }),
      });
      const team = teamFactory();
      server.use(getApiProjectsProjectIdTeamsTeamIdMockHandler(() => team));
      server.use(getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler(() => teamsSubscriptionPlan));
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0'],
      });
      const route = { path: '/projects/:projectId' };
      renderComponent({}, { history, route });

      await waitFor(() =>
        expect(
          screen.queryByRole('button', { name: 'appSidebar.upgradePlanButton.free.title' })
        ).not.toBeInTheDocument()
      );
    });
  });

  describe('when sidebar is not expanded', () => {
    it('does not render title and description', async () => {
      const teamsSubscriptionPlan = subscriptionPlanFactory({
        activePlanName: 'Free',
        activePlanSlug: 'free',
        availableActions: subscriptionPlanAvailableActionsFactory({ edit: true }),
        subscribedPlanName: 'Free',
        subscribedPlanSlug: 'free',
        features: subscriptionPlanFeaturesFactory({
          usersPerTeam: { current: 13, maximum: 24 },
        }),
      });
      const team = teamFactory();
      server.use(getApiProjectsProjectIdTeamsTeamIdMockHandler(() => team));
      server.use(getApiProjectsProjectIdTeamsTeamIdSubscriptionPlanMockHandler(() => teamsSubscriptionPlan));
      const history = createMemoryHistory({ initialEntries: ['/projects/project-0'] });
      const route = { path: '/projects/:projectId' };
      renderComponent({ open: false }, { history, route });

      expect(
        await screen.findByRole('button', { name: 'appSidebar.upgradePlanButton.free.title' })
      ).toBeInTheDocument();

      expect(screen.queryByText('appSidebar.upgradePlanButton.free.title')).not.toBeInTheDocument();
      expect(screen.queryByText('appSidebar.upgradePlanButton.free.description')).not.toBeInTheDocument();
    });
  });
});
