import { useMessageGetter } from '@messageformat/react';
import { Divider, IconButton, VisuallyHidden } from '@shape-construction/arch-ui';
import { ChartBarSquareIcon, Cog6ToothIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import {
  ChevronDoubleLeftIcon,
  ChevronDoubleRightIcon,
  ChevronRightIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import Sidebar, { useSidebar } from '@shape-construction/arch-ui/src/Navigation/Sidebar';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useMediaQuery, useModal } from '@shape-construction/hooks';
import { useQuery } from '@tanstack/react-query';
import { ProjectsSelector } from 'app/components/ProjectsSelector/ProjectsSelector';
import { FeatureLimits } from 'app/components/SubscriptionPlanFeatures/FeatureLimits/FeatureLimits';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import { useCurrentUser } from 'app/queries/users/users';
import React, { useEffect } from 'react';
import { Link, matchPath, useLocation, useMatch, useNavigate } from 'react-router';
import { ADMIN_OR_ABOVE } from '../../../constants/Roles';
import { BackNavigationButton } from '../Navbar/components/BackNavigationButton';
import { ForwardNavigationButton } from '../Navbar/components/ForwardNavigationButton';
import { ReloadButton } from '../Navbar/components/ReloadButton';
import ShapeLogo from '../ShapeLogo/ShapeLogo';
import { AppSidebarQRButton } from './AppSidebarQRButton';
import { AppSidebarUpgradePlanButton } from './AppSidebarUpgradePlanButton';
import QRScanDetails from './QRScanDetailsModal';
import {
  type SidebarItemType,
  useAdminItems,
  useDataBookItems,
  useHighlightItems,
  useOverviewItems,
} from './useSidebarItems';

export const AppSidebarCollapseButton = () => {
  const { isLargeScreen, open, toggleSidebar } = useSidebar();
  const isExpanded = isLargeScreen && open;

  return (
    <div className="hidden md:flex md:justify-end">
      <IconButton
        icon={isExpanded ? ChevronDoubleLeftIcon : ChevronDoubleRightIcon}
        size="md"
        shape="square"
        aria-label="sidebar control"
        aria-expanded={isExpanded}
        color="secondary"
        variant="text"
        onClick={() => toggleSidebar()}
      />
    </div>
  );
};

export const AppSidebarMobileHeader: React.FC = () => {
  const projectRoute = useMatch('/projects/:projectId/*');
  const notificationRoute = useMatch('/notifications/projects/:projectId');
  const match = projectRoute || notificationRoute;
  const { defaultProject } = useCurrentUser();

  const currentProjectId = match?.params?.projectId ?? defaultProject;

  return (
    <div className="section-header my-2 md:hidden">
      <div className="flex h-16 w-full items-center px-6 py-5 justify-between">
        <div className="h-full shrink-0">
          <Link to="/">
            <ShapeLogo withLettering />
          </Link>
        </div>
        <div className="flex items-center">
          {currentProjectId && (
            <>
              <BackNavigationButton color="primary" />
              <ForwardNavigationButton color="primary" />
            </>
          )}
          <ReloadButton color="primary" />
        </div>
      </div>
      <Divider orientation="horizontal" />
    </div>
  );
};

type AppSidebarItemProps = { item: SidebarItemType };
export const AppSidebarItem = ({ item }: AppSidebarItemProps) => {
  const location = useLocation();
  const navigate = useNavigate();

  const { isLargeScreen, toggleSidebar } = useSidebar();

  const notifications = item.notifications;
  const activateBadge = Boolean(isLargeScreen && notifications && notifications > 0);
  const badgeText = activateBadge ? notifications?.toString() : undefined;

  const handleRedirect = (route: string) => {
    navigate(route);
    if (!isLargeScreen) toggleSidebar(false);
  };

  const SidebarItem = ({ item, onClick }: { item: SidebarItemType; onClick: () => void }) => (
    <Sidebar.Item
      active={!!matchPath(item.isActiveOn ?? item.route, location.pathname)}
      disabled={item.disabled}
      tooltip={item.hideTooltip ? null : item.title}
      onClick={onClick}
      aria-current="page"
    >
      <Sidebar.Icon icon={item.image} color={item.color} variant={item.variant} />
      <VisuallyHidden.Root>{`${item.title}-icon`}</VisuallyHidden.Root>
      {item.title}
      {badgeText && <Sidebar.Badge label={badgeText} />}
    </Sidebar.Item>
  );

  return (
    <li data-sidebar="item" className="list-none relative">
      <SidebarItem item={item} onClick={() => handleRedirect(item.route)} />

      {(item?.children ?? []).length > 0 && (
        <Sidebar.Section className="pl-3" divider>
          {item.children?.map((child) =>
            child.subscriptionPlanFeature ? (
              <FeatureLimits key={child.route} featureName={child.subscriptionPlanFeature!}>
                {({ featureEnabled }) => (
                  <SidebarItem item={child} onClick={() => featureEnabled && handleRedirect(child.route)} />
                )}
              </FeatureLimits>
            ) : (
              <SidebarItem key={child.route} item={child} onClick={() => handleRedirect(child.route)} />
            )
          )}
        </Sidebar.Section>
      )}
    </li>
  );
};

export const AppSidebarRoot: React.FC = () => {
  const navigate = useNavigate();
  const projectRoute = useMatch('/projects/:projectId/*');
  const adminRoute = useMatch('/projects/:projectId/settings/*');
  const dataBookRoute = useMatch('/projects/:projectId/databook/*');
  const notificationRoute = useMatch('/notifications/projects/:projectId');
  const match = projectRoute || notificationRoute;
  const projectId = match?.params.projectId;
  const { open: qrModalOpen, openModal, closeModal } = useModal(false);

  const { value: isDataBookFlagEnabled } = useFeatureFlag('data-book');

  const isAdminRoute = Boolean(adminRoute);
  const isDataBookRoute = Boolean(dataBookRoute);

  const { data: currentProject } = useQuery(getProjectQueryOptions(projectId!));
  const currentUser = useCurrentUser();

  const highlightItems = useHighlightItems(currentProject);
  const overviewItems = useOverviewItems(currentProject);
  const adminItems = useAdminItems(currentProject);
  const dataBookItems = useDataBookItems(currentProject);

  const messages = useMessageGetter('appSidebar');

  const { isLargeScreen, open, toggleSidebar } = useSidebar();

  const isWideScreen = useMediaQuery(breakpoints.up('lg'));

  useEffect(() => {
    if (isLargeScreen && !isWideScreen) toggleSidebar(false);
  }, [isLargeScreen, isWideScreen]);

  const renderHeader = () => {
    return (
      <>
        <div className="hidden px-2 md:block">
          <ProjectsSelector currentProject={currentProject} isExpanded={open} />
        </div>
        <AppSidebarMobileHeader />
      </>
    );
  };

  const renderDataBookItems = () => {
    if (!currentProject) return null;

    return (
      <Sidebar.Submenu.Root defaultOpen={isDataBookRoute}>
        <Sidebar.Submenu.Trigger>
          <Sidebar.Item
            active={isDataBookRoute}
            tooltip={messages('dataBook')}
            onClick={() => navigate(`/projects/${projectId}/data-book`)}
          >
            <Sidebar.Icon icon={ChartBarSquareIcon} color="purple" variant="contained" />
            {messages('dataBook')}
            <ChevronRightIcon className="w-5 h-5 ml-auto text-icon-neutral-subtle" />
          </Sidebar.Item>
        </Sidebar.Submenu.Trigger>
        <Sidebar.Submenu.Content title={messages('dataBook')}>
          <Sidebar.Section>
            {dataBookItems.map((item) => (
              <AppSidebarItem key={item.key} item={item} />
            ))}
          </Sidebar.Section>
        </Sidebar.Submenu.Content>
      </Sidebar.Submenu.Root>
    );
  };

  const renderHighlightItems = () => {
    return (
      <Sidebar.Section divider data-testid="app-sidebar-highlight-section">
        {highlightItems.map((item) => (
          <AppSidebarItem key={item.key} item={item} />
        ))}
        {isDataBookFlagEnabled && renderDataBookItems()}
      </Sidebar.Section>
    );
  };

  const renderOverviewItems = () => {
    return (
      <Sidebar.Section divider data-testid="app-sidebar-overview-section">
        {overviewItems.map((item) => (
          <AppSidebarItem key={item.key} item={item} />
        ))}
      </Sidebar.Section>
    );
  };

  const renderAdminItems = () => {
    if (!currentProject) return null;
    if (!ADMIN_OR_ABOVE.includes(currentProject.currentTeamMemberRole)) return null;

    return (
      <Sidebar.Submenu.Root defaultOpen={isAdminRoute}>
        <Sidebar.Submenu.Trigger>
          <Sidebar.Item active={isAdminRoute} tooltip={messages('admin')}>
            <Sidebar.Icon icon={Cog6ToothIcon} />
            {messages('admin')}
            <ChevronRightIcon className="w-5 h-5 ml-auto text-icon-neutral-subtle" />
          </Sidebar.Item>
        </Sidebar.Submenu.Trigger>
        <Sidebar.Submenu.Content title={messages('admin')}>
          <Sidebar.Section>
            {adminItems.map((item) => (
              <AppSidebarItem key={item.key} item={item} />
            ))}
          </Sidebar.Section>
        </Sidebar.Submenu.Content>
      </Sidebar.Submenu.Root>
    );
  };

  if (!currentUser) return null;
  return (
    <>
      <QRScanDetails open={qrModalOpen} onClose={closeModal} />
      <Sidebar.Root data-testid="app-sidebar">
        <Sidebar.Header data-testid="app-sidebar-header">{renderHeader()}</Sidebar.Header>
        <Sidebar.Content>
          {renderHighlightItems()}
          {renderOverviewItems()}
          {renderAdminItems()}
        </Sidebar.Content>
        <Sidebar.Footer data-testid="app-sidebar-footer" className="p-4">
          <AppSidebarQRButton onClick={openModal} />
          <AppSidebarUpgradePlanButton />
          <AppSidebarCollapseButton />
        </Sidebar.Footer>
      </Sidebar.Root>
    </>
  );
};
