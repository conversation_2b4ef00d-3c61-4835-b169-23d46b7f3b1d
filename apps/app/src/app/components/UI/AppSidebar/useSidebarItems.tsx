import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { Badge } from '@shape-construction/arch-ui';
import { SHAPE, SIZE } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import {
  ActivitiesIcon,
  AdjustmentsHorizontalIcon,
  ChartBarSquareIcon,
  LockOpenIcon,
  MapPinIcon,
  MonitoringIcon,
  NewspaperIcon,
  SquaresPlusIcon,
  TableEditIcon,
  UsersIcon,
  WrenchIcon,
} from '@shape-construction/arch-ui/src/Icons/outline';
import {
  ChannelsIcon,
  IssueTrackerIcon,
  ProjectGalleryIcon,
  ShiftReportsIcon,
  WeeklyPlannerIcon,
} from '@shape-construction/arch-ui/src/Icons/product-logo';
import { MinusIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import type { IconColor, IconVariant } from '@shape-construction/arch-ui/src/Navigation/icon-config';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useProjectChannelsCount } from 'app/channels/queries/useProjectChannels';
import {
  type FeatureName,
  useFeatureLimit,
} from 'app/components/SubscriptionPlanFeatures/FeatureLimits/useFeatureLimit';
import { useGroupedDashboards } from 'app/pages/projects/[projectId]/data-book/hooks/useGroupedDashboards';
import React from 'react';

export type SidebarItemType = {
  key: string;
  title: string | JSX.Element;
  image: React.ElementType;
  route: string;
  isActiveOn?: string;
  notifications?: number;
  color?: IconColor;
  variant?: IconVariant;
  disabled?: boolean;
  hidden?: boolean;
  hideTooltip?: boolean;
  children?: SidebarItemType[];
  subscriptionPlanFeature?: FeatureName;
};

export const useHighlightItems = (currentProject?: ProjectSchema): SidebarItemType[] => {
  const labels = useMessageGetter('navigation');
  const isDisabled = !currentProject || currentProject?.currentTeamMemberStatus !== 'joined';

  const notificationsCount = useProjectChannelsCount(currentProject?.id);
  const { value: isControlCenterEnabled } = useFeatureFlag('control-center');

  const appsMenu: SidebarItemType[] = [
    {
      key: 'issues',
      title: labels('issues'),
      image: IssueTrackerIcon,
      route: `/projects/${currentProject?.id}/issues`,
      isActiveOn: '/projects/:projectId/issues/*',
      disabled: isDisabled,
    },
    {
      key: 'weekly_planner',
      title: labels('weeklyPlanner'),
      image: WeeklyPlannerIcon,
      route: `/projects/${currentProject?.id}/weekly-planner/plans`,
      isActiveOn: '/projects/:projectId/weekly-planner/*',
      disabled: isDisabled,
    },
    {
      key: 'shift_reports',
      title: labels('shiftReports'),
      image: ShiftReportsIcon,
      route: `/projects/${currentProject?.id}/shift-reports/drafts`,
      isActiveOn: '/projects/:projectId/shift-reports/*',
      disabled: isDisabled,
    },
    {
      key: 'gallery',
      title: labels('gallery'),
      image: ProjectGalleryIcon,
      route: `/projects/${currentProject?.id}/gallery`,
      isActiveOn: '/projects/:projectId/gallery/*',
      disabled: isDisabled,
    },
    {
      key: 'channels',
      title: labels('channels'),
      image: ChannelsIcon,
      route: `/projects/${currentProject?.id}/channels`,
      isActiveOn: '/projects/:projectId/channels/*',
      disabled: isDisabled,
      notifications: notificationsCount,
    },
  ];

  if (isControlCenterEnabled) {
    appsMenu.push({
      key: 'control-center',
      title: labels('changeTracker'),
      image: MonitoringIcon,
      route: `/projects/${currentProject?.id}/control-center`,
      isActiveOn: '/projects/:projectId/control-center/*',
      color: 'orange',
      variant: 'contained',
      disabled: isDisabled,
    });
  }

  return appsMenu;
};

export const useOverviewItems = (currentProject?: ProjectSchema): SidebarItemType[] => {
  const labels = useMessageGetter('navigation');
  const isDisabled = !currentProject || currentProject?.currentTeamMemberStatus !== 'joined';
  const { value: isDataBookFlagEnabled } = useFeatureFlag('data-book');

  const items: Array<SidebarItemType> = [
    {
      key: 'timeline',
      title: labels('timeline'),
      image: NewspaperIcon,
      route: `/projects/${currentProject?.id}/timeline`,
      isActiveOn: '/projects/:projectId/timeline/*',
      disabled: isDisabled,
    },
    {
      key: 'activities',
      title: labels('activities'),
      image: ActivitiesIcon,
      route: `/projects/${currentProject?.id}/activities`,
      isActiveOn: '/projects/:projectId/activities/*',
      disabled: isDisabled,
    },
    {
      key: 'dashboards',
      title: labels('dashboard'),
      image: ChartBarSquareIcon,
      route: `/projects/${currentProject?.id}/dashboards`,
      isActiveOn: '/projects/:projectId/dashboards/*',
      disabled: isDisabled,
      hidden: isDataBookFlagEnabled,
    },
  ];
  return items.filter((s: SidebarItemType) => !s.hidden);
};

export const useAdminItems = (currentProject?: ProjectSchema): SidebarItemType[] => {
  const items = [];
  const labels = useMessageGetter('admin.menu');

  if (currentProject?.availableActions?.manageLocations) {
    items.push({
      key: 'locations',
      title: labels('locations'),
      image: MapPinIcon,
      route: `/projects/${currentProject.id}/settings/locations`,
      isActiveOn: '/projects/:projectId/settings/locations/*',
      hideTooltip: true,
    });
  }

  if (currentProject?.availableActions?.manageDisciplines) {
    items.push({
      key: 'disciplines',
      title: labels('disciplines'),
      image: WrenchIcon,
      route: `/projects/${currentProject.id}/settings/disciplines`,
      isActiveOn: '/projects/:projectId/settings/disciplines/*',
      hideTooltip: true,
    });
  }

  items.push(
    {
      key: 'teams',
      title: labels('teams'),
      image: UsersIcon,
      route: `/projects/${currentProject?.id}/settings/teams`,
      isActiveOn: '/projects/:projectId/settings/teams/*',
      hideTooltip: true,
    },
    {
      key: 'access_requests',
      title: labels('accessRequests'),
      image: LockOpenIcon,
      route: `/projects/${currentProject?.id}/settings/access-requests`,
      isActiveOn: '/projects/:projectId/settings/access-requests/*',
      hideTooltip: true,
    },
    {
      key: 'custom_fields',
      title: labels('customFields'),
      image: TableEditIcon,
      route: `/projects/${currentProject?.id}/settings/custom-fields`,
      isActiveOn: '/projects/:projectId/settings/custom-fields/*',
      hideTooltip: true,
    },
    {
      key: 'resources',
      title: labels('resources'),
      image: SquaresPlusIcon,
      route: `/projects/${currentProject?.id}/settings/resources`,
      isActiveOn: '/projects/:projectId/settings/resources/*',
      hideTooltip: true,
    },
    {
      key: 'project',
      title: labels('projectSettings'),
      image: AdjustmentsHorizontalIcon,
      route: `/projects/${currentProject?.id}/settings/project`,
      isActiveOn: '/projects/:projectId/settings/project/*',
      hideTooltip: true,
    }
  );

  return items;
};

export const useDataBookItems = (currentProject?: ProjectSchema): SidebarItemType[] => {
  const items: Array<SidebarItemType> = [];
  const labels = useMessageGetter('dataBook.menu');
  const messages = useMessageGetter('dataBook.page.customDashboard');
  const dataBookRoute = `/projects/${currentProject?.id}/data-book`;
  const dataBookRoutePattern = '/projects/:projectId/data-book';
  const { data: groupedDashboards } = useGroupedDashboards(currentProject?.id!);
  const { value: isDataBookProFlagEnabled } = useFeatureFlag('data-book-pro');
  const { featureEnabled: canViewCustomDashboards } = useFeatureLimit('customDashboards');

  items.push(
    {
      key: 'home',
      title: labels('home'),
      image: ChartBarSquareIcon,
      route: dataBookRoute,
      variant: 'text',
      hideTooltip: true,
    },
    {
      key: 'shiftManager',
      title: labels('shiftManager.title'),
      image: ShiftReportsIcon,
      route: `${dataBookRoute}/shift-manager`,
      hideTooltip: true,
      children: groupedDashboards.shift_manager.map((dashboard) => ({
        key: dashboard.id,
        title: dashboard.title,
        image: MinusIcon,
        route: `${dataBookRoute}/shift-manager/${dashboard.id}`,
        isActiveOn: `${dataBookRoutePattern}/shift-manager/${dashboard.id}/*`,
        hideTooltip: true,
        ...(isDataBookProFlagEnabled && dashboard.pro && { subscriptionPlanFeature: 'proDashboards' }),
      })),
    },
    {
      key: 'issueTracker',
      title: labels('issueTracker.title'),
      image: IssueTrackerIcon,
      route: `${dataBookRoute}/issue-tracker`,
      hideTooltip: true,
      children: groupedDashboards.issue_tracker.map((dashboard) => ({
        key: dashboard.id,
        title: dashboard.title,
        image: MinusIcon,
        route: `${dataBookRoute}/issue-tracker/${dashboard.id}`,
        isActiveOn: `${dataBookRoutePattern}/issue-tracker/${dashboard.id}/*`,
        hideTooltip: true,
        ...(isDataBookProFlagEnabled && dashboard.pro && { subscriptionPlanFeature: 'proDashboards' }),
      })),
    },
    {
      key: 'customDashboard',
      title: (
        <div className="flex justify-between items-center w-full">
          {labels('customDashboard.title')}
          {!canViewCustomDashboards && <Badge shape={SHAPE.BASIC} label={messages('badgeLabel')} size={SIZE.SMALL} />}
        </div>
      ),
      image: AdjustmentsHorizontalIcon,
      route: `${dataBookRoute}/custom`,
      hideTooltip: true,
      children: canViewCustomDashboards
        ? groupedDashboards.custom.map((dashboard) => ({
            key: dashboard.id,
            title: dashboard.title,
            image: MinusIcon,
            route: `${dataBookRoute}/custom/${dashboard.id}`,
            isActiveOn: `${dataBookRoutePattern}/custom/${dashboard.id}/*`,
            hideTooltip: true,
          }))
        : [],
      hidden: !isDataBookProFlagEnabled,
    }
  );

  return items
    .filter((item) => !item.hidden)
    .map((item) => ({
      ...item,
      children: item.children?.filter((child) => !child.hidden),
    }));
};

export const useSidebarItems = (currentProject?: ProjectSchema): SidebarItemType[] => {
  const highlightItems = useHighlightItems(currentProject);
  const overviewItems = useOverviewItems(currentProject);
  const adminItems = useAdminItems(currentProject);
  const dataBookItems = useDataBookItems(currentProject);

  return [...highlightItems, ...overviewItems, ...adminItems, ...dataBookItems];
};
