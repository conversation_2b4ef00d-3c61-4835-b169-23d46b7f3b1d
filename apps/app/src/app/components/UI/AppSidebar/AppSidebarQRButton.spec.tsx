import { useSidebar } from '@shape-construction/arch-ui/src/Navigation/Sidebar';
import React from 'react';
import { render, screen, userEvent } from 'tests/test-utils';
import { AppSidebarQRButton } from './AppSidebarQRButton';

jest.mock('@shape-construction/arch-ui/src/Navigation/Sidebar', () => ({
  useSidebar: jest.fn(),
}));

describe('AppSidebarQRButton', () => {
  beforeEach(() => {
    (useSidebar as jest.Mock).mockReturnValue({
      isLargeScreen: true,
      open: true,
      toggleSidebar: jest.fn(),
    });
  });

  it('renders button with correct label', () => {
    render(<AppSidebarQRButton onClick={jest.fn()} />);

    expect(screen.getByLabelText('appSidebar.getShapeAndChannelsOnMobile.title')).toBeInTheDocument();
  });

  describe('when clicked', () => {
    it('calls onClick handler', async () => {
      const onClick = jest.fn();
      render(<AppSidebarQRButton onClick={onClick} />);

      await userEvent.click(screen.getByLabelText('appSidebar.getShapeAndChannelsOnMobile.title'));

      expect(onClick).toHaveBeenCalledTimes(1);
    });

    it('closes sidebar on mobile', async () => {
      const toggleSidebar = jest.fn();
      (useSidebar as jest.Mock).mockReturnValue({
        isLargeScreen: false,
        open: true,
        toggleSidebar,
      });

      render(<AppSidebarQRButton onClick={jest.fn()} />);
      await userEvent.click(screen.getByLabelText('appSidebar.getShapeAndChannelsOnMobile.title'));

      expect(toggleSidebar).toHaveBeenCalledWith(false);
    });
  });
});
