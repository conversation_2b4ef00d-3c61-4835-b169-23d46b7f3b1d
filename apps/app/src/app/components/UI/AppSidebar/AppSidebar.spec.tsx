import { booleanFlagFactory, featureFlagsFactory } from '@shape-construction/api/factories/feature-flags';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { getApiFeatureFlagsMockHandler } from '@shape-construction/api/handlers-factories/feature-flags';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { ShapeIssueTrackerIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import Sidebar from '@shape-construction/arch-ui/src/Navigation/Sidebar';
import { mediaQueryOptions } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { createMemoryHistory } from 'history';
import { server } from 'tests/mock-server';
import { createMatchMedia, eachDeviceSize, render, screen, within } from 'tests/test-utils';
import AppSidebar from '.';
import type { SidebarItemType } from './useSidebarItems';

const renderAppSidebarComponent = (props = {}, context = {}) =>
  render(
    <Sidebar.Provider {...props}>
      <AppSidebar.Root />
    </Sidebar.Provider>,
    { ...context }
  );

describe('<AppSidebar.Root />', () => {
  describe('when on large screens', () => {
    beforeEach(() => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.lg);
    });

    it('renders the project selector in the header', () => {
      renderAppSidebarComponent();

      const appHeader = screen.getByTestId('app-sidebar-header');
      expect(appHeader).toBeInTheDocument();
      expect(within(appHeader).getByRole('menu', { name: 'appSidebar.projectsMenu' })).toBeInTheDocument();
    });

    it('renders the collapse button in the footer', () => {
      renderAppSidebarComponent();

      const appFooter = screen.getByTestId('app-sidebar-footer');
      expect(appFooter).toBeInTheDocument();
      expect(within(appFooter).getByRole('button', { name: 'sidebar control' })).toBeInTheDocument();
    });

    it('renders highlight items', () => {
      renderAppSidebarComponent();

      const highlightSection = screen.getByTestId('app-sidebar-highlight-section');
      expect(highlightSection).toBeInTheDocument();
      expect(within(highlightSection).getByText('navigation.issues')).toBeInTheDocument();
      expect(within(highlightSection).getByText('navigation.weeklyPlanner')).toBeInTheDocument();
      expect(within(highlightSection).getByText('navigation.shiftReports')).toBeInTheDocument();
      expect(within(highlightSection).getByText('navigation.channels')).toBeInTheDocument();
      expect(within(highlightSection).getByText('navigation.gallery')).toBeInTheDocument();
    });

    it('renders overview items', () => {
      renderAppSidebarComponent();

      const overviewSection = screen.getByTestId('app-sidebar-overview-section');
      expect(overviewSection).toBeInTheDocument();
      expect(within(overviewSection).getByText('navigation.timeline')).toBeInTheDocument();
      expect(within(overviewSection).getByText('navigation.activities')).toBeInTheDocument();
      expect(within(overviewSection).getByText('navigation.dashboard')).toBeInTheDocument();
    });

    describe('when user has admin role', () => {
      it('renders admin menu trigger only', async () => {
        const project: ProjectSchema = projectFactory({
          currentTeamMemberRole: 'admin',
        });
        server.use(getApiProjectsProjectIdMockHandler(() => project));
        const route = { path: '/projects/:projectId/issues/:issueId' };
        const history = createMemoryHistory({
          initialEntries: ['/projects/project-0/issues/issue-0'],
        });
        renderAppSidebarComponent({}, { route, history });

        const appSidebar = await screen.findByTestId('app-sidebar');
        expect(appSidebar).toBeInTheDocument();
        expect(await within(appSidebar).findByRole('button', { name: 'appSidebar.admin' })).toBeInTheDocument();
        expect(within(appSidebar).queryByText('admin.menu.accessRequests')).not.toBeInTheDocument();
      });

      describe('when route is admin route', () => {
        it('opens admin submenu by default', async () => {
          const project: ProjectSchema = projectFactory({
            currentTeamMemberRole: 'admin',
          });
          server.use(getApiProjectsProjectIdMockHandler(() => project));
          const route = { path: '/projects/:projectId/settings/access-requests' };
          const history = createMemoryHistory({
            initialEntries: ['/projects/project-0/settings/access-requests'],
          });
          renderAppSidebarComponent({}, { route, history });

          const appSidebar = await screen.findByTestId('app-sidebar');
          expect(appSidebar).toBeInTheDocument();
          expect(await within(appSidebar).findByRole('menu', { description: 'appSidebar.admin' })).toBeInTheDocument();
          expect(within(appSidebar).getByText('admin.menu.accessRequests')).toBeInTheDocument();
          expect(within(appSidebar).getByText('admin.menu.customFields')).toBeInTheDocument();
        });
      });
    });
  });

  describe('when on small screens', () => {
    describe('when sidebar is open', () => {
      it('renders navigation actions in the header', () => {
        window.matchMedia = createMatchMedia(mediaQueryOptions.sm);
        renderAppSidebarComponent({ open: true });

        const appHeader = screen.getByTestId('app-sidebar-header');
        expect(appHeader).toBeInTheDocument();
        expect(within(appHeader).getByRole('button', { name: /back button/ })).toBeInTheDocument();
        expect(within(appHeader).getByRole('button', { name: /forward button/ })).toBeInTheDocument();
        expect(within(appHeader).getByRole('button', { name: /reload button/ })).toBeInTheDocument();
      });
    });
  });
});

describe('<AppSidebar.Item />', () => {
  eachDeviceSize(() => {
    it('renders correctly', () => {
      const item: SidebarItemType = {
        key: 'issues',
        title: 'Issues',
        route: '/new-route',
        image: ShapeIssueTrackerIcon,
      };
      render(
        <Sidebar.Provider>
          <AppSidebar.Item item={item} />
        </Sidebar.Provider>
      );

      expect(screen.getByRole('button', { name: /Issues/ })).toBeVisible();
      expect(screen.getByRole('button', { name: /Issues-icon/ })).toBeVisible();
    });

    describe('when the user clicks on the sidebar item', () => {
      it('redirects to new route', async () => {
        const history = createMemoryHistory({ initialEntries: ['/'] });
        const item: SidebarItemType = {
          key: 'issues',
          title: 'Issues',
          route: '/new-route',
          image: ShapeIssueTrackerIcon,
        };
        const { user } = render(
          <Sidebar.Provider>
            <AppSidebar.Item item={item} />
          </Sidebar.Provider>,
          { history }
        );

        await user.click(screen.getByRole('button', { name: /Issues/ }));

        expect(history.location).toEqual(expect.objectContaining({ pathname: '/new-route' }));
      });
    });

    describe('when item is disabled', () => {
      it('renders item as disabled', () => {
        const item: SidebarItemType = {
          key: 'issues',
          title: 'Issues',
          route: '/new-route',
          image: ShapeIssueTrackerIcon,
          disabled: true,
        };
        render(
          <Sidebar.Provider>
            <AppSidebar.Item item={item} />
          </Sidebar.Provider>
        );

        expect(screen.getByRole('button', { name: /Issues/ })).toBeDisabled();
      });

      it('does not allow to redirect', async () => {
        const history = createMemoryHistory({ initialEntries: ['/'] });
        const item: SidebarItemType = {
          key: 'issues',
          title: 'Issues',
          route: '/new-route',
          image: ShapeIssueTrackerIcon,
          disabled: true,
        };
        const { user } = render(
          <Sidebar.Provider>
            <AppSidebar.Item item={item} />
          </Sidebar.Provider>
        );

        await user.click(screen.getByRole('button', { name: /Issues/ }));

        expect(history.location).toEqual(expect.objectContaining({ pathname: '/' }));
      });
    });

    describe('when the user clicks on the sidebar item', () => {
      it('render children', async () => {
        const flagData = featureFlagsFactory({
          user: [booleanFlagFactory('data-book', true)],
        });
        server.use(getApiFeatureFlagsMockHandler(() => flagData));
        const history = createMemoryHistory({ initialEntries: ['/'] });
        const item: SidebarItemType = {
          key: 'dataBook',
          title: 'Data Book',
          route: '/data-book',
          image: ShapeIssueTrackerIcon,
          children: [
            {
              key: 'issueTracker',
              title: 'Issue tracker',
              route: '/data-book/issue-tracker',
              image: ShapeIssueTrackerIcon,
            },
          ],
        };
        const { user } = render(
          <Sidebar.Provider>
            <AppSidebar.Item item={item} />
          </Sidebar.Provider>,
          { history }
        );

        await user.click(screen.getByRole('button', { name: /Data Book/ }));

        expect(screen.getByRole('button', { name: /Issue tracker/ })).toBeVisible();
      });

      describe('the sidebar item has pro dashboards', () => {
        it('shows the pro badge', async () => {
          const flagData = featureFlagsFactory({
            user: [booleanFlagFactory('data-book', true)],
          });
          server.use(getApiFeatureFlagsMockHandler(() => flagData));
          const history = createMemoryHistory({ initialEntries: ['/'] });
          const item: SidebarItemType = {
            key: 'dataBook',
            title: 'Data Book',
            route: '/data-book',
            image: ShapeIssueTrackerIcon,
            children: [
              {
                key: 'dataQuality',
                title: 'Data Quality',
                route: '/data-book/shift-manager/data-quality',
                image: ShapeIssueTrackerIcon,
                subscriptionPlanFeature: 'proDashboards',
              },
            ],
          };
          const { user } = render(
            <Sidebar.Provider>
              <AppSidebar.Item item={item} />
            </Sidebar.Provider>,
            { history }
          );

          await user.click(screen.getByRole('button', { name: /Data Book/ }));

          expect(screen.getByLabelText('FEATURELIMITS.BADGE')).toBeVisible();
        });
      });

      describe('when user clicks on pro dashboards', () => {
        it('shows subscription plan modal', async () => {
          const flagData = featureFlagsFactory({
            user: [booleanFlagFactory('data-book', true)],
          });
          server.use(getApiFeatureFlagsMockHandler(() => flagData));
          const history = createMemoryHistory({ initialEntries: ['/'] });
          const item: SidebarItemType = {
            key: 'dataBook',
            title: 'Data Book',
            route: '/data-book',
            image: ShapeIssueTrackerIcon,
            children: [
              {
                key: 'dataQuality',
                title: 'Data Quality',
                route: '/data-book/shift-manager/data-quality',
                image: ShapeIssueTrackerIcon,
                subscriptionPlanFeature: 'proDashboards',
              },
            ],
          };
          const { user } = render(
            <Sidebar.Provider>
              <AppSidebar.Item item={item} />
            </Sidebar.Provider>,
            { history }
          );

          await user.click(screen.getByRole('button', { name: /Data Book/ }));
          await user.click(screen.getAllByRole('button', { name: /Data Quality/ })[0]);

          expect(await screen.findByText('subscriptionPlan.modal.title')).toBeVisible();
        });
      });
    });
  });
});
