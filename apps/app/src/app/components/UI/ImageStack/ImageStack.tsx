import { Button } from '@shape-construction/arch-ui';
import React, { useState } from 'react';
import { Thumbnail } from '../Thumbnail/Thumbnail';

const Tile = (props: any) => (
  <li className="flex-shrink-0">
    <div className="h-full block relative overflow-hidden">
      <Thumbnail {...props} size="preview" margin={4} />
    </div>
  </li>
);

type ImageStackProps = {
  imageUrls: string[];
  stackSize?: number;
  onClickImage: (index: number) => void;
};

export const ImageStack: React.FC<ImageStackProps> = ({ imageUrls, stackSize = 4, onClickImage }) => {
  const [showAll, setShowAll] = useState(false);

  const images = imageUrls.map((url, index) => ({
    url,
    index,
  }));

  const cutOffIndex = stackSize - 1;
  const numberOfCutOffTiles = imageUrls.length - stackSize;

  const handleShowAll = () => setShowAll(true);
  const handleHide = () => setShowAll(false);

  return (
    <>
      <ul className="flex p-0 flex-wrap list-none overflow-y-auto scroll-smooth -m-0.5">
        {images.slice(0, cutOffIndex).map(({ url, index }: any) => (
          <Tile imageUrl={url} key={url} onClick={() => onClickImage(index)} />
        ))}
        {images.length === stackSize && <Tile imageUrl={imageUrls[cutOffIndex]} />}
        {imageUrls.length > stackSize &&
          (showAll ? (
            images
              .slice(cutOffIndex)
              .map(({ url, index }: any) => <Tile imageUrl={url} key={url} onClick={() => onClickImage(index)} />)
          ) : (
            <Tile imageUrl={imageUrls[cutOffIndex]} onClick={handleShowAll} overflowNumber={numberOfCutOffTiles + 1} />
          ))}
      </ul>

      {showAll && (
        <Button onClick={handleHide} size="sm" color="secondary" variant="text">
          HIDE
        </Button>
      )}
    </>
  );
};
