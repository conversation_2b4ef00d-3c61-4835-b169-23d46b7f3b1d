import React, { useState, useEffect } from 'react';
import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';

type ImageLightboxProps = {
  imageUrls: string[];
  open: boolean;
  defaultIndex?: number;
  onClose: () => void;
};

export const ImageLightbox: React.FC<ImageLightboxProps> = ({
  imageUrls = [],
  open = false,
  defaultIndex = 0,
  onClose,
}) => {
  const [currentIndex, setCurrentIndex] = useState(defaultIndex);

  useEffect(() => {
    setCurrentIndex(defaultIndex);
  }, [setCurrentIndex, defaultIndex]);

  const nextIndex = () => (currentIndex + 1) % imageUrls.length;
  const prevIndex = () => (currentIndex - 1) % imageUrls.length;

  const handleMoveNext = () => setCurrentIndex(nextIndex());
  const handleMovePrev = () => setCurrentIndex(prevIndex());

  return (
    <>
      {open && (
        <Lightbox
          reactModalStyle={{ overlay: { zIndex: 1300 } }}
          mainSrc={imageUrls[currentIndex]}
          nextSrc={imageUrls[nextIndex()]}
          prevSrc={imageUrls[prevIndex()]}
          onMoveNextRequest={handleMoveNext}
          onMovePrevRequest={handleMovePrev}
          onCloseRequest={onClose}
        />
      )}
    </>
  );
};
