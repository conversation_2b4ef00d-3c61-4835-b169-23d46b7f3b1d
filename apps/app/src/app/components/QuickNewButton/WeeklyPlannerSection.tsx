import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { Menu } from '@shape-construction/arch-ui';
import { WeeklyPlannerIcon } from '@shape-construction/arch-ui/src/Icons/product-logo';
import { MenuHeading } from '@shape-construction/arch-ui/src/Menu/Menu';
import { useQuery } from '@tanstack/react-query';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import React from 'react';
import { Link, useLocation } from 'react-router';
import type { TabHref } from '../../pages/projects/[projectId]/weekly-planner/constants';

export type WorkPlanSectionProps = {
  projectId: ProjectSchema['id'];
};

export const WeeklyPlannerSection = ({ projectId }: WorkPlanSectionProps) => {
  const messages = useMessageGetter('quickNewButton');
  const location = useLocation();
  const { data: project } = useQuery(getProjectQueryOptions(projectId));
  const canCreateWeeklyWorkPlan = project?.availableActions?.createWeeklyWorkPlan;
  const workPlanLink = {
    to: `/projects/${projectId}/weekly-planner/plans/new`,
    state: { background: location, tab: 'my-plans' as TabHref },
  };

  if (!project || !canCreateWeeklyWorkPlan) return null;

  return (
    <>
      <MenuHeading className="flex flex-row gap-2">
        <WeeklyPlannerIcon className="size-4" />
        {messages('weeklyPlanner.heading')}
      </MenuHeading>
      <Link {...workPlanLink} key={messages('weeklyPlanner.options.workPlan.title')} className="w-full">
        <Menu.Item aria-label={messages('weeklyPlanner.options.workPlan.title')}>
          <div className="text-sm font-medium leading-tight">{messages('weeklyPlanner.options.workPlan.title')}</div>
        </Menu.Item>
      </Link>
    </>
  );
};
