import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { Menu } from '@shape-construction/arch-ui';
import { ActivitiesIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { useQuery } from '@tanstack/react-query';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import React from 'react';
import { Link, useLocation } from 'react-router';

export type ActivitySectionProps = {
  projectId: ProjectSchema['id'];
};

export const ActivitySection = ({ projectId }: ActivitySectionProps) => {
  const location = useLocation();
  const messages = useMessageGetter('quickNewButton');
  const { data: project } = useQuery(getProjectQueryOptions(projectId));
  const canCreateActivity = project?.availableActions?.createShiftActivity;

  const activityLink = {
    to: `/projects/${projectId}/activities/new`,
    state: { background: location },
  };

  if (!project || !canCreateActivity) return null;

  return (
    <Link {...activityLink} className="w-full">
      <Menu.Item aria-label={messages('activity.title')}>
        <div className="flex flex-row gap-2 items-center pt-1">
          <ActivitiesIcon className="size-4 text-icon-neutral-subtle" />
          <div className="text-sm font-medium leading-tight">{messages('activity.title')}</div>
        </div>
      </Menu.Item>
    </Link>
  );
};
