import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { Menu } from '@shape-construction/arch-ui';
import { ShiftReportsIcon } from '@shape-construction/arch-ui/src/Icons/product-logo';
import { MenuHeading } from '@shape-construction/arch-ui/src/Menu/Menu';
import { useQuery } from '@tanstack/react-query';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import React from 'react';
import { useCreateDraftReport } from '../../hooks/useCreateDraftReport';

export type ShiftManagerSectionProps = {
  projectId: ProjectSchema['id'];
};

export const ShiftManagerSection = ({ projectId }: ShiftManagerSectionProps) => {
  const messages = useMessageGetter('quickNewButton');
  const { data: project } = useQuery(getProjectQueryOptions(projectId));
  const canCreateShiftReport = project?.availableActions?.createShiftReport;
  const createDraftReport = useCreateDraftReport(projectId);

  if (!project || !canCreateShiftReport) return null;

  return (
    <>
      <MenuHeading className="flex flex-row gap-2">
        <ShiftReportsIcon className="size-4" />
        {messages('shiftManager.heading')}
      </MenuHeading>
      {canCreateShiftReport && (
        <Menu.Item aria-label={messages('shiftManager.options.shiftReport.title')} onClick={() => createDraftReport()}>
          <div className="text-sm font-medium leading-tight">{messages('shiftManager.options.shiftReport.title')}</div>
        </Menu.Item>
      )}
    </>
  );
};
