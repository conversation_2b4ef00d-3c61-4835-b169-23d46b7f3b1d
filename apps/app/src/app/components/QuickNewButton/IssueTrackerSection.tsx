import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { Menu } from '@shape-construction/arch-ui';
import { IssueTrackerIcon } from '@shape-construction/arch-ui/src/Icons/product-logo';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useModal } from '@shape-construction/hooks';
import { useQuery } from '@tanstack/react-query';
import { SmartIssueModal } from 'app/pages/projects/[projectId]/issues/smart/SmartIssueModal';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import React from 'react';
import { Link, type LinkProps, useLocation } from 'react-router';

export type IssueTrackerOption = {
  label: string;
  description: string;
  link: LinkProps;
  onClick?: () => void;
};

export type IssueTrackerSectionProps = {
  projectId: ProjectSchema['id'];
};

export const IssueTrackerSection = ({ projectId }: IssueTrackerSectionProps) => {
  const messages = useMessageGetter('quickNewButton');
  const location = useLocation();
  const { data: project } = useQuery(getProjectQueryOptions(projectId));
  const canCreateIssue = project?.availableActions?.createIssue;
  const { open: isSmartIssueOpen, openModal, closeModal } = useModal(false);
  const { value: isSmartIssueModalEnabled } = useFeatureFlag('smart-issue');

  if (!project || !canCreateIssue) return null;

  const options: IssueTrackerOption[] = [
    {
      label: messages('issueTracker.options.completeIssue.title'),
      description: messages('issueTracker.options.completeIssue.description'),
      link: {
        to: `/projects/${projectId}/issues/new`,
        state: { background: location },
      },
    },
    {
      label: messages('issueTracker.options.quickIssue.title'),
      description: messages('issueTracker.options.quickIssue.description'),
      link: {
        to: `/projects/${projectId}/issues/quick-capture`,
        state: { background: location },
      },
    },
    ...(isSmartIssueModalEnabled
      ? [
          {
            label: messages('issueTracker.options.smartIssue.title'),
            description: messages('issueTracker.options.smartIssue.description'),
            onClick: openModal,
            link: { to: '' },
          },
        ]
      : []),
  ];

  const renderContent = (option: IssueTrackerOption) => (
    <div className="flex flex-col gap-1 justify-start items-start">
      <div className="text-sm font-medium leading-tight">{option.label}</div>
      <div className="opacity-60 text-sm font-normal leading-tight">{option.description}</div>
    </div>
  );

  return (
    <>
      <Menu.Heading className="flex flex-row gap-2">
        <IssueTrackerIcon className="size-4" />
        {messages('issueTracker.heading')}
      </Menu.Heading>
      {options.map((option) =>
        option.onClick ? (
          <Menu.Item key={option.label} className="w-full" aria-label={option.label} onClick={option.onClick}>
            {renderContent(option)}
          </Menu.Item>
        ) : (
          <Link {...option.link} key={option.label} className="w-full">
            <Menu.Item aria-label={option.label}>{renderContent(option)}</Menu.Item>
          </Link>
        )
      )}
      {isSmartIssueOpen && <SmartIssueModal onClose={closeModal} projectId={projectId} />}
    </>
  );
};
