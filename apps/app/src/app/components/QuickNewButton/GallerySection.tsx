import { useMessageGetter } from '@messageformat/react';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { FileUpload, Menu } from '@shape-construction/arch-ui';
import { ProjectGalleryIcon } from '@shape-construction/arch-ui/src/Icons/product-logo';
import { MenuHeading } from '@shape-construction/arch-ui/src/Menu/Menu';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import { useQuery } from '@tanstack/react-query';
import { MAX_FILE_SIZE_IN_BYTES } from 'app/constants/FileUpload';
import { useFileUploadValidator } from 'app/hooks/useFileUploadValidator';
import { getProjectQueryOptions } from 'app/queries/projects/projects';
import React from 'react';
import { imageAcceptTypes } from '../Gallery/constants';
import { MediaPicker, type MediaPickerOptions } from '../MediaPicker/MediaPicker';

export type GallerySectionProps = {
  projectId: ProjectSchema['id'];
  onUpload: (files: File[]) => void;
};

export const GallerySection = ({ projectId, onUpload }: GallerySectionProps) => {
  const messages = useMessageGetter('quickNewButton');
  const fileUploadErrorMessages = useMessageGetter('errors.fileUpload');
  const { data: project } = useQuery(getProjectQueryOptions(projectId));
  const canUploadDocument = project?.availableActions?.uploadDocument;
  const isLargeScreen = useMediaQuery(breakpoints.up('md'));

  const { validateFiles, handleValidationErrors } = useFileUploadValidator({
    maxSizeInBytes: MAX_FILE_SIZE_IN_BYTES,
    errorMessages: {
      fileSizeMin: (filename, min) => fileUploadErrorMessages('fileSizeMin', { filename, min }),
      fileSizeMax: (filename, max) => fileUploadErrorMessages('fileSizeMax', { filename, max }),
      fileTypeInvalid: (filename) => fileUploadErrorMessages('fileTypeInvalid', { filename }),
    },
  });

  const onHandleFileChange = (files: File[]) => {
    const results = validateFiles(files);
    handleValidationErrors(results);

    const validDocuments = results.filter((result) => result.isValid).map((result) => result.file);
    validDocuments.forEach((file) => onUpload([file]));
  };

  const options: MediaPickerOptions = {
    gallery: {
      accept: imageAcceptTypes,
      enabled: !isLargeScreen,
      multiple: true,
      onSelectFiles: onHandleFileChange,
    },
    camera: {
      enabled: true,
      multiple: true,
      onSelectFiles: onHandleFileChange,
    },
    documents: {
      accept: '*',
      enabled: true,
      multiple: true,
      onSelectFiles: onHandleFileChange,
    },
  };

  const renderUploadItem = () => (
    <FileUpload.Root
      accept={options.documents?.accept}
      multiple={options.documents?.multiple}
      onChange={options.documents?.onSelectFiles}
    >
      <FileUpload.Trigger asChild>
        <div className="text-sm font-medium leading-tight">{messages('gallery.options.upload.title')}</div>
      </FileUpload.Trigger>
    </FileUpload.Root>
  );

  const renderMobileUploadItem = () => (
    <MediaPicker options={options}>
      <div className="text-sm font-medium leading-tight">{messages('gallery.options.upload.title')}</div>
    </MediaPicker>
  );

  if (!project || !canUploadDocument) return null;

  return (
    <>
      <MenuHeading className="flex flex-row gap-2">
        <ProjectGalleryIcon className="size-4" />
        {messages('gallery.heading')}
      </MenuHeading>
      <Menu.Item aria-label={messages('gallery.options.upload.title')}>
        {isLargeScreen ? renderUploadItem() : renderMobileUploadItem()}
      </Menu.Item>
    </>
  );
};
