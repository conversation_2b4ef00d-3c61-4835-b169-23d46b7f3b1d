import { booleanFlagFactory, featureFlagsFactory } from '@shape-construction/api/factories/feature-flags';
import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { getApiFeatureFlagsMockHandler } from '@shape-construction/api/handlers-factories/feature-flags';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { QuickNewButton } from './QuickNewButton';

describe('<QuickNewButton />', () => {
  describe('when the user has all permissions to create items from the menu', () => {
    it('renders all menu items correctly', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createIssue: true,
          createShiftActivity: true,
          createShiftReport: true,
          createWeeklyWorkPlan: true,
          uploadDocument: true,
        }),
      });
      const history = createMemoryHistory({ initialEntries: ['/projects/project-0/issues'] });
      const route = { path: '/projects/:projectId/issues' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      await waitFor(() => expect(screen.getAllByRole('menuitem')).toHaveLength(6));
      expect(screen.getByText('quickNewButton.issueTracker.heading')).toBeInTheDocument();
      expect(
        screen.getByRole('menuitem', { name: 'quickNewButton.issueTracker.options.completeIssue.title' })
      ).toBeInTheDocument();
      expect(
        screen.getByRole('menuitem', { name: 'quickNewButton.issueTracker.options.quickIssue.title' })
      ).toBeInTheDocument();
      expect(screen.getByText('quickNewButton.weeklyPlanner.heading')).toBeInTheDocument();
      expect(
        screen.getByRole('menuitem', { name: 'quickNewButton.weeklyPlanner.options.workPlan.title' })
      ).toBeInTheDocument();
      expect(screen.getByText('quickNewButton.shiftManager.heading')).toBeInTheDocument();
      expect(
        screen.getByRole('menuitem', { name: 'quickNewButton.shiftManager.options.shiftReport.title' })
      ).toBeInTheDocument();
      expect(screen.getByText('quickNewButton.gallery.heading')).toBeInTheDocument();
      expect(screen.getByRole('menuitem', { name: 'quickNewButton.gallery.options.upload.title' })).toBeInTheDocument();
      expect(screen.getByRole('menuitem', { name: 'quickNewButton.activity.title' })).toBeInTheDocument();
      expect(screen.getAllByRole('separator', { name: 'divider' })).toHaveLength(4);
    });
  });

  describe('when the user has no permissions to create items from the menu', () => {
    it('does not display the quick new button', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({
          createIssue: false,
          createShiftActivity: false,
          createShiftReport: false,
          createWeeklyWorkPlan: false,
          uploadDocument: false,
        }),
      });
      const history = createMemoryHistory({ initialEntries: ['/projects/project-0/issues'] });
      const route = { path: '/projects/:projectId/issues' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      await waitFor(() => {
        const trigger = screen.queryByRole('button', { name: 'quickNewButton.trigger.title' });
        expect(trigger).not.toBeInTheDocument();
      });
    });
  });

  describe('when the user has permission to create an issue', () => {
    it('renders the issue tracker section correctly', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ createIssue: true }),
      });
      const history = createMemoryHistory({ initialEntries: ['/projects/project-0/issues'] });
      const route = { path: '/projects/:projectId/issues' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      expect(await screen.findByText('quickNewButton.issueTracker.heading')).toBeInTheDocument();
      const completeOption = screen.getByRole('menuitem', {
        name: 'quickNewButton.issueTracker.options.completeIssue.title',
      });
      expect(completeOption).toBeInTheDocument();
      expect(completeOption).toHaveTextContent('quickNewButton.issueTracker.options.completeIssue.title');
      expect(completeOption).toHaveTextContent('quickNewButton.issueTracker.options.completeIssue.description');
      const quickOption = screen.getByRole('menuitem', {
        name: 'quickNewButton.issueTracker.options.quickIssue.title',
      });
      expect(quickOption).toBeInTheDocument();
      expect(quickOption).toHaveTextContent('quickNewButton.issueTracker.options.quickIssue.title');
      expect(quickOption).toHaveTextContent('quickNewButton.issueTracker.options.quickIssue.description');
    });

    describe('when user selects the complete issue option', () => {
      it('navigates to the new complete issue url', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({ createIssue: true }),
        });
        const history = createMemoryHistory({ initialEntries: ['/projects/project-0/issues'] });
        const route = { path: '/projects/:projectId/issues' };
        server.use(getApiProjectsProjectIdMockHandler(() => project));
        render(<QuickNewButton projectId={project.id} />, {
          history,
          route,
        });

        const trigger = await screen.findByRole('button', {
          name: 'quickNewButton.trigger.title',
        });
        await userEvent.click(trigger);
        await userEvent.click(
          await screen.findByRole('menuitem', {
            name: 'quickNewButton.issueTracker.options.completeIssue.title',
          })
        );

        expect(history.location.pathname).toBe('/projects/project-0/issues/new');
      });
    });

    describe('when user selects the quick issue option', () => {
      it('navigates to the new quick issue url', async () => {
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({ createIssue: true }),
        });
        const history = createMemoryHistory({ initialEntries: ['/projects/project-0/issues'] });
        const route = { path: '/projects/:projectId/issues' };
        server.use(getApiProjectsProjectIdMockHandler(() => project));
        render(<QuickNewButton projectId={project.id} />, {
          history,
          route,
        });

        const trigger = await screen.findByRole('button', {
          name: 'quickNewButton.trigger.title',
        });
        await userEvent.click(trigger);
        await userEvent.click(
          await screen.findByRole('menuitem', {
            name: 'quickNewButton.issueTracker.options.quickIssue.title',
          })
        );

        expect(history.location.pathname).toBe('/projects/project-0/issues/quick-capture');
      });
    });

    describe('when smart-issue feature flag is enabled', () => {
      describe('when user selects the smart issue option', () => {
        it('opens the smart issue modal', async () => {
          const project = projectFactory({
            id: 'project-0',
            availableActions: projectAvailableActions({ createIssue: true }),
          });
          const history = createMemoryHistory({ initialEntries: ['/projects/project-0/issues'] });
          const route = { path: '/projects/:projectId/issues' };
          const flagData = featureFlagsFactory({
            user: [booleanFlagFactory('smart-issue', true)],
          });
          server.use(
            getApiProjectsProjectIdMockHandler(() => project),
            getApiFeatureFlagsMockHandler(() => flagData)
          );
          render(<QuickNewButton projectId={project.id} />, {
            history,
            route,
          });

          const trigger = await screen.findByRole('button', {
            name: 'quickNewButton.trigger.title',
          });
          await userEvent.click(trigger);
          await userEvent.click(
            await screen.findByRole('menuitem', {
              name: 'quickNewButton.issueTracker.options.smartIssue.title',
            })
          );

          expect(screen.getByText('issue.smartIssue.title')).toBeInTheDocument();
        });
      });
    });
  });

  describe('when user does not have permission to create an issue', () => {
    it('does not render the issue tracker section', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ createIssue: false }),
      });
      const history = createMemoryHistory({ initialEntries: ['/projects/project-0/issues'] });
      const route = { path: '/projects/:projectId/issues' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      expect(screen.queryByText('quickNewButton.issueTracker.heading')).not.toBeInTheDocument();
      expect(
        screen.queryByRole('menuitem', {
          name: 'quickNewButton.issueTracker.options.completeIssue.title',
        })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('menuitem', {
          name: 'quickNewButton.issueTracker.options.quickIssue.title',
        })
      ).not.toBeInTheDocument();
    });
  });

  describe('when the user has permission to create a work plan', () => {
    it('renders the weekly planner section correctly', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ createWeeklyWorkPlan: true }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/weekly-planner'],
      });
      const route = { path: '/projects/:projectId/weekly-planner' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      expect(await screen.findByText('quickNewButton.weeklyPlanner.heading')).toBeInTheDocument();
      const workPlan = screen.getByRole('menuitem', {
        name: 'quickNewButton.weeklyPlanner.options.workPlan.title',
      });
      expect(workPlan).toBeInTheDocument();
      expect(workPlan).toHaveTextContent('quickNewButton.weeklyPlanner.options.workPlan.title');
    });

    it('renders the activity section as the last menuitem', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ createShiftActivity: true }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/activities'],
      });
      const route = { path: '/projects/:projectId/activities' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      const activity = screen.getByRole('menuitem', { name: 'quickNewButton.activity.title' });
      expect(activity).toBeInTheDocument();
      expect(activity).toHaveTextContent('quickNewButton.activity.title');
      const menuitems = await screen.findAllByRole('menuitem');
      expect(menuitems[menuitems.length - 1]).toBe(activity);
    });
  });

  describe('when the user does not have permission to create a work plan', () => {
    it('does not render the weekly planner section', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ createWeeklyWorkPlan: false }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/weekly-planner'],
      });
      const route = { path: '/projects/:projectId/weekly-planner' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      expect(screen.queryByText('quickNewButton.weeklyPlanner.heading')).not.toBeInTheDocument();
      expect(
        screen.queryByRole('menuitem', {
          name: 'quickNewButton.weeklyPlanner.options.workPlan.title',
        })
      ).not.toBeInTheDocument();
    });

    it('does not render the activity section', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ createShiftActivity: false }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/activity'],
      });
      const route = { path: '/projects/:projectId/activity' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      expect(
        screen.queryByRole('menuitem', {
          name: 'quickNewButton.activity.title',
        })
      ).not.toBeInTheDocument();
    });
  });

  describe('when the user has permission to create a shift report', () => {
    it('renders the shift manager section correctly', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ createShiftReport: true }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports'],
      });
      const route = { path: '/projects/:projectId/shift-reports' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      expect(await screen.findByText('quickNewButton.shiftManager.heading')).toBeInTheDocument();
      const shiftReport = screen.getByRole('menuitem', {
        name: 'quickNewButton.shiftManager.options.shiftReport.title',
      });
      expect(shiftReport).toBeInTheDocument();
      expect(shiftReport).toHaveTextContent('quickNewButton.shiftManager.options.shiftReport.title');
    });
  });

  describe('when the user does not have permission to create a shift report', () => {
    it('does not render the shift manager section', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ createShiftReport: false }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/shift-reports'],
      });
      const route = { path: '/projects/:projectId/shift-reports' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      expect(screen.queryByText('quickNewButton.shiftManager.heading')).not.toBeInTheDocument();
      expect(
        screen.queryByRole('menuitem', {
          name: 'quickNewButton.shiftManager.options.shiftReport.title',
        })
      ).not.toBeInTheDocument();
    });
  });

  describe('when the user has permission to upload a document', () => {
    it('renders the gallery section correctly', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ uploadDocument: true }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/gallery'],
      });
      const route = { path: '/projects/:projectId/gallery' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      expect(await screen.findByText('quickNewButton.gallery.heading')).toBeInTheDocument();
      const gallery = screen.getByRole('menuitem', {
        name: 'quickNewButton.gallery.options.upload.title',
      });
      expect(gallery).toBeInTheDocument();
      expect(gallery).toHaveTextContent('quickNewButton.gallery.options.upload.title');
    });
  });

  describe('when user does not have permission to upload a document', () => {
    it('does not render the gallery section', async () => {
      const project = projectFactory({
        id: 'project-0',
        availableActions: projectAvailableActions({ uploadDocument: false }),
      });
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/gallery'],
      });
      const route = { path: '/projects/:projectId/gallery' };
      server.use(getApiProjectsProjectIdMockHandler(() => project));
      render(<QuickNewButton projectId={project.id} />, {
        history,
        route,
      });

      const trigger = await screen.findByRole('button', { name: 'quickNewButton.trigger.title' });
      expect(trigger).toBeInTheDocument();

      await userEvent.click(trigger);

      expect(screen.queryByText('quickNewButton.gallery.heading')).not.toBeInTheDocument();
      expect(
        screen.queryByRole('menuitem', {
          name: 'quickNewButton.gallery.options.upload.title',
        })
      ).not.toBeInTheDocument();
    });
  });
});
