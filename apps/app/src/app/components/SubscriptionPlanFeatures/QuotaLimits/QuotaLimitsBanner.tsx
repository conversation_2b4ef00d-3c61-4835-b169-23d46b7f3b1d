import { useMessageGetter } from '@messageformat/react';
import { Alert, Link } from '@shape-construction/arch-ui';
import { DiamondIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import React, { useState } from 'react';
import { SubscriptionPlanModal } from '../SubscriptionPlanModal/SubscriptionPlanModal';

type QuotaLimitsBannerProps = {
  className?: string;
};

export const QuotaLimitsBanner: React.FC<QuotaLimitsBannerProps> = ({ className }) => {
  const messages = useMessageGetter('quotaLimits');

  const [openModal, setOpenModal] = useState(false);
  const handleModalClose = () => setOpenModal(false);
  const handleModalOpen = () => setOpenModal(true);

  return (
    <div className={className}>
      <Alert color="success" rounded={false} customIcon={<DiamondIcon className="text-green-400" />}>
        <Alert.Message>
          <div className="flex gap-2 flex-col sm:flex-row">
            {messages('badge')}
            <Link color="success" as="button" onClick={handleModalOpen}>
              {messages('cta')}
            </Link>
          </div>
        </Alert.Message>
      </Alert>
      <SubscriptionPlanModal open={openModal} onClose={handleModalClose} />
    </div>
  );
};
