import { createMemoryHistory } from 'history';
import React from 'react';
import { fakeObserver, render, screen, userEvent } from 'tests/test-utils';
import { SubscriptionPlanModal } from './SubscriptionPlanModal';

describe('SubscriptionPlanModal', () => {
  beforeEach(() => {
    window.IntersectionObserver = fakeObserver();
  });

  it('renders the component', () => {
    render(<SubscriptionPlanModal open onClose={() => {}} />);

    expect(screen.getByText('subscriptionPlan.modal.heading')).toBeInTheDocument();
    expect(screen.getByText('subscriptionPlan.modal.title')).toBeInTheDocument();
    expect(screen.getByText('subscriptionPlan.modal.subtitle')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'subscriptionPlan.modal.learnMore' })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'subscriptionPlan.modal.talkToUs' })).toBeInTheDocument();
  });

  it('triggers onClose function by clicking on call to action', async () => {
    const onClose = jest.fn();
    render(<SubscriptionPlanModal open onClose={onClose} />);

    await userEvent.click(screen.getByRole('button', { name: 'Close Overlay' }));

    expect(onClose).toBeCalled();
  });

  describe('when clicking the "learnMore" button', () => {
    it('navigates to subscription settings', async () => {
      const history = createMemoryHistory({
        initialEntries: ['/projects/project-0/settings/teams/'],
      });
      const route = { path: '/projects/:projectId/settings/teams/' };
      const { user } = render(<SubscriptionPlanModal open onClose={() => {}} />, { history, route });

      await user.click(screen.getByRole('button', { name: 'subscriptionPlan.modal.learnMore' }));

      expect(history.location.pathname).toContain('/projects/project-0/settings/teams/');
    });
  });

  describe('when clicking the "talkToUs" button', () => {
    it('opens email link', async () => {
      const openSpy = jest.spyOn(window, 'open').mockImplementation(() => null);
      const { user } = render(<SubscriptionPlanModal open onClose={() => {}} />);

      await user.click(screen.getByRole('button', { name: 'subscriptionPlan.modal.talkToUs' }));

      expect(openSpy).toHaveBeenCalledWith(expect.stringContaining('mailto:'), '_blank');
    });
  });
});
