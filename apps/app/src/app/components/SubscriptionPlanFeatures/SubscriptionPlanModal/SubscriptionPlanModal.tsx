import { useMessageGetter } from '@messageformat/react';
import { getApiProjectsProjectIdQueryOptions } from '@shape-construction/api/src';
import { Button, Modal } from '@shape-construction/arch-ui';
import { DiamondIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { emailLinkFactory } from '@shape-construction/utils/Email';
import { useQuery } from '@tanstack/react-query';
import { environment } from 'app/config/environment';
import { getProjectTeamQueryOptions } from 'app/queries/projects/teams';
import React from 'react';
import { useMatch, useNavigate } from 'react-router';

interface SubscriptionPlanModalProps {
  open: boolean;
  onClose: () => void;
}

export const SubscriptionPlanModal: React.FC<SubscriptionPlanModalProps> = ({ open, onClose }) => {
  const messages = useMessageGetter('subscriptionPlan.modal');
  const emailMessages = useMessageGetter('team.subscriptions.card.actions.email.pro');
  const navigate = useNavigate();
  const projectRoute = useMatch('/projects/:projectId/*');
  const projectId = projectRoute?.params.projectId;
  const { data: project } = useQuery(getApiProjectsProjectIdQueryOptions(projectId!));
  const { data: team } = useQuery(getProjectTeamQueryOptions(projectId!, project?.currentTeamId!));

  const emailUrl = emailLinkFactory({
    email: environment.SUPPORT_EMAIL,
    subject: emailMessages('subject'),
    body: emailMessages('body', { teamName: team?.displayName, teamId: project?.currentTeamId }),
  });

  return (
    <Modal.Root open={open} onClose={onClose}>
      <Modal.Header onClose={onClose}>
        <Modal.Title>{messages('heading')}</Modal.Title>
      </Modal.Header>
      <Modal.Content className="py-7 flex flex-col items-center justify-center">
        <DiamondIcon className="h-12 w-12 text-green-500" />
        <h2 className="text-lg font-medium text-gray-900 mb-2 mt-4">{messages('title')}</h2>
        <p className="text-sm font-normal leading-5 text-gray-500">{messages('subtitle')}</p>
      </Modal.Content>
      <Modal.Footer className="flex justify-end gap-1">
        <Button
          variant="outlined"
          onClick={() => {
            navigate(`/projects/${projectId}/settings/teams/${project?.currentTeamId}/subscription`);
            onClose();
          }}
          color="secondary"
          size="xs"
        >
          {messages('learnMore')}
        </Button>
        <Button variant="contained" onClick={() => window.open(emailUrl, '_blank')} color="primary" size="xs">
          {messages('talkToUs')}
        </Button>
      </Modal.Footer>
    </Modal.Root>
  );
};
