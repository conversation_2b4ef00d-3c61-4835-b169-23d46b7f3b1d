import { getApiProjectsProjectIdQueryOptions } from '@shape-construction/api/src';
import { useQuery } from '@tanstack/react-query';
import { useTeamsSubscriptionPlan } from 'app/queries/teamsSubscriptionPlan/teamsSubscriptionPlan';
import { useMatch } from 'react-router';

export type FeatureName =
  | 'documentReferences'
  | 'exportIssues'
  | 'exportShiftReportsData'
  | 'issuePrivateChat'
  | 'printIssue'
  | 'shiftReportsManagerView'
  | 'proDashboards'
  | 'customDashboards';

export const useFeatureLimit = (featureName: FeatureName) => {
  const projectRoute = useMatch('/projects/:projectId/*');
  const projectId = projectRoute?.params.projectId;

  const { data: project, isLoading: isLoadingProject } = useQuery(getApiProjectsProjectIdQueryOptions(projectId!));
  const teamId = project?.currentTeamId;

  const { data, isLoading: isLoadingSubscription } = useTeamsSubscriptionPlan(projectId!, teamId!);

  const featureEnabled = Boolean(data?.features[featureName]?.available);
  const isLoading = isLoadingProject || isLoadingSubscription;

  return {
    featureEnabled,
    isLoading,
  };
};
