import { useMessageGetter } from '@messageformat/react';
import { Badge, EmptyState } from '@shape-construction/arch-ui';
import { SHAPE, SIZE, THEME } from '@shape-construction/arch-ui/src/Badge/Badge.types';
import { DiamondIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { upperCase } from 'change-case';
import classNames from 'clsx';
import React, { type KeyboardEvent, useState } from 'react';
import { SubscriptionPlanModal } from '../SubscriptionPlanModal/SubscriptionPlanModal';
import { type FeatureName, useFeatureLimit } from './useFeatureLimit';

export interface FeatureLimitState {
  featureEnabled: boolean;
  isLoading: boolean;
  onClick?: () => void;
}

export interface FeatureLimitProps {
  children: React.ReactNode | ((state: FeatureLimitState) => React.ReactNode);
  featureName: FeatureName;
  showIconWithBadge?: boolean;
  showBanner?: boolean | React.ReactNode;
}

export const FeatureLimits: React.FC<FeatureLimitProps> = ({
  children,
  featureName,
  showIconWithBadge = false,
  showBanner = false,
}): React.ReactElement => {
  const messages = useMessageGetter('featureLimits');
  const { featureEnabled, isLoading } = useFeatureLimit(featureName);

  const [openModal, setOpenModal] = useState(false);
  const handleModalClose = () => setOpenModal(false);
  const handleModalOpen = () => setOpenModal(true);
  const onReturnOrSpacePressed = (event: KeyboardEvent) => {
    event.preventDefault();

    const returnKey = 'Enter';
    const spaceKey = ' ';

    if (event.key === returnKey || event.key === spaceKey) {
      handleModalOpen();
    }
  };

  const buttonClassNames = classNames('flex items-center gap-x-2', {
    'rounded-full hover:bg-gray-50 mr-4': showIconWithBadge,
  });

  const childrenComponent = children instanceof Function ? children({ featureEnabled, isLoading }) : children;

  if (isLoading || featureEnabled) return <>{childrenComponent}</>;

  if (showBanner) {
    if (React.isValidElement(showBanner)) {
      return showBanner;
    }

    return (
      <EmptyState
        icon={<DiamondIcon className="h-12 w-12 text-green-500" />}
        title={messages('banner.title')}
        body={messages('banner.body')}
      />
    );
  }

  return (
    <>
      <div
        role="button"
        className={buttonClassNames}
        onClick={handleModalOpen}
        onKeyPress={onReturnOrSpacePressed}
        tabIndex={0}
      >
        <div className="pointer-events-none select-none" tabIndex={-1}>
          {childrenComponent}
        </div>
        {!showIconWithBadge && (
          <Badge label={upperCase(messages('badge'))} shape={SHAPE.BASIC} theme={THEME.GREEN} size={SIZE.SMALL} />
        )}
      </div>
      <SubscriptionPlanModal open={openModal} onClose={handleModalClose} />
    </>
  );
};
