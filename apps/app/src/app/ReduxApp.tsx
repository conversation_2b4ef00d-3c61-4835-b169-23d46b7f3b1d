import React from 'react';
import { Provider } from 'react-redux';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router';
import { PersistGate } from 'redux-persist/integration/react';

import { App } from './App';
import { useSentry } from './analytics/hooks/useSentry';
import { persistor, store } from './store';

const ReduxApp = React.memo(() => {
  useSentry();

  return (
    <Provider store={store}>
      <PersistGate loading={null} persistor={persistor}>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </PersistGate>
    </Provider>
  );
});

export default ReduxApp;
