const AUTHORIZATION_KEY = 'authorization';
const AUTHORIZATION_REFRESH_KEY = 'authorization-refresh';

export const getToken = () => localStorage.getItem(AUTHORIZATION_KEY);
export const setToken = (token: string) => localStorage.setItem(AUTHORIZATION_KEY, token);

export const getRefreshToken = () => localStorage.getItem(AUTHORIZATION_REFRESH_KEY);
export const setRefreshToken = (token: string) => localStorage.setItem(AUTHORIZATION_REFRESH_KEY, token);

export const clearAuthTokens = () => {
  localStorage.removeItem(AUTHORIZATION_KEY);
  localStorage.removeItem(AUTHORIZATION_REFRESH_KEY);
};

export const isTokenExpired = (token: string) => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expiration = payload.exp * 1_000; // expiration in milliseconds
    const now = Date.now();
    // Add a 30 second buffer before actual expiration
    const bufferTime = 30_000;
    const isExpired = expiration - bufferTime < now;
    return isExpired;
  } catch (error) {
    // Consider malformed tokens as expired
    return true;
  }
};
