import { getApiProjectsProjectIdWeeklyWorkPlans } from '@shape-construction/api/src';
import {
  getApiProjectsProjectIdShiftActivitiesQueryKey,
  getApiProjectsProjectIdShiftActivitiesShiftActivityIdQueryKey,
  getApiProjectsProjectIdWeeklyWorkPlansQueryKey,
  getApiProjectsProjectIdWeeklyWorkPlansQueryOptions,
  getApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderQueryKey,
  getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey,
  getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryKey,
  useDeleteApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId,
  useDeleteApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdScheduledDaysDate,
  useGetApiProjectsProjectIdWeeklyWorkPlans,
  useGetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder,
  useGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId,
  useGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities,
  useGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs,
  usePatchApiProjectsProjectIdShiftActivitiesShiftActivityId,
  usePatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder,
  usePatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId,
  usePostApiProjectsProjectIdWeeklyWorkPlans,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdScheduledDays,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish,
  usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore,
  usePutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId,
} from '@shape-construction/api/src/hooks';
import type {
  ProjectSchema,
  ShiftActivitySchema,
  WeeklyWorkPlanActivityListSchema,
  WeeklyWorkPlanActivitySchema,
  WeeklyWorkPlanSchema,
} from '@shape-construction/api/src/types';
import { type QueryClient, infiniteQueryOptions, useQueryClient } from '@tanstack/react-query';
import { produce } from 'immer';

// queries
export const useWeeklyWorkPlansList = useGetApiProjectsProjectIdWeeklyWorkPlans;

export const getWeeklyWorkPlansInfiniteQueryOptions = (
  ...args: Parameters<typeof getApiProjectsProjectIdWeeklyWorkPlans>
) => {
  const [projectId, params, config] = args;
  return infiniteQueryOptions({
    queryKey: getApiProjectsProjectIdWeeklyWorkPlansQueryOptions(projectId, params, config).queryKey,
    queryFn: ({ pageParam, signal }) => {
      return getApiProjectsProjectIdWeeklyWorkPlans(projectId, { ...params, ...pageParam }, { ...config, signal });
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage.meta?.hasNextPage) return undefined;
      return { after: lastPage.meta?.lastEntryCursor || undefined };
    },
    initialPageParam: { after: undefined as undefined | string },
  });
};

export const useWeeklyWorkPlan = useGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId;

export const useWeeklyWorkPlanActivities = useGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities;

export const useWeeklyWorkPlanProgressLogs = useGetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs;

// mutations
export const useCreateWeeklyWorkPlans = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlans({
    mutation: {
      onSettled: (_, __, { projectId }) =>
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansQueryKey(projectId),
        }),
    },
  });
};

export const useUpdateWeeklyWorkPlan = () => {
  const queryClient = useQueryClient();

  return usePutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId({
    mutation: {
      onSettled: (_, __, { projectId, weeklyWorkPlanId }) => {
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansQueryKey(projectId),
        });
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryKey(projectId, weeklyWorkPlanId),
        });
      },
    },
  });
};

export const useArchiveWeeklyWorkPlan = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdArchive({
    mutation: {
      onSettled: (_, __, { projectId, weeklyWorkPlanId }) => {
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansQueryKey(projectId),
        });
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryKey(projectId, weeklyWorkPlanId),
        });
      },
    },
  });
};

export const useRestoreWeeklyWorkPlan = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdRestore({
    mutation: {
      onSettled: (_, __, { projectId, weeklyWorkPlanId }) => {
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansQueryKey(projectId),
        });
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryKey(projectId, weeklyWorkPlanId),
        });
      },
    },
  });
};

export const useCreateWeeklyWorkPlanActivity = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities({
    mutation: {
      onSettled: (_, __, { projectId, weeklyWorkPlanId }) =>
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(
            projectId,
            weeklyWorkPlanId
          ),
        }),
    },
  });
};

export const useUpdateWeeklyWorkPlanActivity = () => {
  const queryClient = useQueryClient();

  return usePatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId({
    mutation: {
      onSuccess: (updatedActivity: WeeklyWorkPlanActivitySchema, { projectId, weeklyWorkPlanId }) => {
        const oldPlanActivitiesData = queryClient.getQueryData<WeeklyWorkPlanActivityListSchema>(
          getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(projectId, weeklyWorkPlanId)
        );
        if (!oldPlanActivitiesData) return;

        const newActivities = oldPlanActivitiesData.entries.map((oldPlanActivity) =>
          oldPlanActivity.id === updatedActivity.id ? updatedActivity : oldPlanActivity
        );

        queryClient.setQueryData<WeeklyWorkPlanActivityListSchema>(
          getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(projectId, weeklyWorkPlanId),
          { entries: newActivities }
        );
      },
      mutationKey: ['update-weekly-work-plan-activities'],
    },
  });
};

export const useUpdateShiftActivity = (weeklyWorkPlanId: string) => {
  const queryClient = useQueryClient();

  return usePatchApiProjectsProjectIdShiftActivitiesShiftActivityId({
    mutation: {
      onSuccess: (updatedShiftActivity: ShiftActivitySchema, { projectId, shiftActivityId }) => {
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdShiftActivitiesQueryKey(projectId),
        });
        queryClient.setQueryData(
          getApiProjectsProjectIdShiftActivitiesShiftActivityIdQueryKey(projectId, updatedShiftActivity.id),
          updatedShiftActivity
        );

        const oldPlanActivitiesData = queryClient.getQueryData<WeeklyWorkPlanActivityListSchema>(
          getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(projectId, weeklyWorkPlanId)
        );
        if (!oldPlanActivitiesData) return;

        const newActivities = oldPlanActivitiesData.entries.map((oldPlanActivity) =>
          oldPlanActivity.shiftActivity.id === updatedShiftActivity.id
            ? {
                ...oldPlanActivity,
                shiftActivity: updatedShiftActivity,
              }
            : oldPlanActivity
        );

        queryClient.setQueryData<WeeklyWorkPlanActivityListSchema>(
          getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(projectId, weeklyWorkPlanId),
          { entries: newActivities }
        );
      },
      mutationKey: ['update-weekly-work-plan-activities'],
    },
  });
};

const updateScheduledDaysCache = (
  queryClient: QueryClient,
  projectId: ProjectSchema['id'],
  weeklyWorkPlanId: WeeklyWorkPlanSchema['id'],
  activityId: WeeklyWorkPlanActivitySchema['id'],
  updateDays: (days: WeeklyWorkPlanActivitySchema['scheduledDays']) => WeeklyWorkPlanActivitySchema['scheduledDays']
) =>
  queryClient.setQueryData<WeeklyWorkPlanActivityListSchema>(
    getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(projectId, weeklyWorkPlanId),
    (oldPlanActivitiesData) =>
      produce(oldPlanActivitiesData, (data) => {
        if (!data) return;
        const activity = data.entries.find((a) => a.id === activityId);
        if (activity) activity.scheduledDays = updateDays(activity.scheduledDays);
      })
  );

export const useCreateWeeklyWorkPlanActivitySchedule = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdScheduledDays({
    mutation: {
      onMutate({ projectId, weeklyWorkPlanId, activityId, data }) {
        updateScheduledDaysCache(queryClient, projectId, weeklyWorkPlanId, activityId, (days) =>
          Array.from(new Set([...days, data.date]))
        );
      },
      onError(_, { projectId, weeklyWorkPlanId, activityId, data }) {
        // Remove added day on error
        updateScheduledDaysCache(queryClient, projectId, weeklyWorkPlanId, activityId, (days) =>
          days.filter((d) => d !== data.date)
        );
      },
      mutationKey: ['update-weekly-work-plan-activities'],
    },
  });
};

export const useDeleteWeeklyWorkPlanActivitySchedule = () => {
  const queryClient = useQueryClient();
  return useDeleteApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdScheduledDaysDate({
    mutation: {
      onMutate({ projectId, weeklyWorkPlanId, activityId, date }) {
        updateScheduledDaysCache(queryClient, projectId, weeklyWorkPlanId, activityId, (days) =>
          days.filter((d) => d !== date)
        );
      },
      onError(_, { projectId, weeklyWorkPlanId, activityId, date }) {
        // Add day back on error
        updateScheduledDaysCache(queryClient, projectId, weeklyWorkPlanId, activityId, (days) =>
          Array.from(new Set([...days, date]))
        );
      },
    },
  });
};

export const usePublishLookbackWeeklyWorkPlan = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdClose({
    mutation: {
      onSettled: (_, __, { projectId, weeklyWorkPlanId }) => {
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansQueryKey(projectId),
        });
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryKey(projectId, weeklyWorkPlanId),
        });
      },
    },
  });
};

export const useDeleteWeeklyWorkPlanActivity = () => {
  const queryClient = useQueryClient();

  return useDeleteApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId({
    mutation: {
      onSuccess: (_, { projectId, weeklyWorkPlanId, activityId }) => {
        const oldPlanActivitiesData = queryClient.getQueryData<WeeklyWorkPlanActivityListSchema>(
          getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(projectId, weeklyWorkPlanId)
        );
        if (!oldPlanActivitiesData) return;

        const newActivities = oldPlanActivitiesData.entries.filter(
          (oldPlanActivity) => oldPlanActivity.id !== activityId
        );

        queryClient.setQueryData<WeeklyWorkPlanActivityListSchema>(
          getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(projectId, weeklyWorkPlanId),
          { entries: newActivities }
        );
      },
    },
  });
};

export const useDuplicateWeeklyWorkPlan = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdDuplicate({
    mutation: {
      onSettled: (_, __, { projectId }) => {
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansQueryKey(projectId),
        });
      },
    },
  });
};

export const useExportWeeklyWorkPlan = usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdExport;

export const useExportWeeklyWorkPlanLookback = usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdLookbackExport;

export const usePublishWeeklyWorkPlan = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish({
    mutation: {
      onSettled: (_, __, { projectId, weeklyWorkPlanId }) => {
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansQueryKey(projectId),
        });
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryKey(projectId, weeklyWorkPlanId),
        });
      },
    },
  });
};

export const useSortWeeklyWorkPlanActivities = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort({
    mutation: {
      async onSettled(_data, _error, { projectId, weeklyWorkPlanId }) {
        const queryKey = getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(
          projectId,
          weeklyWorkPlanId
        );
        await queryClient.invalidateQueries({ queryKey });
      },
      mutationKey: ['update-weekly-work-plan-activities'],
    },
  });
};

export const useCreateBatchWeeklyWorkPlanActivities = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesBatch({
    mutation: {
      onSettled: (_, __, { projectId, weeklyWorkPlanId }) =>
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryKey(
            projectId,
            weeklyWorkPlanId
          ),
        }),
    },
  });
};

export const useShiftActivitiesFinderOptions = useGetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder;

export const useUpdateShiftActivitiesFinderOptions = () => {
  const queryClient = useQueryClient();

  return usePatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder({
    mutation: {
      onSettled: (_, __, { projectId }) =>
        queryClient.invalidateQueries({
          queryKey: getApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderQueryKey(projectId),
        }),
    },
  });
};
