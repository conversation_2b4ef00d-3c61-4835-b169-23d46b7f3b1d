import {
  getApiProjectsProjectIdControlCenterChangeSignalsDowntimes,
  getApiProjectsProjectIdControlCenterChangeSignalsIssues,
} from '@shape-construction/api/src';
import {
  getApiProjectsProjectIdControlCenterChangeSignalsDowntimesSuspenseQueryOptions,
  getApiProjectsProjectIdControlCenterChangeSignalsIssuesSuspenseQueryOptions,
  usePostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate,
  usePostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDelete,
} from '@shape-construction/api/src/hooks';
import { infiniteQueryOptions, useQueryClient } from '@tanstack/react-query';
import { getPotentialChangesInfiniteQueryOptions } from './commercial-tracker';

export const getChangeSignalsInfiniteQueryOptions = (
  ...args: Parameters<typeof getApiProjectsProjectIdControlCenterChangeSignalsIssues>
) => {
  const [projectId, params] = args;
  return infiniteQueryOptions({
    queryKey: getApiProjectsProjectIdControlCenterChangeSignalsIssuesSuspenseQueryOptions(projectId, params).queryKey,
    queryFn: ({ pageParam }) => {
      return getApiProjectsProjectIdControlCenterChangeSignalsIssues(projectId, {
        ...params,
        after: pageParam || undefined,
      });
    },
    getNextPageParam: (lastPage) => {
      const { hasNextPage, lastEntryCursor } = lastPage.meta;
      return hasNextPage ? lastEntryCursor : undefined;
    },
    initialPageParam: '',
    select: (cache) => ({
      signals: cache.pages.flatMap((page) => page.entries),
      total: cache.pages[0]?.meta?.total,
    }),
  });
};

export const useLinkChangeSignals = () => {
  return usePostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate();
};

export const useUnlinkChangeSignals = () => {
  const queryClient = useQueryClient();

  return usePostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDelete({
    mutation: {
      onSuccess: (_, { projectId }) => {
        queryClient.invalidateQueries(getPotentialChangesInfiniteQueryOptions(projectId));
      },
    },
  });
};

export const getChangeSignalsDowntimeInfiniteQueryOptions = (
  ...args: Parameters<typeof getApiProjectsProjectIdControlCenterChangeSignalsDowntimes>
) => {
  const [projectId, params] = args;
  return infiniteQueryOptions({
    queryKey: getApiProjectsProjectIdControlCenterChangeSignalsDowntimesSuspenseQueryOptions(projectId, params)
      .queryKey,
    queryFn: ({ pageParam }) => {
      return getApiProjectsProjectIdControlCenterChangeSignalsDowntimes(projectId, {
        ...params,
        after: pageParam || undefined,
      });
    },
    getNextPageParam: (lastPage) => {
      const { hasNextPage, lastEntryCursor } = lastPage.meta;
      return hasNextPage ? lastEntryCursor : undefined;
    },
    initialPageParam: '',
    select: (cache) => ({
      signals: cache.pages.flatMap((page) => page.entries),
      total: cache.pages[0]?.meta?.total,
    }),
  });
};
