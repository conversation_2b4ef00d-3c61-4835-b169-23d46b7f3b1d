import { isTokenExpired } from './authorization';

describe('authorization', () => {
  describe('isTokenExpired', () => {
    it('returns false for a valid non-expired token', () => {
      // Create a token that expires in 1 hour
      const payload = {
        exp: Math.floor(Date.now() / 1000) + 3600,
      };
      const token = `header.${btoa(JSON.stringify(payload))}.signature`;
      expect(isTokenExpired(token)).toBe(false);
    });

    it('returns true for an expired token', () => {
      // Create a token that expired 1 hour ago
      const payload = {
        exp: Math.floor(Date.now() / 1000) - 3600,
      };
      const token = `header.${btoa(JSON.stringify(payload))}.signature`;
      expect(isTokenExpired(token)).toBe(true);
    });

    it('returns true for a malformed token', () => {
      expect(isTokenExpired('invalid-token')).toBe(true);
      expect(isTokenExpired('header.invalid-base64.signature')).toBe(true);
      expect(isTokenExpired('header.')).toBe(true);
    });

    it('considers a token expired 30 seconds before actual expiration', () => {
      // Create a token that expires in 20 seconds (should be considered expired due to 30s buffer)
      const payload = {
        exp: Math.floor(Date.now() / 1000) + 20,
      };
      const token = `header.${btoa(JSON.stringify(payload))}.signature`;
      expect(isTokenExpired(token)).toBe(true);
    });
  });
});
