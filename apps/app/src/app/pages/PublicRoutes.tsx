import React from 'react';
import { Navigate, Route, Routes, useLocation } from 'react-router';

import { redirectParamBlackList } from 'app/hooks/usePostLoginRedirect';
import { Auth } from 'app/pages/auth/Auth';
import { ConfirmEmail } from 'app/pages/confirm-email/ConfirmEmail';
import { ForgotPassword } from 'app/pages/forgot-password/ForgotPassword';
import { Login } from 'app/pages/login/Login';
import { SignupWithProvider } from 'app/pages/signup-provider/SignupWithProvider';
import { Signup } from 'app/pages/signup/Signup';
import { SSO } from 'app/pages/sso/SSO';

const PublicRoutes = () => {
  const { pathname, search } = useLocation();
  const redirectPath = redirectParamBlackList.includes(pathname)
    ? '/auth'
    : `/auth?redirect=${encodeURIComponent(pathname + search)}`;

  return (
    <Routes>
      <Route path="/auth" element={<Auth />} />
      <Route path="/login" element={<Login />} />
      <Route path="/sso" element={<SSO />} />
      <Route path="/signup" element={<Signup />} />
      <Route path="/signup-provider" element={<SignupWithProvider />} />

      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/confirm-email" element={<ConfirmEmail />} />

      <Route path="*" element={<Navigate to={redirectPath} replace />} />
    </Routes>
  );
};

export default PublicRoutes;
