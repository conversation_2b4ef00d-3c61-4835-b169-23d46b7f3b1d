import {
  dataHealthScoreFactory,
  dataHealthScoresMetadataFactory,
} from '@shape-construction/api/factories/dataHealthScores';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { teamFactory } from '@shape-construction/api/factories/teams';
import { getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler } from '@shape-construction/api/handlers-factories/projects/dashboard';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { getApiProjectsProjectIdTeamsTeamIdMockHandler } from '@shape-construction/api/handlers-factories/projects/teams';
import type { DataHealthDashboardScoreListSchema } from '@shape-construction/api/src/types';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen } from 'tests/test-utils';
import { DataHealthProvider } from './DataHealthContext';
import { DataHealthHeader } from './DataHealthHeader';

describe('<DataHealthHeader />', () => {
  it('renders the component', async () => {
    const teamMember1 = teamMemberFactory();
    const teamMember2 = teamMemberFactory();
    const teamMember3 = teamMemberFactory();
    const teamMembers = [teamMember1, teamMember2, teamMember3];
    const healthScore1 = dataHealthScoreFactory({ teamMemberId: teamMember1.id });
    const healthScore2 = dataHealthScoreFactory({ teamMemberId: teamMember2.id });
    const healthScore3 = dataHealthScoreFactory({ teamMemberId: teamMember3.id });
    const healthScores: DataHealthDashboardScoreListSchema = {
      metadata: dataHealthScoresMetadataFactory({
        seriesLength: 3,
      }),
      entries: [healthScore1, healthScore2, healthScore3],
    };
    server.use(
      getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler(() => healthScores),
      getApiProjectsProjectIdPeopleMockHandler(() => teamMembers)
    );

    render(
      <DataHealthProvider recordType="shift_reports" seriesLength={3}>
        <DataHealthHeader />
      </DataHealthProvider>,
      { pageData: { project: projectFactory() } }
    );

    expect(await screen.findByText('dataBook.page.heatmapDashboard.heatmap.title')).toBeInTheDocument();
  });

  describe('when entries are empty', () => {
    it('does not display the filter panel', async () => {
      const team = teamFactory({ displayName: 'Logistics team' });
      const teamMember = teamMemberFactory({ id: 74 });
      server.use(
        getApiProjectsProjectIdTeamsTeamIdMockHandler(() => team),
        getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler(),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
      );

      render(
        <DataHealthProvider recordType="shift_reports" seriesLength={3}>
          <DataHealthHeader />
        </DataHealthProvider>,
        { pageData: { project: projectFactory() } }
      );

      expect(
        await screen.findByRole('heading', { name: 'dataBook.page.heatmapDashboard.heatmap.title' })
      ).toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'dataBook.page.heatmapDashboard.heatmap.navigateNextLabel' })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('button', { name: 'dataBook.page.heatmapDashboard.heatmap.navigatePreviousLabel' })
      ).not.toBeInTheDocument();
    });
  });
});
