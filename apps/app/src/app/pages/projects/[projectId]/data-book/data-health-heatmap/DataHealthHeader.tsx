import { LoadingSpinner } from 'app/components/Loading/Loading';
import React from 'react';
import { PerformanceMatrixHeading } from '../components/PerformanceMatrix/PerformanceMatrixHeading';
import { PerformanceMatrixPanel } from '../components/PerformanceMatrix/PerformanceMatrixPanel';
import { useDataHealth } from './DataHealthContext';

export const DataHealthHeader = () => {
  const { metadata, isLoading, isFetching, loadPreviousSeries, loadNextSeries, data } = useDataHealth();
  const isPanelVisible = Boolean(data?.length);

  if (isLoading || !metadata) return <LoadingSpinner variant="screen" />;

  return (
    <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center py-3 px-4">
      <PerformanceMatrixHeading />
      {isPanelVisible && (
        <PerformanceMatrixPanel
          onPrevious={loadPreviousSeries}
          onNext={loadNextSeries}
          metadata={metadata}
          isFetching={isFetching}
        />
      )}
    </div>
  );
};
