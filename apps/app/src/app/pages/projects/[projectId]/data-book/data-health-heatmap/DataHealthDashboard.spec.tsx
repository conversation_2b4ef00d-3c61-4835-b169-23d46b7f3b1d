import {
  dataHealthScoreFactory,
  dataHealthScoresFactory,
  dataHealthScoresMetadataFactory,
} from '@shape-construction/api/factories/dataHealthScores';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler } from '@shape-construction/api/handlers-factories/projects/dashboard';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import type { DataHealthDashboardScoreListSchema } from '@shape-construction/api/src/types';
import { mediaQueryOptions } from '@shape-construction/arch-ui/src/utils/breakpoints';
import React from 'react';
import { server, waitForRequest } from 'tests/mock-server';
import { createMatchMedia, render, screen, waitFor } from 'tests/test-utils';
import { DataHealthProvider } from './DataHealthContext';
import { DataHealthDashboard } from './DataHealthDashboard';

describe('<DataHealthDashboard />', () => {
  it('renders the component', async () => {
    const teamMember1 = teamMemberFactory();
    const teamMember2 = teamMemberFactory();
    const teamMember3 = teamMemberFactory();
    const teamMembers = [teamMember1, teamMember2, teamMember3];
    const healthScore1 = dataHealthScoreFactory({ teamMemberId: teamMember1.id });
    const healthScore2 = dataHealthScoreFactory({ teamMemberId: teamMember2.id });
    const healthScore3 = dataHealthScoreFactory({ teamMemberId: teamMember3.id });
    const healthScores: DataHealthDashboardScoreListSchema = {
      metadata: dataHealthScoresMetadataFactory({
        seriesLength: 3,
      }),
      entries: [healthScore1, healthScore2, healthScore3],
    };
    server.use(
      getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler(() => healthScores),
      getApiProjectsProjectIdPeopleMockHandler(() => teamMembers)
    );
    render(
      <DataHealthProvider recordType="shift_reports" seriesLength={3}>
        <DataHealthDashboard />
      </DataHealthProvider>,
      { pageData: { project: projectFactory() } }
    );
  });

  it('renders the data health dashboard', async () => {
    const teamMember1 = teamMemberFactory({ id: 74 });
    const teamMember2 = teamMemberFactory({ id: 75 });
    const teamMember3 = teamMemberFactory({ id: 76 });
    const teamMember4 = teamMemberFactory({ id: 77 });
    const teamMembers = [teamMember1, teamMember2, teamMember3, teamMember4];
    server.use(
      getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler(() => dataHealthScoresFactory()),
      getApiProjectsProjectIdPeopleMockHandler(() => teamMembers)
    );

    render(
      <DataHealthProvider recordType="shift_reports" seriesLength={3}>
        <DataHealthDashboard />
      </DataHealthProvider>,
      { pageData: { project: projectFactory() } }
    );

    expect(await screen.findAllByText(/%/)).toHaveLength(11);
    expect(await screen.findAllByText(/^-$/)).toHaveLength(5);
    expect(await screen.findAllByRole('status')).toHaveLength(5);
    expect(
      await screen.findAllByRole('button', {
        name: /^dataBook.page.heatmapDashboard.heatmap.cell.label-[0-9]{1,3}$/,
      })
    ).toHaveLength(16);
  });

  describe('when user is on a small screen', () => {
    it('requests scores with series length 3', async () => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.sm);
      const teamMember = teamMemberFactory({ id: 74 });
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler(),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
      );
      const scoresRequest = waitForRequest(
        'GET',
        '*/api/projects/:projectId/dashboards/data_health_scores/:recordType'
      );

      render(
        <DataHealthProvider recordType="shift_reports" seriesLength={3}>
          <DataHealthDashboard />
        </DataHealthProvider>,
        { pageData: { project: projectFactory() } }
      );

      await waitFor(async () => {
        const requestUrl = new URL((await scoresRequest).url);
        expect(requestUrl.searchParams.get('series_length')).toBe('3');
      });
    });
  });

  describe('when user is on a medium screen', () => {
    it('requests scores with series length 6', async () => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.md);
      const teamMember = teamMemberFactory({ id: 74 });
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler(),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
      );
      const scoresRequest = waitForRequest(
        'GET',
        '*/api/projects/:projectId/dashboards/data_health_scores/:recordType'
      );

      render(
        <DataHealthProvider recordType="shift_reports" seriesLength={6}>
          <DataHealthDashboard />
        </DataHealthProvider>,
        { pageData: { project: projectFactory() } }
      );

      await waitFor(async () => {
        const requestUrl = new URL((await scoresRequest).url);
        expect(requestUrl.searchParams.get('series_length')).toBe('6');
      });
    });
  });

  describe('when user is on a large screen', () => {
    it('requests scores with series length 9', async () => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.lg);
      const teamMember = teamMemberFactory({ id: 74 });
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler(),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
      );
      const scoresRequest = waitForRequest(
        'GET',
        '*/api/projects/:projectId/dashboards/data_health_scores/:recordType'
      );

      render(
        <DataHealthProvider recordType="shift_reports" seriesLength={9}>
          <DataHealthDashboard />
        </DataHealthProvider>,
        { pageData: { project: projectFactory() } }
      );

      await waitFor(async () => {
        const requestUrl = new URL((await scoresRequest).url);
        expect(requestUrl.searchParams.get('series_length')).toBe('9');
      });
    });
  });

  describe('when user is on an extra large screen', () => {
    it('requests scores with series length 12', async () => {
      window.matchMedia = createMatchMedia(mediaQueryOptions.xl);
      const teamMember = teamMemberFactory({ id: 74 });
      server.use(
        getApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeMockHandler(),
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember])
      );
      const scoresRequest = waitForRequest(
        'GET',
        '*/api/projects/:projectId/dashboards/data_health_scores/:recordType'
      );

      render(
        <DataHealthProvider recordType="shift_reports" seriesLength={12}>
          <DataHealthDashboard />
        </DataHealthProvider>,
        { pageData: { project: projectFactory() } }
      );

      await waitFor(async () => {
        const requestUrl = new URL((await scoresRequest).url);
        expect(requestUrl.searchParams.get('series_length')).toBe('12');
      });
    });
  });
});
