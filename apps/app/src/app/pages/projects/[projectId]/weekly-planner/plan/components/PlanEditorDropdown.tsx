import { useMessage, useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import { Divider, Dropdown, IconButton, Tooltip } from '@shape-construction/arch-ui';
import type { IconButtonProps } from '@shape-construction/arch-ui/src/Button/IconButton/IconButton';
import {
  EllipsisVerticalIcon,
  InboxArrowDownIcon,
  InboxOutIcon,
  PencilSquareIcon,
  Square2StackIcon,
} from '@shape-construction/arch-ui/src/Icons/solid';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useMediaQuery } from '@shape-construction/hooks';
import React from 'react';
import { PLAN_STATUS } from '../constants/constants';
import { SectionDate } from './PlanDetailLayoutSection/SectionDate';
import { Status } from './PlanDetailLayoutSection/Status';

export type PlanEditorDropdownProps = {
  plan: WeeklyWorkPlanSchema;
  onEdit: () => void;
  onDuplicate: () => void;
  onArchive: () => void;
  onRestore: () => void;
};

const DropdownButton: React.FC<Partial<IconButtonProps>> = React.forwardRef((props, ref) => {
  const isMediumScreen = useMediaQuery(breakpoints.up('md'));
  const tooltipMessage = useMessage('weeklyPlanner.workPlans.table.actions.tooltips.menu');
  return (
    <Tooltip.Root>
      <Tooltip.Trigger asChild>
        <IconButton
          size={isMediumScreen ? 'sm' : 'xs'}
          color="secondary"
          shape="square"
          variant="outlined"
          icon={EllipsisVerticalIcon}
          aria-label="plan-editor-actions"
          ref={ref}
          {...props}
        />
      </Tooltip.Trigger>
      <Tooltip.Content>{tooltipMessage}</Tooltip.Content>
    </Tooltip.Root>
  );
});

export const PlanEditorDropdown = ({ plan, onEdit, onDuplicate, onArchive, onRestore }: PlanEditorDropdownProps) => {
  const actionMessages = useMessageGetter('weeklyPlanner.workPlans.planEditor.header.actions');
  const tooltipMessages = useMessageGetter('weeklyPlanner.workPlans.table.actions.tooltips');

  return (
    <div className="flex items-start gap-2">
      <Dropdown.Root>
        <Dropdown.Trigger asChild>
          <DropdownButton />
        </Dropdown.Trigger>
        <Dropdown.Items>
          <div className="flex w-full flex-col">
            <div className="lg:hidden">
              <div className="flex flex-col px-4 py-3 gap-y-1">
                <h6 className="text-neutral text-base leading-5 font-semibold md:text-sm">{plan.title}</h6>
                <SectionDate startDate={plan.startDate} endDate={plan.endDate} />
                <div>
                  <Status status={plan.status} />
                </div>
              </div>
              <Divider orientation="horizontal" />
            </div>
            <Tooltip.Root>
              <Tooltip.Trigger>
                <Dropdown.Item
                  icon={PencilSquareIcon}
                  disabled={!plan.availableActions.edit}
                  onClick={() => {
                    onEdit();
                  }}
                >
                  {actionMessages('editPlanDetails')}
                </Dropdown.Item>
              </Tooltip.Trigger>
              <Tooltip.Content hidden={plan.availableActions.edit}>
                {plan.status === PLAN_STATUS.CLOSED
                  ? tooltipMessages('noEditClosed')
                  : tooltipMessages('noEditPermission')}
              </Tooltip.Content>
            </Tooltip.Root>
            <Tooltip.Root>
              <Tooltip.Trigger>
                <Dropdown.Item
                  icon={Square2StackIcon}
                  disabled={!plan.availableActions.duplicate}
                  onClick={() => {
                    onDuplicate();
                  }}
                >
                  {actionMessages('duplicate')}
                </Dropdown.Item>
              </Tooltip.Trigger>
              <Tooltip.Content hidden={plan.availableActions.duplicate}>
                {tooltipMessages('noEditPermission')}
              </Tooltip.Content>
            </Tooltip.Root>
            <div className="lg:block hidden">
              <Divider orientation="horizontal" />
            </div>
            {!plan.archived && (
              <Tooltip.Root>
                <Tooltip.Trigger>
                  <Dropdown.Item
                    icon={InboxArrowDownIcon}
                    disabled={!plan.availableActions.archive}
                    onClick={(e: React.MouseEvent<HTMLElement>) => {
                      e.stopPropagation();
                      onArchive();
                    }}
                  >
                    {actionMessages('archive')}
                  </Dropdown.Item>
                </Tooltip.Trigger>
                <Tooltip.Content hidden={plan.availableActions.archive}>
                  {tooltipMessages('noArchivePermission')}
                </Tooltip.Content>
              </Tooltip.Root>
            )}
            {plan.archived && (
              <Tooltip.Root>
                <Tooltip.Trigger>
                  <Dropdown.Item
                    icon={InboxOutIcon}
                    disabled={!plan.availableActions.restore}
                    onClick={(e: React.MouseEvent<HTMLElement>) => {
                      e.stopPropagation();
                      onRestore();
                    }}
                  >
                    {actionMessages('restore')}
                  </Dropdown.Item>
                </Tooltip.Trigger>
                <Tooltip.Content hidden={plan.availableActions.restore}>
                  {tooltipMessages('noRestorePermission')}
                </Tooltip.Content>
              </Tooltip.Root>
            )}
          </div>
        </Dropdown.Items>
      </Dropdown.Root>
    </div>
  );
};
