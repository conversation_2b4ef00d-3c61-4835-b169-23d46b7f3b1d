import type { useMessageGetter } from '@messageformat/react';
import {
  type WeeklyWorkPlanActivitySchema,
  type WeeklyWorkPlanSchema,
  weeklyWorkPlanActivityVarianceCategoriesEnum,
} from '@shape-construction/api/src/types';
import type { ButtonGroupOption } from '@shape-construction/arch-ui';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { parseDate } from '@shape-construction/utils/DateTime';
import type { VarianceCategoryItem, VarianceCategoryKey } from './[planId]/lookback/components/fields/types';
import type { ScheduledDaysItem, ScheduledDaysOptions, WeekdayOptions } from './plan/components/fields/types';
import { PLAN_STATUS } from './plan/constants/constants';

type ProgressNotLoggedMessageKey = 'missingProgressLog' | 'noProgressLog';

export const isVarianceMissing = (row: WeeklyWorkPlanActivitySchema): boolean => {
  const missingCategory = !row.varianceCategory;
  const missingRemarks = !row.varianceRemarks?.trim();
  const progressAct = row.percentageCompleted ?? 0;
  const progressExp = row.expectedPercentageCompleted ?? 0;
  const hasVariance = progressAct < progressExp;
  return hasVariance && (missingCategory || missingRemarks);
};

export const getScheduledDaysOptions = (
  planStartDate: WeeklyWorkPlanSchema['startDate'],
  planEndDate: WeeklyWorkPlanSchema['endDate'],
  weekdayOptions: WeekdayOptions
): ScheduledDaysOptions => {
  const startDate = parseDate(planStartDate);
  const daysDiff = parseDate(planEndDate).diff(startDate, 'days');
  const dayIndices = Array.from({ length: daysDiff + 1 }, (_, i) => i);

  const startDay = startDate.day();
  return dayIndices.map((i) => {
    const weekday = (startDay + i) % 7;
    return {
      label: weekdayOptions[weekday]?.label,
      value: startDate.add(i, 'days').format('YYYY-MM-DD'),
    };
  });
};

export const getTooltipMessage = (
  isEditAvailable: boolean,
  isPlanClosed: boolean,
  messages: ReturnType<typeof useMessageGetter>,
  messageKey: ProgressNotLoggedMessageKey
): string => {
  if (isPlanClosed) {
    return messages('planClosed');
  }
  if (!isEditAvailable) {
    return messages('noPermissions');
  }
  return messages(messageKey);
};

export const getProgressLogButtonOption = (
  day: ScheduledDaysItem,
  plan: WeeklyWorkPlanSchema,
  planActivity: WeeklyWorkPlanActivitySchema,
  messages: ReturnType<typeof useMessageGetter>,
  openCreateProgressDrawer: (selectedDate: string) => void,
  openSeeProgressDrawer: (selectedDate: string) => void
): ButtonGroupOption => {
  const { loggedDays, scheduledDays } = planActivity;
  const dayIsLogged = loggedDays.includes(day.value);
  const dayIsScheduled = scheduledDays.includes(day.value);
  const isPlanClosed = plan.status === PLAN_STATUS.CLOSED;
  const isEditAvailable = plan?.availableActions?.edit === true;
  const isReadOnly = isPlanClosed || !isEditAvailable;
  const id = `${day.value}-${planActivity.id}`;

  if (dayIsLogged) {
    return {
      id,
      label: day.label,
      color: 'success',
      onClick: () => openSeeProgressDrawer(day.value),
      tooltip: messages('checkProgressLog'),
      disabled: false,
      buttonClassnames: 'text-gray-700 w-16',
    };
  }

  if (dayIsScheduled && !dayIsLogged) {
    return {
      id,
      label: day.label,
      color: 'warning',
      onClick: () => (isReadOnly ? {} : openCreateProgressDrawer(day.value)),
      tooltip: getTooltipMessage(isEditAvailable, isPlanClosed, messages, 'missingProgressLog'),
      disabled: isReadOnly,
      icon: ExclamationTriangleIcon,
      buttonClassnames: 'text-gray-700 w-16',
      iconClassnames: 'text-gray-700',
    };
  }

  return {
    id,
    label: day.label,
    color: 'secondary',
    onClick: () => (isReadOnly ? {} : openCreateProgressDrawer(day.value)),
    tooltip: getTooltipMessage(isEditAvailable, isPlanClosed, messages, 'noProgressLog'),
    disabled: isReadOnly,
    buttonClassnames: 'w-16',
  };
};

export const getVarianceCategories = (messages: ReturnType<typeof useMessageGetter>): VarianceCategoryItem[] => {
  const categoryKeys = Object.keys(weeklyWorkPlanActivityVarianceCategoriesEnum);
  const categoryItems: VarianceCategoryItem[] = categoryKeys.map((key) => ({
    label: messages(weeklyWorkPlanActivityVarianceCategoriesEnum[key as VarianceCategoryKey]),
    value: weeklyWorkPlanActivityVarianceCategoriesEnum[key as VarianceCategoryKey],
  }));
  return [
    {
      label: '',
      value: '',
    },
    ...categoryItems,
  ];
};

export const getVarianceCategoryLabels = (messages: ReturnType<typeof useMessageGetter>): Map<string, string> => {
  const categoryKeys = Object.keys(weeklyWorkPlanActivityVarianceCategoriesEnum);
  return new Map(
    categoryKeys.map((key) => {
      const value = weeklyWorkPlanActivityVarianceCategoriesEnum[key as VarianceCategoryKey];
      return [value, messages(value)];
    })
  );
};
