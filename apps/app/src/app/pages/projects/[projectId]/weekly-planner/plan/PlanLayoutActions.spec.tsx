import { queuedTaskFileDownloadFactory } from '@shape-construction/api/factories/exports';
import { projectFactory } from '@shape-construction/api/factories/projects';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory } from '@shape-construction/api/factories/users';
import {
  weeklyWorkPlanAvailableActions,
  weeklyWorkPlanFactory,
} from '@shape-construction/api/factories/weeklyWorkPlans';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import {
  getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler,
  postApiProjectsProjectIdWeeklyWorkPlansPlanIdCloseMockHandler,
} from '@shape-construction/api/handlers-factories/projects/weekly-work-plans';
import useExports from 'app/components/Exports/useExports';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { PlanLayoutActions } from './PlanLayoutActions';
import { PLAN_STATUS } from './constants/constants';

jest.mock('app/components/Exports/useExports', () => jest.fn());

describe('<PlanLayoutActions />', () => {
  beforeEach(() => {
    (useExports as jest.Mock).mockReturnValue({ addToPendingExports: jest.fn() });
  });

  describe('export button', () => {
    describe('when user is the author', () => {
      it('shows the export button', async () => {
        const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
        const weeklyWorkPlan = weeklyWorkPlanFactory({ teamMemberId: teamMember.id });
        const project = projectFactory({ id: 'project-0' });
        server.use(
          getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
        );

        render(
          <PlanLayoutActions
            plan={weeklyWorkPlan}
            projectId={project.id}
            openExportModal={jest.fn}
            openPlanModal={jest.fn}
            openDuplicatePlanModal={jest.fn}
            showNewProgressLogButton
            openDrawer={jest.fn}
          />
        );

        expect(
          await screen.findByRole('button', {
            name: 'weeklyPlanner.workPlans.planEditor.header.actions.export.label',
          })
        ).toBeInTheDocument();
      });
    });
  });

  describe('edit plan', () => {
    describe('when the user does not have permission to edit the plan', () => {
      it('disables the edit action item', async () => {
        const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
        const weeklyWorkPlan = weeklyWorkPlanFactory({
          teamMemberId: teamMember.id,
          status: PLAN_STATUS.REVIEWING,
          availableActions: weeklyWorkPlanAvailableActions({ edit: false }),
        });
        const project = projectFactory({ id: 'project-0' });
        server.use(
          getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
        );
        const { user } = render(
          <PlanLayoutActions
            plan={weeklyWorkPlan}
            projectId={project.id}
            openExportModal={jest.fn}
            openPlanModal={jest.fn}
            openDuplicatePlanModal={jest.fn}
            showNewProgressLogButton
            openDrawer={jest.fn}
          />
        );

        await user.click(
          await screen.findByRole('button', {
            name: 'plan-editor-actions',
          })
        );

        expect(screen.getByRole('menu')).toBeInTheDocument();
        expect(
          screen.getByRole('menuitem', {
            name: 'weeklyPlanner.workPlans.planEditor.header.actions.editPlanDetails',
          })
        ).toHaveAttribute('aria-disabled', 'true');
      });
    });

    describe('when the user has permission to edit the plan', () => {
      it('enables the edit action item', async () => {
        const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
        const weeklyWorkPlan = weeklyWorkPlanFactory({
          teamMemberId: teamMember.id,
          status: PLAN_STATUS.REVIEWING,
          availableActions: weeklyWorkPlanAvailableActions({ edit: true }),
        });
        const project = projectFactory({ id: 'project-0' });
        server.use(
          getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
        );
        const { user } = render(
          <PlanLayoutActions
            plan={weeklyWorkPlan}
            projectId={project.id}
            openExportModal={jest.fn}
            openPlanModal={jest.fn}
            openDuplicatePlanModal={jest.fn}
            showNewProgressLogButton
            openDrawer={jest.fn}
          />
        );

        await user.click(
          await screen.findByRole('button', {
            name: 'plan-editor-actions',
          })
        );

        expect(screen.getByRole('menu')).toBeInTheDocument();
        expect(
          screen.getByRole('menuitem', {
            name: 'weeklyPlanner.workPlans.planEditor.header.actions.editPlanDetails',
          })
        ).not.toHaveAttribute('aria-disabled', 'true');
      });
    });
  });

  describe('duplicate plan', () => {
    describe('when the user does not have permission to duplicate the plan', () => {
      it('disables the duplicate action item', async () => {
        const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
        const weeklyWorkPlan = weeklyWorkPlanFactory({
          teamMemberId: teamMember.id,
          status: PLAN_STATUS.REVIEWING,
          availableActions: weeklyWorkPlanAvailableActions({ duplicate: false }),
        });
        const project = projectFactory({ id: 'project-0' });
        server.use(
          getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
        );
        const { user } = render(
          <PlanLayoutActions
            plan={weeklyWorkPlan}
            projectId={project.id}
            openExportModal={jest.fn}
            openPlanModal={jest.fn}
            openDuplicatePlanModal={jest.fn}
            showNewProgressLogButton
            openDrawer={jest.fn}
          />
        );

        await user.click(
          await screen.findByRole('button', {
            name: 'plan-editor-actions',
          })
        );

        expect(screen.getByRole('menu')).toBeInTheDocument();
        expect(
          screen.getByRole('menuitem', {
            name: 'weeklyPlanner.workPlans.planEditor.header.actions.duplicate',
          })
        ).toHaveAttribute('aria-disabled', 'true');
      });
    });

    describe('when the user has permission to duplicate the plan', () => {
      it('enables the duplicate action item', async () => {
        const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
        const weeklyWorkPlan = weeklyWorkPlanFactory({
          teamMemberId: teamMember.id,
          status: PLAN_STATUS.REVIEWING,
          availableActions: weeklyWorkPlanAvailableActions({ duplicate: true }),
        });
        const project = projectFactory({ id: 'project-0' });
        server.use(
          getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
        );
        const { user } = render(
          <PlanLayoutActions
            plan={weeklyWorkPlan}
            projectId={project.id}
            openExportModal={jest.fn}
            openPlanModal={jest.fn}
            openDuplicatePlanModal={jest.fn}
            showNewProgressLogButton
            openDrawer={jest.fn}
          />
        );

        await user.click(
          await screen.findByRole('button', {
            name: 'plan-editor-actions',
          })
        );

        expect(screen.getByRole('menu')).toBeInTheDocument();
        expect(
          screen.getByRole('menuitem', {
            name: 'weeklyPlanner.workPlans.planEditor.header.actions.duplicate',
          })
        ).not.toHaveAttribute('aria-disabled', 'true');
      });
    });
  });

  describe('new progress log button', () => {
    describe('when user is the author', () => {
      describe('when showNewProgressLogButton is true', () => {
        it('shows the new progress log button', async () => {
          const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
          const weeklyWorkPlan = weeklyWorkPlanFactory({ teamMemberId: teamMember.id });
          const project = projectFactory({ id: 'project-0' });
          server.use(
            getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
            getApiProjectsProjectIdMockHandler(() => project),
            getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
          );

          render(
            <PlanLayoutActions
              plan={weeklyWorkPlan}
              projectId={project.id}
              openExportModal={jest.fn}
              openPlanModal={jest.fn}
              openDuplicatePlanModal={jest.fn}
              showNewProgressLogButton
              openDrawer={jest.fn}
            />
          );

          expect(
            await screen.findByRole('button', {
              name: 'weeklyPlanner.workPlans.details.newProgressLogCTA',
            })
          ).toBeInTheDocument();
        });
      });
    });
  });

  describe('when the user can publish a lookback', () => {
    it('renders the publish lookback button', async () => {
      const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
      const project = projectFactory({ id: 'project-0' });
      const weeklyWorkPlan = weeklyWorkPlanFactory({
        teamMemberId: teamMember.id,
        availableActions: weeklyWorkPlanAvailableActions({ close: true }),
      });
      server.use(getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]));

      render(
        <PlanLayoutActions
          plan={weeklyWorkPlan}
          projectId={project.id}
          openExportModal={jest.fn}
          openPlanModal={jest.fn}
          openDuplicatePlanModal={jest.fn}
          showNewProgressLogButton={false}
          openDrawer={jest.fn}
        />
      );

      expect(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.table.actions.publishLookback',
        })
      ).toBeInTheDocument();
    });

    it('adds the download to queue on publish', async () => {
      const mockAddToPendingExports = jest.fn();
      (useExports as jest.Mock).mockReturnValue({ addToPendingExports: mockAddToPendingExports });
      const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
      const project = projectFactory({ id: 'project-0' });
      const weeklyWorkPlan = weeklyWorkPlanFactory({
        teamMemberId: teamMember.id,
        availableActions: weeklyWorkPlanAvailableActions({ close: true }),
      });
      const mockTask = queuedTaskFileDownloadFactory({ id: 'mock-task' });
      server.use(
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
        postApiProjectsProjectIdWeeklyWorkPlansPlanIdCloseMockHandler(() => ({
          exportQueuedTask: mockTask,
          weeklyWorkPlan: weeklyWorkPlan,
        }))
      );
      render(
        <PlanLayoutActions
          plan={weeklyWorkPlan}
          projectId={project.id}
          openExportModal={jest.fn}
          openPlanModal={jest.fn}
          openDuplicatePlanModal={jest.fn}
          showNewProgressLogButton={false}
          openDrawer={jest.fn}
        />
      );

      screen
        .getByRole('button', {
          name: 'weeklyPlanner.workPlans.table.actions.publishLookback',
        })
        .click();
      userEvent.click(
        await screen.findByRole('button', {
          name: 'weeklyPlanner.workPlans.publishLookback.modal.confirmCTA',
        })
      );

      await waitFor(() => {
        expect(mockAddToPendingExports).toHaveBeenCalledWith(mockTask);
      });
    });
  });

  describe('when the user can publish a plan', () => {
    it('renders the publish plan button', async () => {
      const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
      const project = projectFactory({ id: 'project-0' });
      const weeklyWorkPlan = weeklyWorkPlanFactory({
        teamMemberId: teamMember.id,
        availableActions: weeklyWorkPlanAvailableActions({ publish: true }),
      });
      server.use(getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]));

      render(
        <PlanLayoutActions
          plan={weeklyWorkPlan}
          projectId={project.id}
          openExportModal={jest.fn}
          openPlanModal={jest.fn}
          openDuplicatePlanModal={jest.fn}
          showNewProgressLogButton={false}
          openDrawer={jest.fn}
        />
      );

      expect(
        screen.getByRole('button', {
          name: 'weeklyPlanner.workPlans.table.actions.publishPlan',
        })
      ).toBeInTheDocument();
    });
  });

  describe('when the user has permission to archive a plan', () => {
    it('enables the archive action item', async () => {
      const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
      const weeklyWorkPlan = weeklyWorkPlanFactory({
        teamMemberId: teamMember.id,
        status: PLAN_STATUS.REVIEWING,
        availableActions: weeklyWorkPlanAvailableActions({ archive: true }),
      });
      const project = projectFactory({ id: 'project-0' });
      server.use(
        getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
      );
      const { user } = render(
        <PlanLayoutActions
          plan={weeklyWorkPlan}
          projectId={project.id}
          openExportModal={jest.fn}
          openPlanModal={jest.fn}
          openDuplicatePlanModal={jest.fn}
          showNewProgressLogButton
          openDrawer={jest.fn}
        />
      );

      await user.click(
        await screen.findByRole('button', {
          name: 'plan-editor-actions',
        })
      );

      expect(screen.getByRole('menu')).toBeInTheDocument();
      expect(
        await screen.findByRole('menuitem', {
          name: 'weeklyPlanner.workPlans.planEditor.header.actions.archive',
        })
      ).not.toHaveAttribute('aria-disabled', 'true');
    });
  });

  describe('when plan is archived', () => {
    describe('when the user has permission to restore a plan', () => {
      it('enables the restore action item', async () => {
        const teamMember = teamMemberFactory({ id: 1, user: userBasicDetailsFactory({ id: '123' }) });
        const weeklyWorkPlan = weeklyWorkPlanFactory({
          teamMemberId: teamMember.id,
          status: PLAN_STATUS.REVIEWING,
          archived: true,
          availableActions: weeklyWorkPlanAvailableActions({ restore: true }),
        });
        const project = projectFactory({ id: 'project-0' });
        server.use(
          getApiProjectsProjectIdPeopleMockHandler(() => [teamMember]),
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
        );
        const { user } = render(
          <PlanLayoutActions
            plan={weeklyWorkPlan}
            projectId={project.id}
            openExportModal={jest.fn}
            openPlanModal={jest.fn}
            openDuplicatePlanModal={jest.fn}
            showNewProgressLogButton
            openDrawer={jest.fn}
          />
        );

        await user.click(
          await screen.findByRole('button', {
            name: 'plan-editor-actions',
          })
        );

        expect(screen.getByRole('menu')).toBeInTheDocument();
        expect(
          await screen.findByRole('menuitem', {
            name: 'weeklyPlanner.workPlans.planEditor.header.actions.restore',
          })
        ).not.toHaveAttribute('aria-disabled', 'true');
      });
    });
  });
});
