import type {
  GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsQueryParamsSchema,
  ShiftActivitySchema,
  WeeklyWorkPlanSchema,
} from '@shape-construction/api/src/types';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useWeeklyWorkPlanProgressLogs } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import { useParams } from 'react-router';

type Params = {
  planId: WeeklyWorkPlanSchema['id'];
};

type Args = {
  enabled: boolean;
  progressLogDate: GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsQueryParamsSchema['date'];
  shiftActivityId?: ShiftActivitySchema['id'];
  planId?: WeeklyWorkPlanSchema['id'];
};

export const useFindShiftActivityProgressLog = ({ enabled, progressLogDate, shiftActivityId, planId }: Args) => {
  const project = useCurrentProject();
  const { planId: paramPlanId } = useParams<Params>() as Params;
  const { data: progressLogsData } = useWeeklyWorkPlanProgressLogs(
    project.id,
    planId ?? paramPlanId,
    { date: progressLogDate },
    { query: { enabled: Boolean(enabled && progressLogDate && planId && shiftActivityId) } }
  );
  if (!enabled) return undefined;
  return progressLogsData?.entries?.find((log) => log.shiftActivityId === shiftActivityId);
};

export const useFindShiftActivityProgressLogs = ({ enabled, progressLogDate, shiftActivityId, planId }: Args) => {
  const project = useCurrentProject();
  const { planId: paramPlanId } = useParams<Params>() as Params;
  const { data: progressLogsData } = useWeeklyWorkPlanProgressLogs(
    project.id,
    planId ?? paramPlanId,
    { date: progressLogDate },
    { query: { enabled: Boolean(enabled && progressLogDate && planId && shiftActivityId) } }
  );
  if (!enabled) return undefined;

  return progressLogsData?.entries?.filter((log) => log.shiftActivityId === shiftActivityId);
};
