import { useMessageGetter } from '@messageformat/react';
import { EmptyState, Page } from '@shape-construction/arch-ui';
import { PlusCircleIcon } from '@shape-construction/arch-ui/src/Icons/outline';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useCurrentWeeklyWorkPlan } from 'app/contexts/currentWeeklyWorkPlan';
import {
  useWeeklyWorkPlanActivities,
  useWeeklyWorkPlanProgressLogs,
} from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import React, { useEffect } from 'react';
import { EditDisabledAlert } from './components/EditDisabledAlert';
import { ProgressDateSelector } from './components/progress/ProgressDateSelector';
import { ProgressExpansionPanel } from './components/progress/ProgressExpansionPanel';
import { usePlanActivitiesWithProgressLogs } from './hooks/usePlanActivitiesWithProgressLogs';
import { useProgressSelectedDate } from './hooks/useProgressSelectedDate';

export const ProgressLogs = () => {
  const messages = useMessageGetter('weeklyPlanner.workPlans.progressTracker');
  const project = useCurrentProject();
  const plan = useCurrentWeeklyWorkPlan();
  const { selectedDate, setSelectedDate } = useProgressSelectedDate();
  const { data: activitiesData } = useWeeklyWorkPlanActivities(project.id, plan.id);
  const { data: progressLogsData } = useWeeklyWorkPlanProgressLogs(
    project.id,
    plan.id,
    { date: selectedDate },
    { query: { enabled: Boolean(selectedDate) } }
  );
  const { noUpdates, withUpdates } = usePlanActivitiesWithProgressLogs(activitiesData, progressLogsData);

  useEffect(() => {
    return () => {
      setSelectedDate(undefined);
    };
  }, [setSelectedDate]);

  if (!plan || !activitiesData) return null;

  const hasNoActivities = activitiesData.entries?.length === 0;

  return (
    <Page.Body className="relative flex flex-col items-center h-full overflow-y-auto p-0 md:p-0">
      {plan?.availableActions?.edit !== true && !plan.archived && <EditDisabledAlert />}

      {hasNoActivities ? (
        <div className="w-full max-w-5xl flex flex-col justify-center gap-4 md:py-4 md:px-8 grow">
          <EmptyState
            icon={<PlusCircleIcon className="h-12 w-12 lg:w-14 lg:h-14" />}
            title={messages('emptyState.title')}
            body={messages('emptyState.description')}
          />
        </div>
      ) : (
        <div className="w-full max-w-5xl flex flex-col gap-4 pb-20 md:py-4 md:px-8">
          <ProgressDateSelector plan={plan} selectedDate={selectedDate} setSelectedDate={setSelectedDate} />

          <ProgressExpansionPanel
            title={messages('withoutUpdates')}
            plan={plan}
            planActivitiesWithProgressLogs={noUpdates}
          />

          <ProgressExpansionPanel
            title={messages('withUpdates')}
            plan={plan}
            planActivitiesWithProgressLogs={withUpdates}
          />
        </div>
      )}
    </Page.Body>
  );
};

export { ProgressLogs as Component };
