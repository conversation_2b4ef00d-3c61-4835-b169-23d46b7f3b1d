import type { useMessageGetter } from '@messageformat/react';
import {
  planActivityFactory,
  weeklyWorkPlanAvailableActions,
  weeklyWorkPlanFactory,
} from '@shape-construction/api/factories/weeklyWorkPlans';
import { ExclamationTriangleIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { PLAN_STATUS } from './plan/constants/constants';
import {
  getProgressLogButtonOption,
  getScheduledDaysOptions,
  getTooltipMessage,
  getVarianceCategories,
  getVarianceCategoryLabels,
  isVarianceMissing,
} from './utils';

describe('WeeklyPlannerUtils', () => {
  describe('isVarianceMissing', () => {
    describe('when category and marks are populated', () => {
      describe('when expected progress is less than actual progress', () => {
        it('returns false, indicating the variance is recorded', () => {
          const row = planActivityFactory({
            varianceCategory: 'weather_conditions',
            varianceRemarks: 'Adverse weather',
            expectedPercentageCompleted: 20,
            percentageCompleted: 40,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(false);
        });
      });

      describe('when expected progress is greater than actual progress', () => {
        it('returns false, indicating the variance is recorded', () => {
          const row = planActivityFactory({
            varianceCategory: 'weather_conditions',
            varianceRemarks: 'Adverse weather',
            expectedPercentageCompleted: 40,
            percentageCompleted: 20,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(false);
        });
      });

      describe('when expected progress equals actual progress', () => {
        it('returns false, indicating the variance is recorded', () => {
          const row = planActivityFactory({
            varianceCategory: 'weather_conditions',
            varianceRemarks: 'Adverse weather',
            expectedPercentageCompleted: 40,
            percentageCompleted: 40,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(false);
        });
      });
    });

    describe('when category and marks are partially populated', () => {
      describe('when expected progress is less than actual progress', () => {
        it('returns true, indicating the variance is missing', () => {
          const row = planActivityFactory({
            varianceCategory: null,
            varianceRemarks: 'Adverse weather',
            expectedPercentageCompleted: 20,
            percentageCompleted: 40,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(false);
        });
      });

      describe('when expected progress is greater than actual progress', () => {
        it('returns true, indicating the variance is missing', () => {
          const row = planActivityFactory({
            varianceCategory: 'weather_conditions',
            varianceRemarks: null,
            expectedPercentageCompleted: 40,
            percentageCompleted: 20,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(true);
        });
      });

      describe('when expected progress equals actual progress', () => {
        it('returns false, indicating the variance is recorded', () => {
          const row = planActivityFactory({
            varianceCategory: 'weather_conditions',
            varianceRemarks: 'Adverse weather',
            expectedPercentageCompleted: 40,
            percentageCompleted: 40,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(false);
        });
      });
    });

    describe('when category and remarks are not populated', () => {
      describe('when expected progress is less than actual progress', () => {
        it('returns false, indicating the variance is missing', () => {
          const row = planActivityFactory({
            varianceCategory: null,
            varianceRemarks: null,
            expectedPercentageCompleted: 20,
            percentageCompleted: 40,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(false);
        });
      });

      describe('when expected progress is greater than actual progress', () => {
        it('returns true, indicating the variance is missing', () => {
          const row = planActivityFactory({
            varianceCategory: null,
            varianceRemarks: null,
            expectedPercentageCompleted: 40,
            percentageCompleted: 20,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(true);
        });
      });

      describe('when expected progress equals actual progress', () => {
        it('returns false, indicating the variance is recorded', () => {
          const row = planActivityFactory({
            varianceCategory: null,
            varianceRemarks: null,
            expectedPercentageCompleted: 40,
            percentageCompleted: 40,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(false);
        });
      });
    });

    describe('when remarks field consists of whitespace only', () => {
      describe('when expected progress is less than actual progress', () => {
        it('returns true, indicating the variance is missing', () => {
          const row = planActivityFactory({
            varianceCategory: null,
            varianceRemarks: '   ',
            expectedPercentageCompleted: 20,
            percentageCompleted: 40,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(false);
        });
      });

      describe('when expected progress is greater than actual progress', () => {
        it('returns true, indicating the variance is missing', () => {
          const row = planActivityFactory({
            varianceCategory: null,
            varianceRemarks: '    ',
            expectedPercentageCompleted: 40,
            percentageCompleted: 20,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(true);
        });
      });

      describe('when expected progress equals actual progress', () => {
        it('returns false, indicating the variance is recorded', () => {
          const row = planActivityFactory({
            varianceCategory: null,
            varianceRemarks: '    ',
            expectedPercentageCompleted: 40,
            percentageCompleted: 40,
          });

          const actual = isVarianceMissing(row);

          expect(actual).toBe(false);
        });
      });
    });
  });

  describe('getScheduledDaysOptions', () => {
    const weekdayOptions = [
      { label: 'Sun' },
      { label: 'Mon' },
      { label: 'Tue' },
      { label: 'Wed' },
      { label: 'Thu' },
      { label: 'Fri' },
      { label: 'Sat' },
    ];

    describe('when plan starts on Monday and ends on Sunday', () => {
      it('returns an array with correct day labels and date strings', () => {
        const expected = [
          { label: 'Mon', value: '2024-07-08' },
          { label: 'Tue', value: '2024-07-09' },
          { label: 'Wed', value: '2024-07-10' },
          { label: 'Thu', value: '2024-07-11' },
          { label: 'Fri', value: '2024-07-12' },
          { label: 'Sat', value: '2024-07-13' },
          { label: 'Sun', value: '2024-07-14' },
        ];
        const scheduledDaysOptions = getScheduledDaysOptions('2024-07-08', '2024-07-14', weekdayOptions);

        expect(scheduledDaysOptions).toHaveLength(7);
        expect(scheduledDaysOptions).toEqual(expected);
      });

      describe('when plan starts on Tuesday and ends on Saturday', () => {
        it('returns an array with correct day labels and date strings', () => {
          const expected = [
            { label: 'Tue', value: '2024-07-09' },
            { label: 'Wed', value: '2024-07-10' },
            { label: 'Thu', value: '2024-07-11' },
            { label: 'Fri', value: '2024-07-12' },
            { label: 'Sat', value: '2024-07-13' },
          ];
          const scheduledDaysOptions = getScheduledDaysOptions('2024-07-09', '2024-07-13', weekdayOptions);

          expect(scheduledDaysOptions).toHaveLength(5);
          expect(scheduledDaysOptions).toEqual(expected);
        });
      });

      describe('when plan starts on Wednesday and ends on Friday', () => {
        it('returns an array with correct day labels and date strings', () => {
          const expected = [
            { label: 'Wed', value: '2024-07-10' },
            { label: 'Thu', value: '2024-07-11' },
            { label: 'Fri', value: '2024-07-12' },
          ];
          const scheduledDaysOptions = getScheduledDaysOptions('2024-07-10', '2024-07-12', weekdayOptions);

          expect(scheduledDaysOptions).toHaveLength(3);
          expect(scheduledDaysOptions).toEqual(expected);
        });
      });

      describe('when plan starts and ends on Saturday', () => {
        it('returns an array with correct day labels and date strings', () => {
          const expected = [{ label: 'Sat', value: '2024-07-13' }];
          const scheduledDaysOptions = getScheduledDaysOptions('2024-07-13', '2024-07-13', weekdayOptions);

          expect(scheduledDaysOptions).toHaveLength(1);
          expect(scheduledDaysOptions).toEqual(expected);
        });
      });

      describe('when plan starts on Wednesday and ends on next Tuesday', () => {
        it('returns an array with correct day labels and date strings', () => {
          const expected = [
            { label: 'Wed', value: '2024-07-10' },
            { label: 'Thu', value: '2024-07-11' },
            { label: 'Fri', value: '2024-07-12' },
            { label: 'Sat', value: '2024-07-13' },
            { label: 'Sun', value: '2024-07-14' },
            { label: 'Mon', value: '2024-07-15' },
            { label: 'Tue', value: '2024-07-16' },
          ];
          const scheduledDaysOptions = getScheduledDaysOptions('2024-07-10', '2024-07-16', weekdayOptions);

          expect(scheduledDaysOptions).toHaveLength(7);
          expect(scheduledDaysOptions).toEqual(expected);
        });
      });
    });
  });

  describe('getTooltipMessage', () => {
    const messagesMock = () => {
      return jest.fn((key?: string | string[]) => {
        switch (key) {
          case 'checkProgressLog':
            return 'Click to check the progress log for this day.';
          case 'missingProgressLog':
            return 'Missing progress log. Click to add.';
          case 'noPermissions':
            return "You don't have sufficient permissions to add a progress log.";
          case 'noProgressLog':
            return 'No progress log. Click to add.';
          case 'planClosed':
            return 'You cannot add a progress log on a closed work plan.';
          default:
            return '';
        }
      });
    };

    describe('when there is no edit action available', () => {
      describe('when the plan is closed', () => {
        it('returns a correct tooltip message', () => {
          const messages = messagesMock();
          const tooltip = getTooltipMessage(false, true, messages, 'missingProgressLog');

          expect(tooltip).toEqual('You cannot add a progress log on a closed work plan.');
        });
      });

      describe('when the plan is not closed', () => {
        it('returns a correct tooltip message', () => {
          const messages = messagesMock();
          const tooltip = getTooltipMessage(false, false, messages, 'missingProgressLog');

          expect(tooltip).toEqual("You don't have sufficient permissions to add a progress log.");
        });
      });
    });

    describe('when edit action is available', () => {
      describe('when the plan is closed', () => {
        it('returns a correct tooltip message', () => {
          const messages = messagesMock();
          const tooltip = getTooltipMessage(true, true, messages, 'missingProgressLog');

          expect(tooltip).toEqual('You cannot add a progress log on a closed work plan.');
        });
      });

      describe('when the plan is not closed', () => {
        describe('when the progress is missing', () => {
          it('returns a correct tooltip message', () => {
            const messages = messagesMock();
            const tooltip = getTooltipMessage(true, false, messages, 'missingProgressLog');

            expect(tooltip).toEqual('Missing progress log. Click to add.');
          });
        });

        describe('when there is no progress log', () => {
          it('returns a correct tooltip message', () => {
            const messages = messagesMock();
            const tooltip = getTooltipMessage(true, false, messages, 'noProgressLog');

            expect(tooltip).toEqual('No progress log. Click to add.');
          });
        });
      });
    });
  });

  describe('getProgressLogButtonOption', () => {
    const messagesMock = () => {
      return jest.fn((key?: string | string[]) => {
        switch (key) {
          case 'checkProgressLog':
            return 'Click to check the progress log for this day.';
          case 'missingProgressLog':
            return 'Missing progress log. Click to add.';
          case 'noPermissions':
            return "You don't have sufficient permissions to add a progress log.";
          case 'noProgressLog':
            return 'No progress log. Click to add.';
          case 'planClosed':
            return 'You cannot add a progress log on a closed work plan.';
          default:
            return '';
        }
      });
    };

    describe('when edit action is not available', () => {
      describe('when day is scheduled and logged', () => {
        describe('when the plan is not closed', () => {
          it('returns a success button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: false,
                close: false,
                duplicate: false,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
            });
            const planActivity = planActivityFactory({
              scheduledDays: ['2024-07-08'],
              loggedDays: ['2024-07-08'],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'success',
              onClick: expect.any(Function),
              tooltip: 'Click to check the progress log for this day.',
              disabled: false,
              buttonClassnames: 'text-gray-700 w-16',
            });
          });
        });

        describe('when the plan is closed', () => {
          it('returns a success button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: false,
                close: false,
                duplicate: false,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
              status: PLAN_STATUS.CLOSED,
            });
            const planActivity = planActivityFactory({
              scheduledDays: ['2024-07-08'],
              loggedDays: ['2024-07-08'],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'success',
              onClick: expect.any(Function),
              tooltip: 'Click to check the progress log for this day.',
              disabled: false,
              buttonClassnames: 'text-gray-700 w-16',
            });
          });
        });
      });

      describe('when day is scheduled but not logged', () => {
        describe('when the plan is not closed', () => {
          it('returns a disabled warning button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: false,
                close: false,
                duplicate: false,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
            });
            const planActivity = planActivityFactory({
              scheduledDays: ['2024-07-08'],
              loggedDays: [],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'warning',
              onClick: expect.any(Function),
              icon: ExclamationTriangleIcon,
              tooltip: "You don't have sufficient permissions to add a progress log.",
              disabled: true,
              buttonClassnames: 'text-gray-700 w-16',
              iconClassnames: 'text-gray-700',
            });
          });
        });

        describe('when the plan is closed', () => {
          it('returns a disabled warning button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: false,
                close: false,
                duplicate: false,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
              status: PLAN_STATUS.CLOSED,
            });
            const planActivity = planActivityFactory({
              scheduledDays: ['2024-07-08'],
              loggedDays: [],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'warning',
              onClick: expect.any(Function),
              icon: ExclamationTriangleIcon,
              tooltip: 'You cannot add a progress log on a closed work plan.',
              disabled: true,
              buttonClassnames: 'text-gray-700 w-16',
              iconClassnames: 'text-gray-700',
            });
          });
        });
      });

      describe('when day is not scheduled and does not have logs', () => {
        describe('when the plan is not closed', () => {
          it('returns a disabled secondary button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: false,
                close: false,
                duplicate: false,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
            });
            const planActivity = planActivityFactory({
              scheduledDays: [],
              loggedDays: [],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'secondary',
              onClick: expect.any(Function),
              tooltip: "You don't have sufficient permissions to add a progress log.",
              disabled: true,
              buttonClassnames: 'w-16',
            });
          });
        });

        describe('when the plan is closed', () => {
          it('returns a disabled secondary button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: false,
                close: false,
                duplicate: false,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
              status: PLAN_STATUS.CLOSED,
            });
            const planActivity = planActivityFactory({
              scheduledDays: [],
              loggedDays: [],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'secondary',
              onClick: expect.any(Function),
              tooltip: 'You cannot add a progress log on a closed work plan.',
              disabled: true,
              buttonClassnames: 'w-16',
            });
          });
        });
      });

      describe('when day is not scheduled and has logs', () => {
        describe('when the plan is not closed', () => {
          it('returns a success button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: false,
                close: false,
                duplicate: false,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
            });
            const planActivity = planActivityFactory({
              scheduledDays: [],
              loggedDays: ['2024-07-08'],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'success',
              onClick: expect.any(Function),
              tooltip: 'Click to check the progress log for this day.',
              disabled: false,
              buttonClassnames: 'text-gray-700 w-16',
            });
          });
        });

        describe('when the plan is closed', () => {
          it('returns a success button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: false,
                close: false,
                duplicate: false,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
              status: PLAN_STATUS.CLOSED,
            });
            const planActivity = planActivityFactory({
              scheduledDays: [],
              loggedDays: ['2024-07-08'],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'success',
              onClick: expect.any(Function),
              tooltip: 'Click to check the progress log for this day.',
              disabled: false,
              buttonClassnames: 'text-gray-700 w-16',
            });
          });
        });
      });
    });

    describe('when edit action is available', () => {
      describe('when day is scheduled and logged', () => {
        describe('when the plan is not closed', () => {
          it('returns a success button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: true,
                close: true,
                duplicate: true,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
            });
            const planActivity = planActivityFactory({
              scheduledDays: ['2024-07-08'],
              loggedDays: ['2024-07-08'],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'success',
              onClick: expect.any(Function),
              tooltip: 'Click to check the progress log for this day.',
              disabled: false,
              buttonClassnames: 'text-gray-700 w-16',
            });
          });
        });

        describe('when the plan is closed', () => {
          it('returns a success button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: true,
                close: true,
                duplicate: true,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
              status: PLAN_STATUS.CLOSED,
            });
            const planActivity = planActivityFactory({
              scheduledDays: ['2024-07-08'],
              loggedDays: ['2024-07-08'],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'success',
              onClick: expect.any(Function),
              tooltip: 'Click to check the progress log for this day.',
              disabled: false,
              buttonClassnames: 'text-gray-700 w-16',
            });
          });
        });
      });

      describe('when day is scheduled but not logged', () => {
        describe('when the plan is not closed', () => {
          it('returns a warning button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: true,
                close: true,
                duplicate: true,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
            });
            const planActivity = planActivityFactory({
              scheduledDays: ['2024-07-08'],
              loggedDays: [],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'warning',
              onClick: expect.any(Function),
              icon: ExclamationTriangleIcon,
              tooltip: 'Missing progress log. Click to add.',
              disabled: false,
              buttonClassnames: 'text-gray-700 w-16',
              iconClassnames: 'text-gray-700',
            });
          });
        });

        describe('when the plan is closed', () => {
          it('returns a disabled warning button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: true,
                close: true,
                duplicate: true,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
              status: PLAN_STATUS.CLOSED,
            });
            const planActivity = planActivityFactory({
              scheduledDays: ['2024-07-08'],
              loggedDays: [],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'warning',
              onClick: expect.any(Function),
              icon: ExclamationTriangleIcon,
              tooltip: 'You cannot add a progress log on a closed work plan.',
              disabled: true,
              buttonClassnames: 'text-gray-700 w-16',
              iconClassnames: 'text-gray-700',
            });
          });
        });
      });

      describe('when day is not scheduled and does not have logs', () => {
        describe('when the plan is not closed', () => {
          it('returns a secondary button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: true,
                close: true,
                duplicate: true,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
            });
            const planActivity = planActivityFactory({
              scheduledDays: [],
              loggedDays: [],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'secondary',
              onClick: expect.any(Function),
              tooltip: 'No progress log. Click to add.',
              disabled: false,
              buttonClassnames: 'w-16',
            });
          });
        });

        describe('when the plan is closed', () => {
          it('returns a disabled secondary button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: true,
                close: true,
                duplicate: true,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
              status: PLAN_STATUS.CLOSED,
            });
            const planActivity = planActivityFactory({
              scheduledDays: [],
              loggedDays: [],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'secondary',
              onClick: expect.any(Function),
              tooltip: 'You cannot add a progress log on a closed work plan.',
              disabled: true,
              buttonClassnames: 'w-16',
            });
          });
        });
      });

      describe('when day is not scheduled and has logs', () => {
        describe('when the plan is not closed', () => {
          it('returns a success button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: true,
                close: true,
                duplicate: true,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
            });
            const planActivity = planActivityFactory({
              scheduledDays: [],
              loggedDays: ['2024-07-08'],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'success',
              onClick: expect.any(Function),
              tooltip: 'Click to check the progress log for this day.',
              disabled: false,
              buttonClassnames: 'text-gray-700 w-16',
            });
          });
        });

        describe('when the plan is closed', () => {
          it('returns a success button option', () => {
            const messages = messagesMock();
            const openCreateProgressDrawer = jest.fn();
            const openSeeProgressDrawer = jest.fn();
            const day = { label: 'Mon', value: '2024-07-08' };
            const plan = weeklyWorkPlanFactory({
              availableActions: weeklyWorkPlanAvailableActions({
                edit: true,
                close: true,
                duplicate: true,
              }),
              startDate: '2024-07-08',
              endDate: '2024-07-14',
              status: PLAN_STATUS.CLOSED,
            });
            const planActivity = planActivityFactory({
              scheduledDays: [],
              loggedDays: ['2024-07-08'],
            });

            const progressLogButtonOption = getProgressLogButtonOption(
              day,
              plan,
              planActivity,
              messages,
              openCreateProgressDrawer,
              openSeeProgressDrawer
            );

            expect(progressLogButtonOption).toMatchObject({
              id: '2024-07-08-plan-activity-1',
              label: 'Mon',
              color: 'success',
              onClick: expect.any(Function),
              tooltip: 'Click to check the progress log for this day.',
              disabled: false,
              buttonClassnames: 'text-gray-700 w-16',
            });
          });
        });
      });
    });
  });

  describe('variance category transformations', () => {
    const varianceCategoryMessages = (item: string): string => {
      const items: Record<string, string> = {
        approval_delays: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.approval_delays',
        client_changes: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.client_changes',
        delay_in_decision_or_communication:
          'weeklyPlanner.workPlans.lookback.varianceCategory.options.delay_in_decision_or_communication',
        design_information_issues:
          'weeklyPlanner.workPlans.lookback.varianceCategory.options.design_information_issues',
        documentation_issues: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.documentation_issues',
        equipment_unavailability: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.equipment_unavailability',
        inaccurate_estimates: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.inaccurate_estimates',
        inadequate_access_to_workface:
          'weeklyPlanner.workPlans.lookback.varianceCategory.options.inadequate_access_to_workface',
        material_shortage: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.material_shortage',
        miscellaneous: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.miscellaneous',
        personnel_shortage: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.personnel_shortage',
        previous_trade_handover_delay:
          'weeklyPlanner.workPlans.lookback.varianceCategory.options.previous_trade_handover_delay',
        priority_reassessment: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.priority_reassessment',
        process_inefficiencies: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.process_inefficiencies',
        rework_required: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.rework_required',
        safety_concerns_unsafe_conditions:
          'weeklyPlanner.workPlans.lookback.varianceCategory.options.safety_concerns_unsafe_conditions',
        scheduling_conflicts: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.scheduling_conflicts',
        technical_challenges: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.technical_challenges',
        weather_conditions: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.weather_conditions',
      };
      return items[item];
    };

    describe('getVarianceCategories', () => {
      it('prepares an array of variance categories', () => {
        const expected = [
          { label: '', value: '' },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.approval_delays',
            value: 'approval_delays',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.client_changes',
            value: 'client_changes',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.delay_in_decision_or_communication',
            value: 'delay_in_decision_or_communication',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.design_information_issues',
            value: 'design_information_issues',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.documentation_issues',
            value: 'documentation_issues',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.equipment_unavailability',
            value: 'equipment_unavailability',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.inaccurate_estimates',
            value: 'inaccurate_estimates',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.inadequate_access_to_workface',
            value: 'inadequate_access_to_workface',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.material_shortage',
            value: 'material_shortage',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.miscellaneous',
            value: 'miscellaneous',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.personnel_shortage',
            value: 'personnel_shortage',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.previous_trade_handover_delay',
            value: 'previous_trade_handover_delay',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.priority_reassessment',
            value: 'priority_reassessment',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.process_inefficiencies',
            value: 'process_inefficiencies',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.rework_required',
            value: 'rework_required',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.safety_concerns_unsafe_conditions',
            value: 'safety_concerns_unsafe_conditions',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.scheduling_conflicts',
            value: 'scheduling_conflicts',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.technical_challenges',
            value: 'technical_challenges',
          },
          {
            label: 'weeklyPlanner.workPlans.lookback.varianceCategory.options.weather_conditions',
            value: 'weather_conditions',
          },
        ];
        const categories = getVarianceCategories(varianceCategoryMessages as ReturnType<typeof useMessageGetter>);

        expect(categories).toMatchObject(expected);
      });
    });

    describe('getVarianceCategoryLabels', () => {
      it('prepares a map of variance categories with labels', () => {
        const expected = new Map([
          ['approval_delays', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.approval_delays'],
          ['client_changes', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.client_changes'],
          [
            'delay_in_decision_or_communication',
            'weeklyPlanner.workPlans.lookback.varianceCategory.options.delay_in_decision_or_communication',
          ],
          [
            'design_information_issues',
            'weeklyPlanner.workPlans.lookback.varianceCategory.options.design_information_issues',
          ],
          ['documentation_issues', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.documentation_issues'],
          [
            'equipment_unavailability',
            'weeklyPlanner.workPlans.lookback.varianceCategory.options.equipment_unavailability',
          ],
          ['inaccurate_estimates', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.inaccurate_estimates'],
          [
            'inadequate_access_to_workface',
            'weeklyPlanner.workPlans.lookback.varianceCategory.options.inadequate_access_to_workface',
          ],
          ['material_shortage', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.material_shortage'],
          ['miscellaneous', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.miscellaneous'],
          ['personnel_shortage', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.personnel_shortage'],
          [
            'previous_trade_handover_delay',
            'weeklyPlanner.workPlans.lookback.varianceCategory.options.previous_trade_handover_delay',
          ],
          ['priority_reassessment', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.priority_reassessment'],
          [
            'process_inefficiencies',
            'weeklyPlanner.workPlans.lookback.varianceCategory.options.process_inefficiencies',
          ],
          ['rework_required', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.rework_required'],
          [
            'safety_concerns_unsafe_conditions',
            'weeklyPlanner.workPlans.lookback.varianceCategory.options.safety_concerns_unsafe_conditions',
          ],
          ['scheduling_conflicts', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.scheduling_conflicts'],
          ['technical_challenges', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.technical_challenges'],
          ['weather_conditions', 'weeklyPlanner.workPlans.lookback.varianceCategory.options.weather_conditions'],
        ]);

        const mappedCategories = getVarianceCategoryLabels(
          varianceCategoryMessages as ReturnType<typeof useMessageGetter>
        );

        expect(mappedCategories).toMatchObject(expected);
      });
    });
  });
});
