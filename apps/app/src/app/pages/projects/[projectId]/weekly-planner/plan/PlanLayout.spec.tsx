import { projectFactory } from '@shape-construction/api/factories/projects';
import {
  weeklyWorkPlanAvailableActions,
  weeklyWorkPlanFactory,
} from '@shape-construction/api/factories/weeklyWorkPlans';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler } from '@shape-construction/api/handlers-factories/projects/weekly-work-plans';
import * as SidebarContext from '@shape-construction/arch-ui/src/Navigation/Sidebar/Sidebar.context';
import { createMemoryHistory } from 'history';
import React from 'react';
import { server } from 'tests/mock-server';
import { render, screen, userEvent } from 'tests/test-utils';
import { PlanLayout } from './PlanLayout';
import { PLAN_STATUS, PLAN_STATUS_LABELS } from './constants/constants';

describe('<PlanLayout />', () => {
  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('renders the header of the plan', async () => {
    const weeklyWorkPlan = weeklyWorkPlanFactory({ title: 'My weekly work plan' });
    const project = projectFactory({ id: 'project-0' });
    server.use(
      getApiProjectsProjectIdMockHandler(() => project),
      getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
    );
    const history = createMemoryHistory({
      initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}`],
    });
    const route = { path: '/projects/:projectId/weekly-planner/plans/:planId' };
    render(
      <SidebarContext.SidebarProvider defaultOpen={false}>
        <PlanLayout />
      </SidebarContext.SidebarProvider>,
      { history, route, pageData: { project } }
    );

    expect(await screen.findByRole('heading', { name: weeklyWorkPlan.title })).toBeInTheDocument();
    expect(screen.getByText(PLAN_STATUS_LABELS[PLAN_STATUS.TRACKING])).toBeInTheDocument();
    expect(screen.getByText(PLAN_STATUS_LABELS[PLAN_STATUS.TRACKING])).toHaveClass('bg-green-500');
  });

  describe('when plan renders with open side drawer', () => {
    it('closes the side drawer', async () => {
      const mockToggleSidebar = jest.fn();
      jest.spyOn(SidebarContext, 'useSidebar').mockReturnValueOnce({
        ...jest.requireActual('@shape-construction/arch-ui/src/Navigation/Sidebar/Sidebar.context'),
        open: true,
        toggleSidebar: mockToggleSidebar,
      });
      const weeklyWorkPlan = weeklyWorkPlanFactory();
      const project = projectFactory({ id: 'project-0' });
      server.use(
        getApiProjectsProjectIdMockHandler(() => project),
        getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
      );
      const history = createMemoryHistory({
        initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}`],
      });
      const route = { path: '/projects/:projectId/weekly-planner/plans/:planId' };
      render(
        <SidebarContext.SidebarProvider>
          <PlanLayout />
        </SidebarContext.SidebarProvider>,
        { history, route, pageData: { project } }
      );

      expect(await screen.findByText(weeklyWorkPlan.title)).toBeInTheDocument();
      expect(mockToggleSidebar).toHaveBeenCalledTimes(1);
    });
  });

  describe('when back to weekly work plans is clicked', () => {
    describe('when there is no tab on location state', () => {
      it('navigates to plans', async () => {
        const weeklyWorkPlan = weeklyWorkPlanFactory();
        const project = projectFactory({ id: 'project-0' });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}`],
        });
        const route = { path: '/projects/:projectId/weekly-planner/plans/:planId' };
        render(
          <SidebarContext.SidebarProvider>
            <PlanLayout />
          </SidebarContext.SidebarProvider>,
          { history, route, pageData: { project } }
        );

        await userEvent.click(await screen.findByRole('button', { name: 'navigation.back' }));

        expect(history.location.pathname).toBe('/projects/project-0/weekly-planner/plans');
      });
    });

    describe('when there is a tab on location state', () => {
      it('navigates to tab', async () => {
        const weeklyWorkPlan = weeklyWorkPlanFactory();
        const project = projectFactory({ id: 'project-0' });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
        );
        const history = createMemoryHistory({
          initialEntries: [
            {
              pathname: `/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}`,
              state: {
                tab: 'all',
              },
            },
          ],
        });
        const route = { path: '/projects/:projectId/weekly-planner/plans/:planId' };
        render(
          <SidebarContext.SidebarProvider>
            <PlanLayout />
          </SidebarContext.SidebarProvider>,
          { history, route, pageData: { project } }
        );

        await userEvent.click(await screen.findByRole('button', { name: 'navigation.back' }));

        expect(history.location.pathname).toBe('/projects/project-0/weekly-planner/plans/lists/all');
      });
    });
  });

  describe('when plan is archived', () => {
    describe('when user can restore plan', () => {
      it('renders the archived message', async () => {
        const weeklyWorkPlan = weeklyWorkPlanFactory({
          archived: true,
          availableActions: weeklyWorkPlanAvailableActions({ restore: true }),
        });
        const project = projectFactory({ id: 'project-0' });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansPlanIdMockHandler(() => weeklyWorkPlan)
        );
        const history = createMemoryHistory({
          initialEntries: [`/projects/${project.id}/weekly-planner/plans/${weeklyWorkPlan.id}`],
        });
        const route = { path: '/projects/:projectId/weekly-planner/plans/:planId' };

        render(
          <SidebarContext.SidebarProvider>
            <PlanLayout />
          </SidebarContext.SidebarProvider>,
          { history, route, pageData: { project } }
        );

        expect(await screen.findByText('weeklyPlanner.workPlans.details.archivedPlan.description')).toBeInTheDocument();
        expect(
          screen.getByRole('button', { name: 'weeklyPlanner.workPlans.details.archivedPlan.restoreCTA' })
        ).toBeInTheDocument();
      });
    });
  });
});
