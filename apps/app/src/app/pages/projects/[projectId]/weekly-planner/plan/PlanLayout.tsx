import { useMessage, useMessageGetter } from '@messageformat/react';
import type { WeeklyWorkPlanSchema } from '@shape-construction/api/src/types';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Tabs } from '@shape-construction/arch-ui';
import { useSidebar } from '@shape-construction/arch-ui/src/Navigation/Sidebar';
import { showSuccessToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { breakpoints } from '@shape-construction/arch-ui/src/utils/breakpoints';
import { useFeatureFlag } from '@shape-construction/feature-flags';
import { useMediaQuery } from '@shape-construction/hooks';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { useCurrentProject } from 'app/contexts/currentProject';
import { CurrentWeeklyWorkPlanProvider, useCurrentWeeklyWorkPlan } from 'app/contexts/currentWeeklyWorkPlan';
import { useRestoreWeeklyWorkPlan, useWeeklyWorkPlan } from 'app/queries/weeklyWorkPlans/weeklyWorkPlans';
import { useEffect, useMemo, useState } from 'react';
import { Link, Outlet, useLocation, useNavigate, useParams } from 'react-router';
import { DuplicatePlan, type DuplicatePlanProps } from '../components/DuplicatePlan/DuplicatePlan';
import { EditPlan } from '../components/EditPlan/EditPlan';
import { PlanLayoutActions } from './PlanLayoutActions';
import { ExportConfirmationModal } from './components/ExportConfirmationModal';
import type { ExportPlanOption } from './components/ExportPlanLookback/types';
import { PlanHeaderTitle } from './components/PlanHeaderTitle';
import { ProgressDrawer } from './components/progress/ProgressDrawer';
import { PLAN_STATUS } from './constants/constants';
import { useProgressDrawerState } from './hooks/useProgressDrawerState';

const PlanLayoutUI = () => {
  const { value: isStreamliningProgressLogs } = useFeatureFlag('streamlining-progress-logs');
  const isSmallScreen = useMediaQuery(breakpoints.up('sm'));
  const backNavigationTitle = useMessage('navigation.back', {
    route: useMessage('weeklyPlanner.workPlans.title'),
  });
  const project = useCurrentProject();
  const plan = useCurrentWeeklyWorkPlan();
  const messages = useMessageGetter('weeklyPlanner.workPlans.details');
  const { open, toggleSidebar } = useSidebar();

  const [openPlanModal, setOpenPlanModal] = useState(false);
  const [openDuplicatePlanModal, setOpenDuplicatePlanModal] = useState(false);
  const [openExportModal, setOpenExportModal] = useState(false);
  const [exportOption, setExportOption] = useState<ExportPlanOption>('lookback');
  const { drawerOpen, drawerPlanActivity, drawerDefaultDate, openDrawer, closeDrawer } = useProgressDrawerState();

  const navigate = useNavigate();
  const location = useLocation();
  const previousLocationTab = location.state?.tab;

  const tabs = useMemo(() => {
    return [
      {
        enabled: true,
        label: isStreamliningProgressLogs ? messages('tabs.plan') : messages('tabs.editor'),
        path: `/projects/${project.id}/weekly-planner/plans/${plan.id}`,
      },
      {
        enabled: !isStreamliningProgressLogs,
        label: messages('tabs.progressTracker'),
        path: `/projects/${project.id}/weekly-planner/plans/${plan.id}/progress-logs`,
      },
      {
        enabled: true,
        label: isStreamliningProgressLogs ? messages('tabs.progressAndLookback') : messages('tabs.lookback'),
        path: `/projects/${project.id}/weekly-planner/plans/${plan.id}/lookback`,
      },
    ];
  }, [project.id, plan.id, messages]);

  const isProgressLogsTab =
    location.pathname === `/projects/${project.id}/weekly-planner/plans/${plan.id}/progress-logs`;
  const showNewProgressLogButton = isProgressLogsTab && plan?.status === PLAN_STATUS.TRACKING;

  const handleBack = () => {
    if (previousLocationTab) {
      navigate(`/projects/${project.id}/weekly-planner/plans/lists/${previousLocationTab}`);
    } else {
      navigate(`/projects/${project.id}/weekly-planner/plans`);
    }
  };

  const handleDuplicateSuccess: DuplicatePlanProps['onDuplicateSuccess'] = (newPlan) => {
    navigate(`/projects/${project.id}/weekly-planner/plans/${newPlan.id}`, {
      state: { tab: previousLocationTab },
    });
  };

  const { mutate: restoreWeeklyWorkPlan } = useRestoreWeeklyWorkPlan();
  const onRestore = () => {
    restoreWeeklyWorkPlan(
      {
        projectId: project.id,
        weeklyWorkPlanId: plan.id,
      },
      {
        onSuccess: () => {
          showSuccessToast({
            message: messages('archivedPlan.toast.success', {
              planName: plan.title,
            }),
          });
        },
      }
    );
  };

  useEffect(() => {
    if (open) toggleSidebar();
  }, []);

  return (
    <Page>
      {plan.archived && plan?.availableActions.restore && (
        <Alert color="warning" rounded={false} displayInRow justifyContent="center">
          <Alert.Message>{messages('archivedPlan.description')}</Alert.Message>
          <Alert.Actions>
            <Button color="warning" variant="contained" size="xs" onClick={onRestore}>
              {messages('archivedPlan.restoreCTA')}
            </Button>
          </Alert.Actions>
        </Alert>
      )}
      <Page.Header
        className="bg-white pb-0 md:pb-0 shadow-none"
        backNavigationTitle={backNavigationTitle}
        hasBackNavigation
        onBackNavigation={handleBack}
        title={plan.title}
        titleAs={() => (isSmallScreen ? <PlanHeaderTitle plan={plan} /> : null)}
        rightSection={
          <PlanLayoutActions
            plan={plan}
            projectId={project.id}
            openPlanModal={() => setOpenPlanModal(true)}
            openDuplicatePlanModal={() => setOpenDuplicatePlanModal(true)}
            showNewProgressLogButton={showNewProgressLogButton}
            openDrawer={openDrawer}
            openExportModal={(option) => {
              setExportOption(option);
              setOpenExportModal(true);
            }}
          />
        }
      />
      <Tabs selectedValue={location.pathname}>
        {tabs.map(({ enabled, label, path }) => {
          if (!enabled) return null;
          return (
            <Link key={label} to={path} state={location.state} replace>
              <Tabs.Tab selected={path === location.pathname}>{label}</Tabs.Tab>
            </Link>
          );
        })}
      </Tabs>

      <Outlet />

      <EditPlan
        initialValues={{
          title: plan.title,
          start_date: plan.startDate,
          end_date: plan.endDate,
        }}
        planId={plan.id}
        open={openPlanModal}
        onClose={() => setOpenPlanModal(false)}
      />
      <DuplicatePlan
        initialValues={{
          title: `Copy of ${plan.title}`,
          start_date: plan.endDate,
        }}
        planId={plan.id}
        open={openDuplicatePlanModal}
        onClose={() => setOpenDuplicatePlanModal(false)}
        onDuplicateSuccess={handleDuplicateSuccess}
      />
      <ExportConfirmationModal
        planId={plan.id}
        exportOption={exportOption}
        open={openExportModal}
        onClose={() => setOpenExportModal(false)}
      />
      {isProgressLogsTab && (
        <ProgressDrawer
          open={drawerOpen}
          planActivity={drawerPlanActivity}
          defaultDate={drawerDefaultDate}
          onClose={() => closeDrawer()}
        />
      )}
    </Page>
  );
};

export const PlanLayout = () => {
  const { planId } = useParams() as { planId: WeeklyWorkPlanSchema['id'] };
  const project = useCurrentProject();
  const { data: plan, isLoading } = useWeeklyWorkPlan(project.id, planId);

  // TODO: Error state for wwp
  if (isLoading || !plan) return <LoadingSpinner variant="screen" />;

  return (
    <CurrentWeeklyWorkPlanProvider weeklyWorkPlan={plan}>
      <PlanLayoutUI />
    </CurrentWeeklyWorkPlanProvider>
  );
};

export { PlanLayout as Component };
