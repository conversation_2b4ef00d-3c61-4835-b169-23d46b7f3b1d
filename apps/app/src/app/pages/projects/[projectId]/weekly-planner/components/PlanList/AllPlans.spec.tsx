import { projectAvailableActions, projectFactory } from '@shape-construction/api/factories/projects';
import { sharedCursorPaginationMetaFactory } from '@shape-construction/api/factories/sharedCursorPagination';
import { teamMemberFactory } from '@shape-construction/api/factories/team-member';
import { userBasicDetailsFactory, userFactory } from '@shape-construction/api/factories/users';
import { weeklyWorkPlanFactory, weeklyWorkPlanIndexFactory } from '@shape-construction/api/factories/weeklyWorkPlans';
import { getApiProjectsProjectIdMockHandler } from '@shape-construction/api/handlers-factories/projects';
import { getApiProjectsProjectIdPeopleMockHandler } from '@shape-construction/api/handlers-factories/projects/team-members';
import { getApiProjectsProjectIdWeeklyWorkPlansMockHandler } from '@shape-construction/api/handlers-factories/projects/weekly-work-plans';
import React from 'react';
import { server, waitForRequest } from 'tests/mock-server';
import { render, screen, userEvent, waitFor } from 'tests/test-utils';
import { AllPlans } from './AllPlans';

describe('<AllPlans />', () => {
  it('renders plan list with all plans', async () => {
    const userBasicDetails = userBasicDetailsFactory({ id: '123' });
    const user = userFactory(userBasicDetails);
    const currentTeamMember = teamMemberFactory({ id: 1, user: userBasicDetails });
    server.use(
      getApiProjectsProjectIdPeopleMockHandler(() => [currentTeamMember]),
      getApiProjectsProjectIdWeeklyWorkPlansMockHandler(({ request }) => {
        if (new URL(request.url).searchParams.get('author_id')) return weeklyWorkPlanIndexFactory();
        return weeklyWorkPlanIndexFactory({
          entries: [
            weeklyWorkPlanFactory({
              id: '31',
              title: 'Plan 1',
              teamMemberId: currentTeamMember.id,
            }),
            weeklyWorkPlanFactory({ id: '41', title: 'Plan 2', teamMemberId: 5 }),
            weeklyWorkPlanFactory({ id: '59', title: 'Plan 3', teamMemberId: 6 }),
          ],
        });
      }),
      getApiProjectsProjectIdWeeklyWorkPlansMockHandler(({ request }) => {
        if (new URL(request.url).searchParams.get('author_id')) return weeklyWorkPlanIndexFactory();
        return weeklyWorkPlanIndexFactory({
          entries: [
            weeklyWorkPlanFactory({
              id: '31',
              title: 'Plan 1',
              teamMemberId: currentTeamMember.id,
            }),
            weeklyWorkPlanFactory({ id: '41', title: 'Plan 2', teamMemberId: 5 }),
            weeklyWorkPlanFactory({ id: '59', title: 'Plan 3', teamMemberId: 6 }),
          ],
        });
      }),
      getApiProjectsProjectIdWeeklyWorkPlansMockHandler(({ request }) => {
        if (new URL(request.url).searchParams.get('author_id')) return weeklyWorkPlanIndexFactory();
        return weeklyWorkPlanIndexFactory({
          entries: [
            weeklyWorkPlanFactory({
              id: '31',
              title: 'Plan 1',
              teamMemberId: currentTeamMember.id,
            }),
            weeklyWorkPlanFactory({ id: '41', title: 'Plan 2', teamMemberId: 5 }),
            weeklyWorkPlanFactory({ id: '59', title: 'Plan 3', teamMemberId: 6 }),
          ],
        });
      })
    );

    render(<AllPlans />, { user });

    expect(await screen.findAllByRole('row')).toHaveLength(5); // header + 3 plans + footer
  });

  describe('when there are no plans', () => {
    describe('when user has create WWP permissions', () => {
      it('renders empty state with the new plan button', async () => {
        const user = userFactory();
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createWeeklyWorkPlan: true,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansMockHandler(() => weeklyWorkPlanIndexFactory())
        );

        render(<AllPlans />, { user, pageData: { project } });

        expect(screen.queryAllByRole('row')).toHaveLength(0);
        expect(await screen.findByText('weeklyPlanner.workPlans.table.emptyStates.all.title')).toBeInTheDocument();
        expect(
          await screen.findByText('weeklyPlanner.workPlans.table.emptyStates.all.description')
        ).toBeInTheDocument();
        expect(
          await screen.findByRole('button', {
            name: 'weeklyPlanner.workPlans.table.emptyStates.all.action',
          })
        ).toBeInTheDocument();
      });
    });

    describe('when user does not have create WWP permissions', () => {
      it('renders empty state without the new plan button', async () => {
        const user = userFactory();
        const project = projectFactory({
          id: 'project-0',
          availableActions: projectAvailableActions({
            createWeeklyWorkPlan: false,
          }),
        });
        server.use(
          getApiProjectsProjectIdMockHandler(() => project),
          getApiProjectsProjectIdWeeklyWorkPlansMockHandler(() => weeklyWorkPlanIndexFactory())
        );

        render(<AllPlans />, { user, pageData: { project } });

        const newPlanPromise = waitFor(() => {
          expect(
            screen.queryByRole('button', { name: 'weeklyPlanner.workPlans.table.emptyStates.all.action' })
          ).toBeInTheDocument();
        });
        await expect(newPlanPromise).rejects.toThrow();
        expect(await screen.findByText('weeklyPlanner.workPlans.table.emptyStates.all.title')).toBeInTheDocument();
        expect(screen.queryByText('weeklyPlanner.workPlans.table.emptyStates.all.description')).not.toBeInTheDocument();
        expect(screen.queryAllByRole('row')).toHaveLength(0);
      });
    });
  });

  describe('pagination', () => {
    describe('when next button is pressed', () => {
      it('calls api with "after" cursor', async () => {
        const userBasicDetails = userBasicDetailsFactory({ id: '123' });
        const user = userFactory(userBasicDetails);
        const currentTeamMember = teamMemberFactory({ id: 1, user: userBasicDetails });
        server.use(
          getApiProjectsProjectIdPeopleMockHandler(() => [currentTeamMember]),
          getApiProjectsProjectIdWeeklyWorkPlansMockHandler(() =>
            weeklyWorkPlanIndexFactory({
              entries: [weeklyWorkPlanFactory()],
              meta: sharedCursorPaginationMetaFactory({
                hasNextPage: true,
                lastEntryCursor: 'lastEntryCursor',
              }),
            })
          )
        );
        const weeklyWorkPlansRequest = waitForRequest('get', '/api/projects/project-0/weekly_work_plans', (req) =>
          Boolean(new URL(req.url).searchParams.get('after'))
        );

        render(<AllPlans />, { user });
        await userEvent.click(await screen.findByRole('button', { name: 'table.cursorPagination.next' }));

        const req = await weeklyWorkPlansRequest;
        expect(new URL(req.url).searchParams.get('after')).toBe('lastEntryCursor');
      });
    });

    describe('when prev button is pressed', () => {
      it('calls api with "before" cursor', async () => {
        const userBasicDetails = userBasicDetailsFactory({ id: '123' });
        const user = userFactory(userBasicDetails);
        const currentTeamMember = teamMemberFactory({ id: 1, user: userBasicDetails });
        server.use(
          getApiProjectsProjectIdPeopleMockHandler(() => [currentTeamMember]),
          getApiProjectsProjectIdWeeklyWorkPlansMockHandler(() =>
            weeklyWorkPlanIndexFactory({
              entries: [weeklyWorkPlanFactory()],
              meta: sharedCursorPaginationMetaFactory({
                hasPreviousPage: true,
                firstEntryCursor: 'firstEntryCursor',
              }),
            })
          )
        );
        const weeklyWorkPlansRequest = waitForRequest('get', '/api/projects/project-0/weekly_work_plans', (req) =>
          Boolean(new URL(req.url).searchParams.get('before'))
        );

        render(<AllPlans />, { user });
        await userEvent.click(await screen.findByRole('button', { name: 'table.cursorPagination.previous' }));

        const req = await weeklyWorkPlansRequest;
        expect(new URL(req.url).searchParams.get('before')).toBe('firstEntryCursor');
      });
    });
  });
});
