import { getApiProjectsProjectIdQueryOptions } from '@shape-construction/api/src/hooks';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { useQuery } from '@tanstack/react-query';
import { LoadingSpinner } from 'app/components/Loading/Loading';
import { CurrentProjectProvider, useCurrentProject } from 'app/contexts/currentProject';
import { ProjectError } from 'app/pages/projects/[projectId]/ProjectError';
import { useProjectPerson } from 'app/queries/projects/people';
import { useProjectDefaultMutation } from 'app/queries/projects/projects';
import { useCurrentUser } from 'app/queries/users/users';
import React, { useEffect } from 'react';
import { Outlet, useParams } from 'react-router';

export const ProjectLayout: React.FC = () => {
  const { projectId } = useParams<{ projectId: ProjectSchema['id'] }>();
  const { data: project, isError, error, isLoading } = useQuery(getApiProjectsProjectIdQueryOptions(projectId!));
  /**
   * This is being called here to ensure that the current project person is fetched
   * before creating an issue, this is especially important on offline mode
   */
  useProjectPerson(project?.id!, project?.currentTeamMemberId!, {
    query: {
      enabled: !!project,
    },
  });

  if (isError) return <ProjectError error={error} />;
  if (isLoading || !project) return <LoadingSpinner variant="screen" />;

  return (
    <CurrentProjectProvider project={project}>
      <DefaultProjectUpdater />
      <Outlet />
    </CurrentProjectProvider>
  );
};

export { ProjectLayout as Component };

const DefaultProjectUpdater = () => {
  const { defaultProject } = useCurrentUser();
  const project = useCurrentProject();
  const { mutate: setDefaultProject } = useProjectDefaultMutation();

  useEffect(() => {
    if (project.id !== defaultProject && project.sampleProject === false) {
      setDefaultProject({ projectId: project.id });
    }
  }, [setDefaultProject, defaultProject, project]);

  return null;
};
