import { getApiProjectsProjectIdQueryOptions } from '@shape-construction/api/src/hooks';
import type { ProjectSchema } from '@shape-construction/api/src/types';
import { useSuspenseQuery } from '@tanstack/react-query';
import { CurrentProjectProvider } from 'app/contexts/currentProject';
import { Outlet, useParams } from 'react-router';

export const ModalProjectLayout: React.FC = () => {
  const { projectId } = useParams<{ projectId: ProjectSchema['id'] }>();
  const { data: project } = useSuspenseQuery(getApiProjectsProjectIdQueryOptions(projectId!));

  return (
    <CurrentProjectProvider project={project}>
      <Outlet />
    </CurrentProjectProvider>
  );
};

export { ModalProjectLayout as Component };
