/* eslint-disable testing-library/no-node-access */
import { renderHook, waitFor } from 'tests/test-utils';
import { useUseberry } from './useUseberry';

jest.mock('app/config/environment', () => ({
  environment: {
    USEBERRY: true,
  },
}));
jest.mock('app/analytics/hooks/useCookieConsent');
jest.mock('app/queries/users/users');

const mockUseCookieConsent = require('./useCookieConsent').useCookieConsent;
const mockIsUserAuthenticated = require('app/queries/users/users').useIsUserAuthenticated;

describe('useUseberry', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    document.body.innerHTML = '';
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('when user is authenticated', () => {
    describe('when consent for statistics is true', () => {
      it('loads the Useberry script', async () => {
        mockIsUserAuthenticated.mockReturnValue(true);
        mockUseCookieConsent.mockReturnValue({
          consentData: {
            statistics: true,
          },
        });

        const { result } = renderHook(useUseberry);

        await waitFor(() => {
          expect(result.current.isInitialized).toBe(true);
        });
        expect(
          document.querySelector(
            'script[src="https://api.useberry.com/integrations/liveUrl/scripts/useberryScript.js"]'
          )
        ).not.toBeNull();
      });
    });

    describe('when consent for statistics is false', () => {
      it('does not load the Useberry script', async () => {
        mockIsUserAuthenticated.mockReturnValue(true);
        mockUseCookieConsent.mockReturnValue({
          consentData: {
            statistics: false,
          },
        });

        const { result } = renderHook(useUseberry);

        await waitFor(() => {
          expect(result.current.isInitialized).toBe(false);
        });
        expect(
          document.querySelector(
            'script[src="https://api.useberry.com/integrations/liveUrl/scripts/useberryScript.js"]'
          )
        ).toBeNull();
      });
    });
  });

  describe('when user is not authenticated', () => {
    it('does not load the Useberry script', async () => {
      mockIsUserAuthenticated.mockReturnValue(false);
      mockUseCookieConsent.mockReturnValue({
        consentData: {
          statistics: true,
        },
      });

      const { result } = renderHook(useUseberry);

      await waitFor(() => {
        expect(result.current.isInitialized).toBe(false);
      });
      expect(
        document.querySelector('script[src="https://api.useberry.com/integrations/liveUrl/scripts/useberryScript.js"]')
      ).toBeNull();
    });
  });
});
/* eslint-enable testing-library/no-node-access */
