import { renderHook, waitFor } from 'tests/test-utils';
import { useHotjar } from './useHotjar';

jest.mock('app/config/environment', () => ({
  environment: {
    HOTJAR_SITE_ID: '987654321',
  },
}));
jest.mock('app/analytics/hooks/useCookieConsent');
jest.mock('app/queries/users/users');

const mockUseCookieConsent = require('./useCookieConsent').useCookieConsent;
const mockIsUserAuthenticated = require('app/queries/users/users').useIsUserAuthenticated;

describe('useHotjar', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('when user is authenticated', () => {
    describe('when consent for statistics is true', () => {
      it('initializes Hotjar', async () => {
        mockIsUserAuthenticated.mockReturnValue(true);
        mockUseCookieConsent.mockReturnValue({
          consentData: {
            statistics: true,
          },
        });

        const { result } = renderHook(useHotjar);

        await waitFor(() => {
          expect(result.current.isInitialized).toBe(true);
        });
      });
    });

    describe('when consent for statistics is false', () => {
      it('does not initialize Hotjar', async () => {
        mockIsUserAuthenticated.mockReturnValue(true);
        mockUseCookieConsent.mockReturnValue({
          consentData: {
            statistics: false,
          },
        });

        const { result } = renderHook(useHotjar);

        await waitFor(() => {
          expect(result.current.isInitialized).toBe(false);
        });
      });
    });
  });

  describe('when user is not authenticated', () => {
    it('does not initialize Hotjar', async () => {
      mockIsUserAuthenticated.mockReturnValue(false);
      mockUseCookieConsent.mockReturnValue({
        consentData: {
          statistics: true,
        },
      });

      const { result } = renderHook(useHotjar);

      await waitFor(() => {
        expect(result.current.isInitialized).toBe(false);
      });
    });
  });
});
