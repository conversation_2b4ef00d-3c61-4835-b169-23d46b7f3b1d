import { getApiUsersMeQueryOptions } from '@shape-construction/api/src/hooks';
import { showErrorToast } from '@shape-construction/arch-ui/src/Toast/toasts';
import { dedupePromise } from '@shape-construction/utils/promises/dedupe';
import type { ResponseError } from 'app/axios';
import { environment } from 'app/config/environment';
import type { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import axios from 'axios';
import { getRefreshToken, getToken, setRefreshToken, setToken } from './authorization';
import queryClient from './queries/query.client.builder';

const getConsentData = () => window?.CookieConsent?.consent || null;
const cookieConsentHeaderContent = ({ statistics }: Consent) => JSON.stringify({ statistics });

export const setupAuthorizationHeader = (request: InternalAxiosRequestConfig) => {
  const config = { ...request };
  config.headers = config.headers ?? {};
  config.headers['X-Shape-User-Agent'] = `ShapeApp/${environment.VERSION}`; // eslint-disable-line no-param-reassign

  const authorization = getToken();
  if (authorization) {
    config.headers.Authorization = authorization;
  }

  const consentData = getConsentData();
  if (consentData) {
    config.headers['Cookie-Consent'] = cookieConsentHeaderContent(consentData);
  }

  return config;
};

export const preserveAuthorizationHeaders = (response: AxiosResponse) => {
  const authorization = response.headers.authorization;
  if (authorization) {
    setToken(authorization);
  }

  const refreshToken = response.headers['authorization-refresh'];
  if (refreshToken) {
    setRefreshToken(refreshToken);
  }
};

export const onMaintenanceMode = () => {
  window.location.assign('/maintenance.html');
};

export const onMutateError = (error: ResponseError) => {
  const errorMessage = error.response?.data?.errorDescription ?? error?.message ?? 'Something went wrong';
  showErrorToast({ id: errorMessage, message: errorMessage });
};

export const refreshAuthToken = dedupePromise(async () => {
  const refreshToken = getRefreshToken();
  if (!refreshToken) throw new Error('No refresh token found');

  const response = await axios('/api/login/refresh', {
    method: 'POST',
    headers: {
      'Authorization-Refresh': refreshToken,
      'X-Shape-User-Agent': `ShapeApp/${environment.VERSION}`,
    },
    baseURL: environment.API_URL,
  });

  preserveAuthorizationHeaders(response);

  return response;
});

export const onNotAcceptedEndUserAgreement = () => {
  const user = queryClient.getQueryData(getApiUsersMeQueryOptions().queryKey);
  if (user) {
    queryClient.setQueryData(getApiUsersMeQueryOptions().queryKey, {
      ...user,
      pendingEuaAcceptance: true,
    });
  }
};
