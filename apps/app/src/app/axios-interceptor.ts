import axiosInstance, { type RequestInterceptors, type ResponseInterceptors } from 'app/axios';
import { type AxiosInstance, type InternalAxiosRequestConfig, type Method, isAxiosError } from 'axios';
import { getToken, isTokenExpired } from './authorization';
import {
  onMaintenanceMode,
  onMutateError,
  onNotAcceptedEndUserAgreement,
  preserveAuthorizationHeaders,
  refreshAuthToken,
  setupAuthorizationHeader,
} from './axios-interceptor-handlers';
import { endUnAuthorizedSession } from './session';

export const onRequest: RequestInterceptors['onRequest'] = async (config) => {
  const request = setupAuthorizationHeader(config);
  const token = getToken();

  if (token && isTokenExpired(token)) {
    try {
      const response = await refreshAuthToken();
      request.headers.Authorization = response.headers.authorization;
      return request;
    } catch (error) {
      if (isAxiosError(error) && error.response?.status === 401) endUnAuthorizedSession();
      return Promise.reject(error);
    }
  }

  return request;
};

export const onResponse: ResponseInterceptors['onResponse'] = (response) => {
  preserveAuthorizationHeaders(response);

  return response;
};

export interface RefreshableAxiosRequestConfig extends InternalAxiosRequestConfig<any> {
  _hasRefreshedToken?: boolean;
}
const mutateMethods: Method[] = ['patch', 'post', 'put', 'delete'];
export const onResponseError: ResponseInterceptors['onResponseError'] = async (error) => {
  const method = error.config?.method as Method | undefined;
  const isMutating = method && mutateMethods.includes(method);
  const originalRequest = error.config as RefreshableAxiosRequestConfig;

  if (!originalRequest || originalRequest._hasRefreshedToken) {
    return Promise.reject(error);
  }

  if (error.response) {
    const status = error.response.status;
    const errorCode = error.response.data?.errorCode;
    const authorization = getToken();

    if (status === 401 && authorization) {
      try {
        const response = await refreshAuthToken();
        // Retry the request with the new token.
        // This prevents backoff from react-query on subsequent request failures.
        const newToken = response.headers.authorization;
        originalRequest.headers = originalRequest.headers ?? {};
        originalRequest.headers.Authorization = newToken;
        originalRequest._hasRefreshedToken = true;

        return axiosInstance(originalRequest);
      } catch (e) {
        if (isAxiosError(error) && error.response?.status === 401) endUnAuthorizedSession();
        return Promise.reject(error);
      }
    }

    if (status === 403) {
      if (errorCode === 'latest_end_user_agreement_not_accepted') {
        onNotAcceptedEndUserAgreement();
      }
    }

    if (status === 503) {
      /**
       * Using Promise.resolve to bypass the channels service unavailable error because the request is being made through a POST and it
       * is going to be show a toast through the "if (isMutating) onMutateError(error);"
       * */
      if (errorCode === 'channels_service_unavailable') return Promise.resolve(error);

      if (errorCode === 'maintenance') onMaintenanceMode();
    }
  }

  if (isMutating) onMutateError(error);

  return Promise.reject(error);
};

export const setupInterceptors = (instance: AxiosInstance) => {
  instance.interceptors.request.use(onRequest, undefined);
  instance.interceptors.response.use(onResponse, onResponseError);
};
